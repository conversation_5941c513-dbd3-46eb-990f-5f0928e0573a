package com.altomni.apn.report.repository.v2;

import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.vo.recruiting.v2.KpiReportCreatedVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiApplicationCountVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiNoteCountVO;
import com.altomni.apn.common.vo.recruiting.v2.ReserveInterviewVO;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.Record;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.altomni.apn.report.repository.v2.Constants.*;
import static com.altomni.apn.report.repository.v2.RecruitingKpiConditionBuilder.*;
import static org.jooq.impl.DSL.*;

@Slf4j
@Service
public class RecruitingKpiUserRepositoryV2 {

    private final QueryExecutor queryExecutor;

    public RecruitingKpiUserRepositoryV2(QueryExecutor queryExecutor) {
        this.queryExecutor = queryExecutor;
    }

    /**
     * 流程相关 kpi 指标
     */
    public List<RecruitingKpiApplicationCountVO> searchApplicationKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        RecruitingKpiDateType dateType = searchDto.getDateType();
        Field<String> dateField = RecruitingKpiDateType.ADD.equals(dateType) ? ADD_DATE : EVENT_DATE;
        Supplier<Condition> dataPermissionCondition = () -> buildApplicationCondition(searchDto, teamDTO);
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, dateField, VIEW_APPLICATION_API, APPLICATION_METRICS, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, RecruitingKpiApplicationCountVO.class, "searchApplicationKpiData");
    }


    /**
     * 预约面试相关 kpi 指标, 预约面试指标不受事件时间\操作时间影响，统一都是按照操作时间
     */
    public List<ReserveInterviewVO> searchReserveInterviewKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Supplier<Condition> dataPermissionCondition = () -> buildApplicationCondition(searchDto, teamDTO);
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, ADD_DATE, VIEW_APPLICATION_API, RESERVE_INTERVIEW_METRICS, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, ReserveInterviewVO.class, "searchReserveInterviewKpiData");
    }


    /**
     * 备注相关 kpi 指标
     */
    public List<RecruitingKpiNoteCountVO> searchNoteKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Supplier<Condition> dataPermissionCondition = () -> reportDataPermissionCondition(searchDto, teamDTO);
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, ADD_DATE, VIEW_NOTES_KPI, NOTE_METRICS, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, RecruitingKpiNoteCountVO.class, "searchNoteKpiData");
    }

    /**
     * 创建公司、职位、候选人相关 kpi 指标
     */
    public List<KpiReportCreatedVO> searchCreatedKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        List<Field<?>> metrics = List.of(
                bitmap_union_count(field("created_company_count", Integer.class)).as("createdCompanyCount"),
                bitmap_union_count(field("upgrade_company_count", Integer.class)).as("upgradeCompanyCount"),
                bitmap_union_count(field("talen_ids", Integer.class)).as("createdTalentCount"),
                sum(field("openings", Integer.class)).as("openings"));

        Supplier<Condition> dataPermissionCondition = () -> reportDataPermissionCondition(searchDto, teamDTO);
        SelectHavingStep<Record> query = buildKpiQuery(searchDto, ADD_DATE, VIEW_CREATED__KPI, metrics, dataPermissionCondition);
        return queryExecutor.executeKpiQuery(query, KpiReportCreatedVO.class, "searchCreatedDataMapKpiData");
    }


    /**
     * 通用的 KPI 查询构建方法
     */
    private SelectHavingStep<Record> buildKpiQuery(RecruitingKpiReportSearchDto searchDto,
                                                   Field<String> dateField,
                                                   Table<?> fromTable,
                                                   List<Field<?>> metrics,
                                                   Supplier<Condition> dataPermissionCondition) {
        String timezone = searchDto.getTimezone();
        List<Field<?>> dimensions = buildDimensions(searchDto, dateField, timezone);

        // 构建基础条件
        Condition condition = buildBaseCondition(searchDto, dateField);
        // 构建数据权限条件
        Condition dataPermission = dataPermissionCondition.get();
        // 最终查询条件
        Condition finalCondition = condition.and(dataPermission);

        // 构建查询
        SelectJoinStep<Record> query = dsl.select(Stream.concat(dimensions.stream(), metrics.stream()).toList())
                .from(fromTable);

        return query.where(finalCondition).groupBy(dimensions);
    }

    /**
     * 构建基础查询条件
     */
    private Condition buildBaseCondition(RecruitingKpiReportSearchDto searchDto, Field<String> dateField) {
        Condition condition = TENANT_ID.eq(searchDto.getSearchTenantId())
                .and(dateField.between(getUtcByTimeZone(searchDto.getStartDate() + " 00:00:00", searchDto.getTimezone()),
                        getUtcByTimeZone(searchDto.getEndDate() + " 23:59:59", searchDto.getTimezone())));
        return condition.and(userSearchCondition(searchDto));
    }

    public String getUtcByTimeZone(String time, String timezone) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timezone)).withZoneSameInstant(ZoneOffset.UTC).format(formatter);
    }

    private Condition buildApplicationCondition(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Condition condition = trueCondition();
        Condition permissionCondition = jobDataPermissionCondition(searchDto, teamDTO);
        Optional<Condition> jobCondition = jobSearchCondition(searchDto);
        Optional<Condition> companyCondition = companySearchCondition(searchDto);
        if (jobCondition.isPresent()) {
            condition = condition.and(jobCondition.get());
        }
        if (companyCondition.isPresent()) {
            condition = condition.and(companyCondition.get());
        }

        return condition.and(permissionCondition).and(userRoleCondition(searchDto));
    }

    /**
     * 构建维度字段列表
     */
    private List<Field<?>> buildDimensions(RecruitingKpiReportSearchDto searchDto, Field<String> dateField, String timezone) {
        return searchDto.getGroupByFieldList().stream()
                .flatMap(groupByField -> switch (groupByField) {
                    case COMPANY, JOB -> Stream.empty();
                    case USER -> Stream.of(USER_ID, USER_NAME);
                    case TEAM -> Stream.of(TEAM_ID, TEAM_NAME, TEAM_PARENT_ID, TEAM_LEVEL, TEAM_IS_LEAF);
                    case DAY -> Stream.of(buildDateField("day", dateField, timezone));
                    case WEEK -> Stream.of(buildDateField("week", dateField, timezone));
                    case MONTH -> Stream.of(buildDateField("month", dateField, timezone));
                    case YEAR -> Stream.of(buildDateField("year", dateField, timezone));
                    case QUARTER -> Stream.of(buildDateField("quarter", dateField, timezone));
                }).toList();
    }

    /**
     * 构建日期维度字段
     */
    private Field<?> buildDateField(String period, Field<String> dateField, String timezone) {
        String convertedDate = convertTz(dateField.getName(), timezone);
        return switch (period) {
            case "day" -> field("DATE_FORMAT(DATE_TRUNC('day', %s), '%%Y-%%m-%%d')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "week" -> field("DATE_FORMAT(DATE_TRUNC('week', %s), '%%Y-%%m-%%d')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "month" ->
                    field("DATE_FORMAT(DATE_TRUNC('month', %s), '%%Y-%%m')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "year" -> field("DATE_FORMAT(DATE_TRUNC('year', %s), '%%Y')".formatted(convertedDate)).as(DATE_DIM_ALIAS);
            case "quarter" ->
                    field("CONCAT(YEAR(%s),'-', QUARTER(%s))".formatted(convertedDate, convertedDate)).as(DATE_DIM_ALIAS);
            default -> throw new IllegalArgumentException("Unsupported period: " + period);
        };
    }

    private static String convertTz(String dataColumn, String timezone) {
        return "CONVERT_TZ(%s, 'UTC', '%s')".formatted(dataColumn, timezone);
    }

    private static Field<Integer> bitmap_union_count(Field<Integer> field) {
        return field("BITMAP_UNION_COUNT(%s)".formatted(field.getName()), Integer.class);
    }

    private static Field<String> bitmap_union_to_string(Field<?> field) {
        return field("BITMAP_TO_STRING(BITMAP_UNION(%s))".formatted(field.getName()), String.class);
    }

    // 指标定义移到常量部分
    private static final List<Field<?>> NOTE_METRICS = List.of(
            bitmap_union_count(field("callNoteNum", Integer.class)).as("callNoteNum"),
            bitmap_union_count(field("personNoteNum", Integer.class)).as("personNoteNum"),
            bitmap_union_count(field("otherNoteNum", Integer.class)).as("otherNoteNum"),
            bitmap_union_count(field("emailNoteNum", Integer.class)).as("emailNoteNum"),
            bitmap_union_count(field("videoNoteNum", Integer.class)).as("videoNoteNum"),
            bitmap_union_count(field("iciNum", Integer.class)).as("iciNum"),
            bitmap_union_count(field("noteCount", Integer.class)).as("noteCount"),
            bitmap_union_to_string(field("unique_talent_ids")).as("uniqueTalentIds"),
            bitmap_union_count(field("application_note_count_num", Integer.class)).as("applicationNoteCountNum"),
            bitmap_union_count(field("talent_tracking_note_count_num", Integer.class)).as("talentTrackingNoteCountNum"),
            bitmap_union_to_string(field("talent_tracking_note_ids")).as("talentTrackingNoteIds")
    );

    private static final List<Field<?>> APPLICATION_METRICS = createKpiMetrics();

    private static final List<Field<?>> RESERVE_INTERVIEW_METRICS = List.of(
            // 预约面试相关指标
            bitmap_union_count(field("reserve_interview_total", Integer.class)).as("reserveInterviewTotal"),
            bitmap_union_count(field("reserve_current_interview_total", Integer.class)).as("reserveCurrentInterviewTotal"),
            bitmap_union_count(field("reserve_interview_aiRecommendCountNum", Integer.class)).as("reserveInterviewAiRecommendCountNum"),
            bitmap_union_count(field("reserve_interview_currentAiRecommendNum", Integer.class)).as("reserveInterviewCurrentAiRecommendNum"),
            bitmap_union_count(field("reserve_interview_precisionAiRecommendNum", Integer.class)).as("reserveInterviewPrecisionAiRecommendNum"),
            bitmap_union_count(field("reserve_interview_currentPrecisionAiRecommendNum", Integer.class)).as("reserveInterviewCurrentPrecisionAiRecommendNum")
    );


    /**
     * 创建 KPI 指标列表 - 使用方法来避免过长的静态初始化
     */
    private static List<Field<?>> createKpiMetrics() {
        return List.of(
                // 投递相关指标
                bitmap_union_count(field("submit_to_job_current_countNum", Integer.class)).as("submitToJobCurrentCountNum"),
                bitmap_union_count(field("submit_to_job_countNum", Integer.class)).as("submitToJobCountNum"),
                bitmap_union_count(field("submit_to_job_aiRecommendCountNum", Integer.class)).as("submitToJobAiRecommendCountNum"),
                bitmap_union_count(field("submit_to_job_currentAiRecommendNum", Integer.class)).as("submitToJobCurrentAiRecommendNum"),
                bitmap_union_count(field("submit_to_job_precisionAiRecommendNum", Integer.class)).as("submitToJobPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_job_currentPrecisionAiRecommendNum", Integer.class)).as("submitToJobCurrentPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_job_currentStayedOver", Integer.class)).as("submitToJobCurrentStayedOver"),
                bitmap_union_count(field("submit_to_job_stayedOver", Integer.class)).as("submitToJobStayedOver"),

                // 提交给客户相关指标
                bitmap_union_count(field("submit_to_client_countNum", Integer.class)).as("submitToClientCountNum"),
                bitmap_union_count(field("submit_to_client_current_countNum", Integer.class)).as("submitToClientCurrentCountNum"),
                bitmap_union_count(field("submit_to_client_aiRecommendCountNum", Integer.class)).as("submitToClientAiRecommendCountNum"),
                bitmap_union_count(field("submit_to_client_currentAiRecommendNum", Integer.class)).as("submitToClientCurrentAiRecommendNum"),
                bitmap_union_count(field("submit_to_client_precisionAiRecommendNum", Integer.class)).as("submitToClientPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_client_currentPrecisionAiRecommendNum", Integer.class)).as("submitToClientCurrentPrecisionAiRecommendNum"),
                bitmap_union_count(field("submit_to_client_currentStayedOver", Integer.class)).as("submitToClientCurrentStayedOver"),
                bitmap_union_count(field("submit_to_client_stayedOver", Integer.class)).as("submitToClientStayedOver"),

                // 面试相关基础指标
                bitmap_union_count(field("interview1", Integer.class)).as("interview1"),
                bitmap_union_count(field("interview2", Integer.class)).as("interview2"),
                bitmap_union_count(field("current_interview1", Integer.class)).as("currentInterview1"),
                bitmap_union_count(field("current_interview2", Integer.class)).as("currentInterview2"),
                bitmap_union_count(field("two_or_more_interviews", Integer.class)).as("twoOrMoreInterviews"),
                bitmap_union_count(field("current_two_or_more_interviews", Integer.class)).as("currentTwoOrMoreInterviews"),
                bitmap_union_count(field("interview_final", Integer.class)).as("interviewFinal"),
                bitmap_union_count(field("current_interview_final", Integer.class)).as("currentInterviewFinal"),
                bitmap_union_count(field("interview_total", Integer.class)).as("interviewTotal"),
                bitmap_union_count(field("current_interview_total", Integer.class)).as("currentInterviewTotal"),
                bitmap_union_count(field("unique_interview_talents", Integer.class)).as("uniqueInterviewTalents"),
                bitmap_union_count(field("interview_total_process", Integer.class)).as("interviewTotalProcess"),
                bitmap_union_count(field("current_interview_total_process", Integer.class)).as("currentInterviewTotalProcess"),

                // 面试 AI 推荐指标
                bitmap_union_count(field("interviewTotalAiRecommendNum", Integer.class)).as("interviewTotalAiRecommendNum"),
                bitmap_union_count(field("currentInterviewTotalAiRecommendNum", Integer.class)).as("currentInterviewTotalAiRecommendNum"),
                bitmap_union_count(field("interviewTotalProcessAiRecommendNum", Integer.class)).as("interviewTotalProcessAiRecommendNum"),
                bitmap_union_count(field("currentInterviewTotalProcessAiRecommendNum", Integer.class)).as("currentInterviewTotalProcessAiRecommendNum"),
                bitmap_union_count(field("interview1AiRecommendNum", Integer.class)).as("interview1AiRecommendNum"),
                bitmap_union_count(field("interview2AiRecommendNum", Integer.class)).as("interview2AiRecommendNum"),
                bitmap_union_count(field("currentInterview1AiRecommendNum", Integer.class)).as("currentInterview1AiRecommendNum"),
                bitmap_union_count(field("currentInterview2AiRecommendNum", Integer.class)).as("currentInterview2AiRecommendNum"),
                bitmap_union_count(field("twoOrMoreInterviewsAiRecommendNum", Integer.class)).as("twoOrMoreInterviewsAiRecommendNum"),
                bitmap_union_count(field("currentTwoOrMoreInterviewsAiRecommendNum", Integer.class)).as("currentTwoOrMoreInterviewsAiRecommendNum"),
                bitmap_union_count(field("interviewFinalAiRecommendNum", Integer.class)).as("interviewFinalAiRecommendNum"),
                bitmap_union_count(field("currentInterviewFinalAiRecommendNum", Integer.class)).as("currentInterviewFinalAiRecommendNum"),

                // 面试精准推荐指标
                bitmap_union_count(field("interviewTotalPrecisionAiRecommendNum", Integer.class)).as("interviewTotalPrecisionAiRecommendNum"),
                bitmap_union_count(field("currentInterviewTotalPrecisionAiRecommendNum", Integer.class)).as("currentInterviewTotalPrecisionAiRecommendNum"),
                bitmap_union_count(field("interviewNumProcessPrecisionAIRecommend", Integer.class)).as("interviewNumProcessPrecisionAIRecommend"),
                bitmap_union_count(field("currentInterviewNumProcessPrecisionAIRecommend", Integer.class)).as("currentInterviewNumProcessPrecisionAIRecommend"),
                bitmap_union_count(field("interview1PrecisionAiRecommendNum", Integer.class)).as("interview1PrecisionAiRecommendNum"),
                bitmap_union_count(field("currentInterview1PrecisionAiRecommendNum", Integer.class)).as("currentInterview1PrecisionAiRecommendNum"),
                bitmap_union_count(field("interview2PrecisionAiRecommendNum", Integer.class)).as("interview2PrecisionAiRecommendNum"),
                bitmap_union_count(field("currentInterview2PrecisionAiRecommendNum", Integer.class)).as("currentInterview2PrecisionAiRecommendNum"),
                bitmap_union_count(field("twoOrMoreInterviewsPrecisionAiRecommendNum", Integer.class)).as("twoOrMoreInterviewsPrecisionAiRecommendNum"),
                bitmap_union_count(field("currentTwoOrMoreInterviewsPrecisionAiRecommendNum", Integer.class)).as("currentTwoOrMoreInterviewsPrecisionAiRecommendNum"),
                bitmap_union_count(field("interviewFinalPrecisionAiRecommendNum", Integer.class)).as("interviewFinalPrecisionAiRecommendNum"),
                bitmap_union_count(field("currentInterviewFinalPrecisionAiRecommendNum", Integer.class)).as("currentInterviewFinalPrecisionAiRecommendNum"),

                // Offer 相关指标
                bitmap_union_count(field("offer_countNum", Integer.class)).as("offerCountNum"),
                bitmap_union_count(field("offer_current_countNum", Integer.class)).as("offerCurrentCountNum"),
                bitmap_union_count(field("offer_aiRecommendCountNum", Integer.class)).as("offerAiRecommendCountNum"),
                bitmap_union_count(field("offer_currentAiRecommendNum", Integer.class)).as("offerCurrentAiRecommendNum"),
                bitmap_union_count(field("offer_precisionAiRecommendNum", Integer.class)).as("offerPrecisionAiRecommendNum"),
                bitmap_union_count(field("offer_currentPrecisionAiRecommendNum", Integer.class)).as("offerCurrentPrecisionAiRecommendNum"),

                // Offer 接受相关指标
                bitmap_union_count(field("offer_accept_countNum", Integer.class)).as("offerAcceptCountNum"),
                bitmap_union_count(field("offer_accept_current_countNum", Integer.class)).as("offerAcceptCurrentCountNum"),
                bitmap_union_count(field("offer_accept_aiRecommendCountNum", Integer.class)).as("offerAcceptAiRecommendCountNum"),
                bitmap_union_count(field("offer_accept_currentAiRecommendNum", Integer.class)).as("offerAcceptCurrentAiRecommendNum"),
                bitmap_union_count(field("offer_accept_precisionAiRecommendNum", Integer.class)).as("offerAcceptPrecisionAiRecommendNum"),
                bitmap_union_count(field("offer_accept_currentPrecisionAiRecommendNum", Integer.class)).as("offerAcceptCurrentPrecisionAiRecommendNum"),

                // 入职相关指标
                bitmap_union_count(field("onboard_countNum", Integer.class)).as("onboardCountNum"),
                bitmap_union_count(field("onboard_current_countNum", Integer.class)).as("onboardCurrentCountNum"),
                bitmap_union_count(field("onboard_aiRecommendCountNum", Integer.class)).as("onboardAiRecommendCountNum"),
                bitmap_union_count(field("onboard_currentAiRecommendNum", Integer.class)).as("onboardCurrentAiRecommendNum"),
                bitmap_union_count(field("onboard_precisionAiRecommendNum", Integer.class)).as("onboardPrecisionAiRecommendNum"),
                bitmap_union_count(field("onboard_currentPrecisionAiRecommendNum", Integer.class)).as("onboardCurrentPrecisionAiRecommendNum"),

                // 淘汰相关指标
                bitmap_union_count(field("eliminate_countNum", Integer.class)).as("eliminateCountNum"),
                bitmap_union_count(field("eliminate_current_countNum", Integer.class)).as("eliminateCurrentCountNum"),
                bitmap_union_count(field("eliminate_aiRecommendCountNum", Integer.class)).as("eliminateAiRecommendCountNum"),
                bitmap_union_count(field("eliminate_currentAiRecommendNum", Integer.class)).as("eliminateCurrentAiRecommendNum"),
                bitmap_union_count(field("eliminate_precisionAiRecommendNum", Integer.class)).as("eliminatePrecisionAiRecommendNum"),
                bitmap_union_count(field("eliminate_currentPrecisionAiRecommendNum", Integer.class)).as("eliminateCurrentPrecisionAiRecommendNum")
        );
    }
}
