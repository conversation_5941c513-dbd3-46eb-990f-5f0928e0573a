package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.vo.recruiting.*;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Repository
public class RecruitingKpiUserRepository extends RecruitingKpiBaseRepository {

    public List<RecruitingKpiCommonCountVO> searchJobKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchJobTask");
        stopWatch.start();
        String sql = """
                SELECT
                    {selectTempFields},
                    sum( temp.openings ) count_num
                  FROM
                    (
                    SELECT
                        {selectFields}
                    FROM
                        job j
                        INNER JOIN job_user_relation ul ON ul.job_id = j.id
                        INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                        INNER join permission_team pt on pt.id = put.team_id
                        INNER join user u on u.id = ul.user_id
                        {joinTables}
                    WHERE
                        j.tenant_id = :tenantId
                        {whereClause}
                        GROUP BY {groupByField}
                    ) temp
                GROUP BY {tempGroupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        //设置租户id
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        List<? extends ReportField> selectTempFields = getSelectTempFieldsForJobOlap(searchDto.getGroupByFieldList());
        map.put("selectTempFields", reportFieldsToExpression(selectTempFields));
        List<? extends ReportField> selectFields = getSelectFieldsForJobOlap(searchDto.getGroupByFieldList());
        if (!selectFields.isEmpty()) {
            map.put("selectFields", reportFieldsToExpression(selectFields));
        }
        map.put("joinTables", getJoinTablesForJob(searchDto));
        map.put("whereClause", getWhereClauseForJob(searchDto, teamDTO, param));
        map.put("groupByField", getGroupByFieldFoJob(searchDto.getGroupByFieldList(), selectFields));
        map.put("tempGroupByField", getTempGroupByFieldForJob(selectTempFields));
        List<RecruitingKpiCommonCountVO> voList = getClassList2(sql, map, param, RecruitingKpiCommonCountVO.class, "searchJobKpiData");
        stopWatch.stop();
        log.info("[apn @{}] search job count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    private String getGroupByFieldFoJob(List<RecruitingKpiGroupByFieldType> groupByFieldList, List<? extends ReportField> selectFields) {
        List<RecruitingKpiGroupByFieldType> userOrTeam = List.of(RecruitingKpiGroupByFieldType.USER, RecruitingKpiGroupByFieldType.TEAM);
        List<ColumnField> groupFields = groupByFieldList.stream()
                .filter(userOrTeam::contains)
                .map(groupByFieldType -> {
                    {
                        switch (groupByFieldType) {
                            case USER -> {
                                return ColumnField.ofAutoAlias("ul.user_id");
                            }
                            case TEAM -> {
                                return ColumnField.ofAutoAlias("put.team_id");
                            }
                            default -> {
                                return null;
                            }
                        }
                    }
                })
                .filter(Objects::nonNull)
                .toList();
        List<? extends ReportField> finalGroupFields = Stream.concat(groupFields.stream(), selectFields.stream()).distinct().toList();
        return appendGroupByFields("ul.job_id, ", finalGroupFields);
    }


    public List<RecruitingKpiCommonCountVO> searchTalentKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchTalentTask");
        stopWatch.start();
        String sql = """
                select
                {columnFields},
                count(distinct t.id) countNum
                from talent t
                inner join talent_user_relation ul on ul.talent_id = t.id
                inner join user u on u.id = ul.user_id
                inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                inner join permission_team pt on pt.id = put.team_id
                {joinTables}
                where t.tenant_id = :tenantId
                {whereClause}
                GROUP BY {groupByField}
                """;
        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        List<? extends ReportField> finalSelectFields = getSelectFieldsForTalentOrApplicationOlap(searchDto, null);
        map.put("columnFields", reportFieldsToExpression(finalSelectFields));
        map.put("joinTables", getJoinTablesForTalent(searchDto));
        map.put("whereClause", getWhereClauseForTalent(searchDto, teamDTO, param));
        List<? extends ReportField> groupByFieldOlap = getGroupByFieldOlap(searchDto.getGroupByFieldList());
        List<ReportField> finalGroupFields = Stream.concat(groupByFieldOlap.stream(), finalSelectFields.stream()).distinct().toList();
        map.put("groupByField", appendGroupByFields("", finalGroupFields));
        List<RecruitingKpiCommonCountVO> voList = getClassList2(sql, map, param, RecruitingKpiCommonCountVO.class, "searchTalentKpiData");
        stopWatch.stop();
        log.info("[apn @{}] search talent count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    public List<RecruitingKpiApplicationCommonCountVO> searchNodeTypeKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO, ReportTableType reportTableType) {
        StopWatch stopWatch = new StopWatch("search" + reportTableType + "Task");
        stopWatch.start();
        String sql = """
                SELECT
                {columnFields},
                count(distinct node.id) countNum,
                count(distinct  case when trp.ai_score is not null then trp.id else null end ) as aiRecommendNum,
                count(distinct  case when rf.id is not null then trp.id else null end ) as precisionAiRecommendNum
               
                FROM
                {fromTable} node
                inner join talent_recruitment_process trp on trp.id = node.talent_recruitment_process_id
                left join (select distinct case when reason='UNLOCK_CANDIDATE' || reason='ADD_TO_TALENT' then ct.talent_id else rf.talent_id end as talent_id,rf.id,rf.job_id
                            from job_talent_recommend_feedback rf
                           left join credit_transaction ct on ct.profile_id = rf.talent_id
                           where  rf.reason in('ADD_TO_POSITION','ADD_TO_ASSOCIATION_JOB_FOLDER','UNLOCK_CANDIDATE','ADD_TO_TALENT')) rf on rf.job_id = trp.job_id and rf.talent_id = trp.talent_id
                inner join job j on j.id = trp.job_id
                inner join company c on c.id = j.company_id
                {processUserRelation}
                inner join talent t on t.id = trp.talent_id
                inner join user u on u.id = ul.user_id
                inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                inner join permission_team pt on pt.id = put.team_id
                {joinTables}
                where trp.tenant_id = :tenantId
                {whereClause}
                GROUP BY {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        List<? extends ReportField> finalSelectFields = getSelectFieldsForTalentOrApplicationOlap(searchDto, reportTableType);
        map.put("columnFields", reportFieldsToExpression(finalSelectFields));
        map.put("fromTable", getFromTable(reportTableType));
        map.put("processUserRelation", getKpiUserJoinTablesByUser(reportTableType));
        map.put("joinTables", getJoinTablesByUser(searchDto, "node", reportTableType));
        map.put("whereClause", getWhereClauseForApplication(searchDto, teamDTO, param, reportTableType));
        List<? extends ReportField> groupByFieldOlap = getGroupByFieldOlap(searchDto.getGroupByFieldList());
        List<ReportField> finalGroupFields = Stream.concat(groupByFieldOlap.stream(), finalSelectFields.stream()).distinct().toList();
        map.put("groupByField", appendGroupByFields("", finalGroupFields));
        List<RecruitingKpiApplicationCommonCountVO> voList = getClassList2(sql, map, param, RecruitingKpiApplicationCommonCountVO.class, "searchNodeTypeKpiData: "+ reportTableType);
        stopWatch.stop();
        log.info("[apn @{}] search {} count task, times = {}ms", searchDto.getSearchUserId(), reportTableType, stopWatch.getTotalTimeMillis());
        return voList;
    }

    public List<RecruitingKpiInterviewCountVO> searchReserveInterviewKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {

        RecruitingKpiReportSearchDto copySearchDto = ObjectUtil.cloneByStream(searchDto);
        copySearchDto.setDateType(RecruitingKpiDateType.ADD);
        StopWatch stopWatch = new StopWatch("searchReserveInterviewTask");
        stopWatch.start();
        String sql = """
                SELECT
                	{columnFields},
                	count(DISTINCT node.id ) interview_total,
                	count(DISTINCT CASE WHEN trpn.id IS NOT NULL THEN node.id END ) AS current_interview_total,
                	count(distinct case when trp.ai_score is not null then node.id else null end ) as aiRecommendNum,
                	count(distinct case when trp.ai_score is not null and trpn.id IS NOT NULL then node.id else null end ) as currentAiRecommendNum,
                	count(distinct case when rf.id is not null then node.id else null end ) as precisionAiRecommendNum,
                	count(distinct case when rf.id is not null and trpn.id IS NOT NULL then node.id else null end ) as currentPrecisionAiRecommendNum
                FROM
                	talent_recruitment_process_interview node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	left join (select distinct case when reason='UNLOCK_CANDIDATE' || reason='ADD_TO_TALENT' then ct.talent_id else rf.talent_id end as talent_id,rf.id,rf.job_id
                             from job_talent_recommend_feedback rf\s
                            left join credit_transaction ct on ct.profile_id = rf.talent_id
                            where  rf.reason in('ADD_TO_POSITION','ADD_TO_ASSOCIATION_JOB_FOLDER','ADD_TO_TALENT','UNLOCK_CANDIDATE')) rf on rf.job_id = trp.job_id and rf.talent_id = trp.talent_id
                	inner join job j on j.id = trp.job_id
                    inner join company c on c.id = j.company_id
                	INNER JOIN talent t ON t.id = trp.talent_id
                	{processUserRelation}
                	inner join user u on u.id = ul.user_id
                	inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                	{joinTables}
                WHERE trp.tenant_id = :tenantId
                {whereClause}
                GROUP BY {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", copySearchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        //interview 不支持 current 搜索
        List<? extends ReportField> finalSelectFields = getSelectFieldsForTalentOrApplicationOlap(searchDto, null);
        map.put("columnFields", reportFieldsToExpression(finalSelectFields));
        map.put("processUserRelation", getKpiUserJoinTablesByUser(ReportTableType.INTERVIEW));
        map.put("joinTables", getJoinTablesByUser(copySearchDto, "node", ReportTableType.INTERVIEW));
        map.put("whereClause", getWhereClauseForApplication(copySearchDto, teamDTO, param, ReportTableType.INTERVIEW));
        List<? extends ReportField> groupByFieldOlap = getGroupByFieldOlap(searchDto.getGroupByFieldList());
        List<ReportField> finalGroupFields = Stream.concat(groupByFieldOlap.stream(), finalSelectFields.stream()).distinct().toList();
        map.put("groupByField", appendGroupByFields("", finalGroupFields));
        List<RecruitingKpiInterviewCountVO> voList = getClassList2(sql, map, param, RecruitingKpiInterviewCountVO.class, "searchReserveInterviewKpiData");
        stopWatch.stop();
        log.info("[apn @{}] search reserve interview count task, times = {}ms", copySearchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }


    public List<RecruitingKpiInterviewCountVO> searchInterviewKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchInterviewTask");
        stopWatch.start();
        String sql = """
                WITH interview_max as (
                select
                max(progress) progress,
                talent_recruitment_process_id
                from talent_recruitment_process_interview
                group by talent_recruitment_process_id
                )
                SELECT
                	{columnFields},
                	count(DISTINCT CASE WHEN node.progress = 1 THEN node.id END ) AS interview1,
                	count(DISTINCT CASE WHEN node.progress = 2 THEN node.id END ) AS interview2,
                	count(DISTINCT CASE WHEN node.progress = 1  and max.progress = 1 and trpn.id IS NOT NULL THEN node.id END ) AS current_interview1,
                 	count(DISTINCT CASE WHEN node.progress = 2 and max.progress = 2 and trpn.id IS NOT NULL THEN node.id END ) AS current_interview2,
                	count(DISTINCT CASE WHEN node.progress >=2  and node.final_round !=1 THEN node.id END ) AS two_or_more_interviews,
                	count(DISTINCT CASE WHEN node.progress >=2 and node.final_round !=1 and trpn.id IS NOT NULL THEN node.id END ) AS current_two_or_more_interviews,
                	count(DISTINCT CASE WHEN node.final_round = 1 THEN node.id END ) AS interview_final,
                	count(DISTINCT CASE WHEN node.final_round = 1 and trpn.id IS NOT NULL THEN node.id END ) AS current_interview_final,
                	count(DISTINCT node.id ) interview_total,
                	count(DISTINCT CASE WHEN trpn.id IS NOT NULL THEN node.id END ) AS current_interview_total,
                	count(DISTINCT node.talent_recruitment_process_id ) interview_total_process,
                	count(DISTINCT CASE WHEN trpn.id IS NOT NULL THEN node.talent_recruitment_process_id END ) AS current_interview_total_process,
                	count(DISTINCT t.id) unique_interview_talents,
                	count(distinct  case when trp.ai_score is not null then node.id else null end ) as interviewTotalAiRecommendNum,
                	count(distinct  case when trp.ai_score is not null and trpn.id IS NOT NULL then node.id else null end ) as currentInterviewTotalAiRecommendNum,
                	
                	count(distinct  case when trp.ai_score is not null then trp.id else null end ) as interviewTotalProcessAiRecommendNum,
                	count(distinct  case when trp.ai_score is not null and trpn.id IS NOT NULL then trp.id else null end ) as currentInterviewTotalProcessAiRecommendNum,
                	
                	count(DISTINCT CASE WHEN node.progress = 1 and trp.ai_score is not null THEN trp.id else null END ) AS interview1AiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 1 and max.progress = 1 and trpn.id IS NOT NULL and trp.ai_score is not null THEN trp.id else null END ) AS currentInterview1AiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and trp.ai_score is not null THEN trp.id else null END  ) AS interview2AiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and max.progress = 2 and trpn.id IS NOT NULL and trp.ai_score is not null THEN trp.id else null END ) AS currentInterview2AiRecommendNum,
                    count(DISTINCT CASE WHEN node.progress >=2 and node.final_round !=1 and trp.ai_score is not null THEN node.id else null END ) AS twoOrMoreInterviewsAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress >=2 and node.final_round !=1 and trpn.id IS NOT NULL and trp.ai_score is not null THEN node.id else null END ) AS currentTwoOrMoreInterviewsAiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and trp.ai_score is not null THEN trp.id else null END ) AS interviewFinalAiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and trpn.id IS NOT NULL and trp.ai_score is not null THEN trp.id else null END ) AS currentInterviewFinalAiRecommendNum,
                	
                	count(distinct  case when rf.id is not null then node.id else null end ) as interviewTotalPrecisionAiRecommendNum,
                	count(distinct  case when rf.id is not null and trpn.id IS NOT NULL then node.id else null end ) as currentInterviewTotalPrecisionAiRecommendNum,
                	count(distinct  case when rf.id is not null then trp.id else null end ) as interviewNumProcessPrecisionAIRecommend,
                	count(distinct  case when rf.id is not null and trpn.id IS NOT NULL then trp.id else null end ) as currentInterviewNumProcessPrecisionAIRecommend,
                
                	count(DISTINCT CASE WHEN node.progress = 1 and rf.id is not null THEN trp.id else null END ) AS interview1PrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 1 and max.progress = 1 and trpn.id IS NOT NULL and rf.id is not null THEN trp.id else null END ) AS currentInterview1PrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and rf.id is not null THEN trp.id else null END  ) AS interview2PrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and max.progress = 2 and trpn.id IS NOT NULL and rf.id is not null THEN trp.id else null END ) AS currentInterview2PrecisionAiRecommendNum,
                    count(DISTINCT CASE WHEN node.progress >=2 and node.final_round !=1 and rf.id is not null THEN node.id else null END ) AS twoOrMoreInterviewsPrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress >=2 and node.final_round !=1 and trpn.id IS NOT NULL and rf.id is not null THEN node.id else null END ) AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and rf.id is not null THEN trp.id else null END ) AS interviewFinalPrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and trpn.id IS NOT NULL and rf.id is not null THEN trp.id else null END ) AS currentInterviewFinalPrecisionAiRecommendNum
                	
                FROM
                	talent_recruitment_process_interview node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	left join (select distinct case when reason='UNLOCK_CANDIDATE' || reason='ADD_TO_TALENT' then ct.talent_id else rf.talent_id end as talent_id,rf.id,rf.job_id
                             from job_talent_recommend_feedback rf\s
                            left join credit_transaction ct on ct.profile_id = rf.talent_id
                            where  rf.reason in('ADD_TO_POSITION','ADD_TO_ASSOCIATION_JOB_FOLDER','UNLOCK_CANDIDATE','ADD_TO_TALENT')) rf on rf.job_id = trp.job_id and rf.talent_id = trp.talent_id 
                	inner join job j on j.id = trp.job_id
                    inner join company c on c.id = j.company_id
                	INNER JOIN talent t ON t.id = trp.talent_id
                	{processUserRelation}
                	inner join user u on u.id = ul.user_id
                	inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                	{joinTables}
                	left join interview_max max on max.talent_recruitment_process_id = node.talent_recruitment_process_id
                WHERE trp.tenant_id = :tenantId
                {whereClause}
                GROUP BY {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        //interview 不支持 current 搜索
        List<? extends ReportField> finalSelectFields = getSelectFieldsForTalentOrApplicationOlap(searchDto, null);
        map.put("columnFields", reportFieldsToExpression(finalSelectFields));
        map.put("processUserRelation", getKpiUserJoinTablesByUser(ReportTableType.INTERVIEW));
        map.put("joinTables", getJoinTablesByUser(searchDto, "node", ReportTableType.INTERVIEW));
        map.put("whereClause", getWhereClauseForApplication(searchDto, teamDTO, param, ReportTableType.INTERVIEW));
        List<? extends ReportField> groupByFieldOlap = getGroupByFieldOlap(searchDto.getGroupByFieldList());
        List<ReportField> finalGroupFields = Stream.concat(groupByFieldOlap.stream(), finalSelectFields.stream()).distinct().toList();
        map.put("groupByField", appendGroupByFields("", finalGroupFields));
        List<RecruitingKpiInterviewCountVO> voList = getClassList2(sql, map, param, RecruitingKpiInterviewCountVO.class, "searchInterviewKpiData");
        stopWatch.stop();
        log.info("[apn @{}] search interview count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    public List<RecruitingKpiTalentNoteCountVO> searchTalentNoteKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchTalentNoteTask");
        stopWatch.start();
        String sql = """
                SELECT
                    {selectFields},
                    count( DISTINCT CASE WHEN tn.note_type = 0 THEN tn.id END ) callNoteNum,
                    count( DISTINCT CASE WHEN tn.note_type = 1 THEN tn.id END ) personNoteNum,
                    count( DISTINCT CASE WHEN tn.note_type = 2 THEN tn.id END ) otherNoteNum,
                    count( DISTINCT CASE WHEN tn.note_type = 3 THEN tn.id END ) emailNoteNum,
                    count( DISTINCT CASE WHEN tn.note_type = 4 THEN tn.id END ) videoNoteNum,
                    count( DISTINCT CASE WHEN tn.note_type = 5 THEN tn.id END ) iciNum,
                    count( DISTINCT CASE WHEN tn.agency_id IS NULL THEN tn.id END ) noteCount,
                    GROUP_CONCAT(DISTINCT tn.talent_id) as unique_talent_ids
                 FROM
                    talent_note tn
                    inner join user u on u.id = tn.puser_id
                    inner join permission_user_team put on put.user_id = tn.puser_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                    {joinTables}
                 WHERE
                    u.tenant_id = :tenantId
                    {whereClause}
                    GROUP BY {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        List<? extends ReportField> finalSelectFields = getSelectFieldsForTalentNoteOlap(searchDto.getGroupByFieldList());
        map.put("selectFields", reportFieldsToExpression(finalSelectFields));
        map.put("joinTables", getJoinTablesForTalentNote(searchDto, "tn"));
        map.put("whereClause", getWhereClauseForTalentNote(searchDto, teamDTO, param));
        List<? extends ReportField> groupByFieldOlap = getGroupByFieldForTalentNoteOlap(searchDto.getGroupByFieldList());
        List<ReportField> finalGroupFields = Stream.concat(groupByFieldOlap.stream(), finalSelectFields.stream()).distinct().toList();
        map.put("groupByField", appendGroupByFields("", finalGroupFields));
        List<RecruitingKpiTalentNoteCountVO> voList = getClassList2(sql, map, param, RecruitingKpiTalentNoteCountVO.class, "searchTalentNote");
        stopWatch.stop();
        log.info("[apn @{}] search talentNote count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    public List<RecruitingKpiApplicationNoteCountVO> searchApplicationNoteKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApplicationNoteKpiData");
        stopWatch.start();
        String sql = """
                SELECT
                	{selectFields},
                	count( DISTINCT node.id ) count_num
                FROM
                	talent_recruitment_process_note node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	INNER JOIN permission_user_team put ON put.user_id = node.user_id and put.is_primary = 1
                	inner join permission_team pt on pt.id = put.team_id
                	inner join user u on u.id = put.user_id
                	{joinTables}
                WHERE
                    node.tenant_id = :tenantId
                	{whereCondition}
                	GROUP BY {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        List<? extends ReportField> finalSelectFields = getSelectFieldsForApplicationOrApnProNoteOlap(searchDto.getGroupByFieldList());
        map.put("selectFields", reportFieldsToExpression(finalSelectFields));
        map.put("joinTables", getJoinTablesForApplicationNote(searchDto));
        param.put("tenantId", searchDto.getSearchTenantId());
        map.put("whereCondition", getWhereClauseForApplicationNote(searchDto, teamDTO, param));
        List<? extends ReportField> groupByFieldOlap = getGroupByFieldForApplicationOrApnProNoteOlap(searchDto.getGroupByFieldList());
        List<ReportField> finalGroupFields = Stream.concat(groupByFieldOlap.stream(), finalSelectFields.stream()).distinct().toList();
        map.put("groupByField", appendGroupByFields("", finalGroupFields));
        List<RecruitingKpiApplicationNoteCountVO> voList = getClassList2(sql, map, param, RecruitingKpiApplicationNoteCountVO.class, "searchApplicationNoteKpiData");
        stopWatch.stop();
        log.info("[apn @{}] search applicationNote count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    public List<RecruitingKpiCommonCountVO> searchApnProNoteKpiData(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchApnProNoteKpiData");
        stopWatch.start();
        String sql = """
                SELECT
                    {selectFields},
                    count( DISTINCT ttn.id ) count_num,
                	group_concat( DISTINCT ttn.id ) ids
                 FROM
                    talent_tracking_note ttn
                    INNER JOIN talent t ON t.id = ttn.synced_talent_id
                    inner join user u on u.id = ttn.user_id
                    inner join permission_user_team put on put.user_id = ttn.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                    {joinTables}
                 WHERE
                    ttn.tenant_id = :tenantId
                    {whereCondition}
                    GROUP BY {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        List<? extends ReportField> finalSelectFields = getSelectFieldsForApplicationOrApnProNoteOlap(searchDto.getGroupByFieldList());
        map.put("selectFields", reportFieldsToExpression(finalSelectFields));
        map.put("joinTables", getJoinTablesForApnProNote(searchDto));
        map.put("whereCondition", getWhereConditionForApnProNote(searchDto, teamDTO, param));
        List<? extends ReportField> groupByFieldOlap = getGroupByFieldForApplicationOrApnProNoteOlap(searchDto.getGroupByFieldList());
        List<ReportField> finalGroupFields = Stream.concat(groupByFieldOlap.stream(), finalSelectFields.stream()).distinct().toList();
        map.put("groupByField", appendGroupByFields("", finalGroupFields));
        List<RecruitingKpiCommonCountVO> voList = getClassList2(sql, map, param, RecruitingKpiCommonCountVO.class,"searchApnProNoteKpiData");
        stopWatch.stop();
        log.info("[apn @{}] search apn pro note count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    private String getWhereConditionForApnProNote(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        param.put("startDate", searchDto.getStartDateUtc());
        param.put("endDate", searchDto.getEndDateUtc());
        sb.append(" and ttn.created_date BETWEEN :startDate AND :endDate ");
        if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
            sb.append(" and put.user_id in :userIdList ");
            param.put("userIdList", searchDto.getUserIdList());
        }
        if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            sb.append("and put.team_id in :teamIdList  ");
            param.put("teamIdList", searchDto.getTeamIdList());
        }
        //talent search
//        appendTalentSearch(sb, searchDto, param);
        //job search
//        appendJobSearch(sb, searchDto, param);
        //company search
//        appendCompanySearch(sb, searchDto, param);
        //data permission
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            sb.append(" and put.user_id = :userId ");
            param.put("userId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            sb.append("and put.team_id in :teamIds ");
            param.put("teamIds", teamDTO.getNestedTeamIds());
        }
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        return sb.toString();
    }

    private String getJoinTablesForApnProNote(RecruitingKpiReportSearchDto searchDto) {
        return " inner join date_dimension d on d.date = DATE_FORMAT( CONVERT_TZ( ttn.created_date, 'UTC', '" + searchDto.getTimezone() + "' ), '%Y-%m-%d' ) ";
    }


    private List<? extends ReportField> getGroupByFieldForApplicationOrApnProNoteOlap(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return groupByFieldList.stream().map(groupByFieldType -> switch (groupByFieldType) {
            case USER -> ColumnField.ofAutoAlias("put.user_id");
            case TEAM -> ColumnField.ofAutoAlias("put.team_id");
            case DAY -> ColumnField.ofAutoAlias("d.date");
            case WEEK -> ColumnField.ofAutoAlias("d.start_of_week");
            case MONTH -> ColumnField.ofAutoAlias("d.start_of_month");
            case QUARTER -> ColumnField.ofAutoAlias("d.quarter_of_year");
            case YEAR -> ColumnField.ofAutoAlias("d.year");
            default -> null;
        }).filter(Objects::nonNull).distinct().toList();
    }

    private String getWhereClauseForApplicationNote(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        String timezoneColumn = getColumnSuffix(searchDto.getTimezone());
        param.put("startDate", searchDto.getStartDateUtc());
        param.put("endDate", searchDto.getEndDateUtc());
        String fieldColumn = "node.created_date";
        if (shouldJoinDateDimension(searchDto) && StrUtil.isNotBlank(timezoneColumn)) {
            fieldColumn = getTimezoneField("node", searchDto.getTimezone(), "created_date");
            param.put("startDate", searchDto.getStartDate());
            param.put("endDate", searchDto.getEndDate());
        }
        sb.append(" and ").append(fieldColumn).append(" BETWEEN :startDate AND :endDate ");
        if (CollUtil.isNotEmpty(searchDto.getUserIdList()) && CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            sb.append(" and ( put.user_id in :userIdList or put.team_id in :teamIdList )");
            param.put("userIdList", searchDto.getUserIdList());
            param.put("teamIdList", searchDto.getTeamIdList());
        } else {
            if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
                sb.append(" and put.user_id in :userIdList ");
                param.put("userIdList", searchDto.getUserIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
                sb.append(" and put.team_id in :teamIdList  ");
                param.put("teamIdList", searchDto.getTeamIdList());
            }
        }
        //talent search
//        appendTalentSearchForApplication(sb, searchDto, param);
        //job search
//        appendJobSearch(sb, searchDto, param);
//        //company search
//        appendCompanySearch(sb, searchDto, param);
        //data permission
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            sb.append(" and put.user_id = :userId ");
            param.put("userId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            sb.append("and put.team_id in :teamIds ");
            param.put("teamIds", teamDTO.getNestedTeamIds());
        }
//        appendJobDataPermissionForJobOrDetail(sb, searchDto, param, teamDTO);
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        return sb.toString();
    }

    private String getJoinTablesForApplicationNote(RecruitingKpiReportSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        if (shouldJoinDateDimension(searchDto)) {
            String dateField = getTimezoneField("node", searchDto.getTimezone(), "created_date");
            sb.append(" inner join date_dimension d on d.date = ").append(dateField).append(" ");
        }
        return sb.toString();
    }

    private List<? extends ReportField> getSelectFieldsForApplicationOrApnProNoteOlap(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
       return groupByFieldList.stream().flatMap(groupByFieldType -> switch (groupByFieldType) {
           case USER -> Stream.of(ColumnField.ofAutoAlias("put.user_id"), FunctionField.of("concat(u.first_name, \" \", u.last_name)", "user_name"));
           case TEAM -> Stream.of(ColumnField.ofAutoAlias("put.team_id"), ColumnField.of("pt.name", "team_name"));
           case DAY -> Stream.of(ColumnField.groupByDate("d.date"));
           case WEEK -> Stream.of(ColumnField.groupByDate("d.start_of_week"));
           case MONTH -> Stream.of(FunctionField.of("DATE_FORMAT(d.start_of_month, '%Y-%m' )", "group_by_date"));
           case QUARTER -> Stream.of(ColumnField.groupByDate("d.quarter_of_year"));
           case YEAR -> Stream.of(ColumnField.groupByDate("d.year"));
           default -> Stream.empty();
       }).filter(Objects::nonNull).distinct().toList();
    }


    /**
     * 获取查询条件
     * @param searchDto
     * @param teamDTO
     * @return
     */
    private String getWhereClauseForTalentNote(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        String timezoneColumn = getColumnSuffix(searchDto.getTimezone());
        boolean groupDateFlag = shouldJoinDateDimension(searchDto);
        // Append date range condition to the query based on timezone and grouping flag
        if (StrUtil.isNotBlank(timezoneColumn)) {
            // If timezone is set (固化 means 'solidified' or 'fixed' here)
            param.put("startDate", BooleanUtil.isTrue(groupDateFlag) ? searchDto.getStartDate() : searchDto.getStartDateUtc());
            param.put("endDate", BooleanUtil.isTrue(groupDateFlag) ? searchDto.getEndDate() : searchDto.getEndDateUtc());

            // Use 'd.date' for grouped dates or 'tn.created_date' otherwise
            sb.append(" and ").append(BooleanUtil.isTrue(groupDateFlag) ? "d.date" : "tn.created_date")
                    .append(" BETWEEN :startDate AND :endDate ");
        } else {
            // If timezone is not set (未固化 means 'not fixed')
            param.put("startDate", searchDto.getStartDateUtc());
            param.put("endDate", searchDto.getEndDateUtc());

            // Always use 'tn.created_date' since timezone isn't considered
            sb.append(" and tn.created_date BETWEEN :startDate AND :endDate ");
        }
        if (CollUtil.isNotEmpty(searchDto.getUserIdList()) && CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            sb.append(" and (put.user_id in :userIdList or put.team_id in :teamIdList) ");
            param.put("userIdList", searchDto.getUserIdList());
            param.put("teamIdList", searchDto.getTeamIdList());
        } else {
            if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
                sb.append(" and put.user_id in :userIdList ");
                param.put("userIdList", searchDto.getUserIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
                sb.append(" and put.team_id in :teamIdList  ");
                param.put("teamIdList", searchDto.getTeamIdList());
            }
        }

        //talent search
//        appendTalentSearch(sb, searchDto, param);
        //job search
//        appendJobSearch(sb, searchDto, param);
        //company search
//        appendCompanySearch(sb, searchDto, param);
        //data permission
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            sb.append(" and put.user_id = :userId ");
            param.put("userId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            sb.append("and put.team_id in :teamIds ");
            param.put("teamIds", teamDTO.getNestedTeamIds());
        }
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        return sb.toString();
    }

    List<? extends ReportField> getSelectFieldsForTalentNoteOlap(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return groupByFieldList.stream().flatMap(groupByFieldType -> switch (groupByFieldType) {
            case USER -> Stream.of(ColumnField.of("tn.puser_id", "user_id"), FunctionField.of("CONCAT(u.first_name, ' ', u.last_name)", "user_name"));
            case TEAM -> Stream.of(ColumnField.ofAutoAlias("put.team_id"), ColumnField.of("pt.name", "team_name"));
            case DAY -> Stream.of(ColumnField.groupByDate("d.date"));
            case WEEK -> Stream.of(ColumnField.groupByDate("d.start_of_week"));
            case MONTH -> Stream.of(FunctionField.of("DATE_FORMAT(d.start_of_month, '%Y-%m' )", "group_by_date"));
            case QUARTER -> Stream.of(ColumnField.groupByDate("d.quarter_of_year"));
            case YEAR -> Stream.of(ColumnField.groupByDate("d.year"));
            default -> Stream.empty();
        }).distinct().toList();
    }

    List<? extends ReportField> getGroupByFieldForTalentNoteOlap(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return groupByFieldList.stream().map(groupByFieldType -> switch (groupByFieldType) {
            case USER -> ColumnField.ofAutoAlias("put.user_id");
            case TEAM -> ColumnField.ofAutoAlias("put.team_id");
            case DAY -> ColumnField.groupByDate("d.date");
            case WEEK -> ColumnField.groupByDate("d.start_of_week");
            case MONTH -> ColumnField.groupByDate("d.start_of_month");
            case QUARTER -> ColumnField.groupByDate("d.quarter_of_year");
            case YEAR -> ColumnField.groupByDate("d.year");
            default -> null;
        }).filter(Objects::nonNull).distinct().toList();
    }

    /**
     * 根据入参来获取 join tables
     * @param searchDto 时区
     * @return
     */
    protected String getJoinTablesForJob(RecruitingKpiReportSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        getJoinTablesForJobColumn(searchDto, sb);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) || ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            sb.append("""
                    left join talent_recruitment_process trp ON trp.job_id = j.id
                    """);
            getJoinTablesForJobByJobOrCompanyOrTalent(searchDto, sb);
        }
        return sb.toString();
    }

    protected String getJoinTablesForTalent(RecruitingKpiReportSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        String timezone = searchDto.getTimezone();
        sb.append(" inner join date_dimension d on d.date = ")
                .append(getTimezoneField("t", timezone, "created_date"))
                .append(" ");

        if (ObjectUtil.isNotEmpty(searchDto.getJob()) || ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            sb.append(" left join talent_recruitment_process trp on trp.talent_id = t.id ");
            boolean joinedJob = false;
            if (ObjectUtil.isNotEmpty(searchDto.getJob())) {
                sb.append(" left join job j on j.id = trp.job_id ");
                joinedJob = true;
            }
            if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
                if (!joinedJob) {
                    sb.append(" left join job j on j.id = trp.job_id ");
                }
                sb.append(" left join company c on c.id = j.company_id ");
            }
            getJoinTablesByJobOrCompany(searchDto, sb);
        }

        return sb.toString();
    }

    /**
     * 根据入参来获取 join tables
     * @param searchDto search
     * @param searchTableAlias 查询表别名
     * @return
     */
    protected String getJoinTablesForTalentNote(RecruitingKpiReportSearchDto searchDto, String searchTableAlias) {
        List<RecruitingKpiGroupByFieldType> groupByFieldList = searchDto.getGroupByFieldList();
        String timezone = searchDto.getTimezone();
        return groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case DAY, WEEK, MONTH, QUARTER, YEAR -> {
                    String  dateField = getTimezoneField(searchTableAlias, timezone, "created_date");
                    return " inner join date_dimension d on d.date = " + dateField + " ";
                }
                default -> { return "";}
            }
        }).collect(Collectors.joining(" "));
    }

    private List<? extends ReportField> getSelectTempFieldsForJobOlap(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case USER -> {
                    return List.of(ColumnField.ofAutoAlias("temp.user_id"), ColumnField.ofAutoAlias("temp.user_name"));
                }
                case TEAM -> {
                    return List.of(ColumnField.ofAutoAlias("temp.team_id"), ColumnField.ofAutoAlias("temp.team_name"));
                }
                case DAY, WEEK, MONTH, QUARTER, YEAR -> {
                    return List.of(ColumnField.groupByDate("temp.group_by_date"));
                }
                default -> {
                    return Collections.<ReportField>emptyList();
                }
            }
        }).filter(l -> !l.isEmpty()).flatMap(List::stream).toList();
    }

    protected List<? extends ReportField> getSelectFieldsForJobOlap(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        Stream<? extends ReportField> fixedColumnFields = Stream.of(
                ColumnField.of("j.id", "job_id"),
                ColumnField.ofAutoAlias("j.openings")
        );

        Stream<? extends ReportField> baseSelectFields = groupByFieldList.stream().flatMap(groupByFieldType -> switch (groupByFieldType) {
            case DAY -> Stream.of(FunctionField.of("DATE_FORMAT(d.date, '%Y-%m-%d' )", "group_by_date"));
            case WEEK -> Stream.of(FunctionField.of("DATE_FORMAT(d.start_of_week, '%Y-%m-%d' )", "group_by_date"));
            case MONTH -> Stream.of(FunctionField.of("DATE_FORMAT(d.start_of_month, '%Y-%m' )", "group_by_date"));
            case QUARTER -> Stream.of(ColumnField.groupByDate("d.quarter_of_year"));
            case YEAR -> Stream.of(ColumnField.groupByDate("d.year"));
            case TEAM -> Stream.of(ColumnField.ofAutoAlias("put.team_id"), ColumnField.of("pt.name", "team_name"));
            case USER ->
                    Stream.of(ColumnField.ofAutoAlias("ul.user_id"), FunctionField.of("CONCAT(u.first_name, ' ', u.last_name)", "user_name"));
            default -> Stream.of();
        }).filter(Objects::nonNull).distinct();
        return Stream.concat(baseSelectFields, fixedColumnFields).toList();
    }


}
