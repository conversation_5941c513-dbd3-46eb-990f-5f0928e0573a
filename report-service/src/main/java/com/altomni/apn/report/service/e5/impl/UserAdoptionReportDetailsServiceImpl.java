package com.altomni.apn.report.service.e5.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.common.domain.dict.EnumVoipCallResult;
import com.altomni.apn.common.domain.dict.EnumVoipCallType;
import com.altomni.apn.common.dto.email.EmailLogOverviewDTO;
import com.altomni.apn.common.dto.email.MailSearchDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.SearchUserDto;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.talent.TalentFeignClient;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.report.config.env.ApplicationProperties;
import com.altomni.apn.report.domain.enumeration.E5ReportViewType;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.enumeration.UserAdoptionReportSearchType;
import com.altomni.apn.report.domain.enumeration.VoipDetailReportType;
import com.altomni.apn.report.domain.vo.RecruitingKpiApplicationBaseDetailVO;
import com.altomni.apn.report.domain.vo.RecruitingKpiTalentNoteDetailExcelENVO;
import com.altomni.apn.report.domain.vo.RecruitingKpiTalentNoteDetailVO;
import com.altomni.apn.report.domain.vo.e5.*;
import com.altomni.apn.report.domain.vo.voip.VoipDetailReportExcelVO;
import com.altomni.apn.report.domain.vo.voip.VoipDetailReportVO;
import com.altomni.apn.report.domain.vo.voip.VoipReportVO;
import com.altomni.apn.report.dto.RecruitingKpiApplicationDetailSearchDto;
import com.altomni.apn.report.dto.RecruitingKpiTalentNoteDetailSearchDto;
import com.altomni.apn.report.dto.TalentNoteDetailSearchDto;
import com.altomni.apn.report.dto.e5.*;
import com.altomni.apn.report.dto.voip.VoipDetailReportDTO;
import com.altomni.apn.report.service.VoipReportService;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.e5.UserAdoptionReportDetailsService;
import com.altomni.apn.report.service.email.EmailService;
import com.altomni.apn.report.service.recruiting.RecruitingKpiByUserService;
import com.altomni.apn.report.service.recruiting.RecruitingKpiService;
import com.altomni.apn.report.service.recruiting.impl.RecruitingKpiBaseServiceImpl;
import com.altomni.apn.report.service.talent.TalentService;
import com.altomni.apn.report.service.voip.VoipService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.report.util.HtmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.Collator;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Slf4j
@Service
public class UserAdoptionReportDetailsServiceImpl extends RecruitingKpiBaseServiceImpl implements UserAdoptionReportDetailsService {

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String URL_SEPATATOR = "/";

    private final static String SPACE = " ";

    private static final String CRM_NOTE_LIST = "/report/api/v1/bd-activity-report/list/note";

    private static final String CRM_NOTE_DETAIL_URI = "/report/api/v1/bd-activity-report/detail";

    private static final String CRM_EMAIL_DETAIL_REPORT = "/common/api/v1/mail/search-sent-count-replied";

    @Resource
    private VoipReportService voipReportService;

    @Resource
    private VoipService voipService;

    @Resource
    private RecruitingKpiService recruitingKpiService;

    @Resource
    private RecruitingKpiByUserService recruitingKpiByUserService;

    @Resource
    private HttpService httpService;

    @Resource
    private EmailService emailService;

    @Resource
    private TalentService talentService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private CompanyService companyService;

    @Resource
    private TalentFeignClient talentFeignClient;

    @Resource
    private UserAdoptionReportDetailsService self;

    @Override
    public Map<Long, int[]> getCallReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds) {
        VoipReportDTO voipReportDTO = new VoipReportDTO();
        voipReportDTO.setStartTime(userAdoptionReportDTO.getStartTime());
        voipReportDTO.setEndTime(userAdoptionReportDTO.getEndTime());
        voipReportDTO.setTimeZone(userAdoptionReportDTO.getTimeZone());
        voipReportDTO.setTenantId(SecurityUtils.getTenantId());
        if (CollectionUtils.isNotEmpty(userIds)) {
            voipReportDTO.setUserIdList(List.copyOf(userIds));
        }
        try {
            List<VoipReportVO> res = voipReportService.getVoipReport(voipReportDTO, null);
            if (CollectionUtils.isNotEmpty(res)) {
                return res.stream().collect(Collectors.toMap(VoipReportVO::getUserId, d -> new int[]{d.getTotalCalls(), d.getTotalCandidateCalls()}));
            }
        } catch (ExecutionException | InterruptedException e) {
            log.error("[UserAdoptionReportService: getCallReport] error when call voipReportService.getVoipReport, error msg: {}, with voipReportDTO: {}", e.getMessage(), voipReportDTO, e);
        }
        return Collections.emptyMap();
    }

    @Override
    public Map<Long, int[]> getCallReportV2(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds) {
        VoipReportDTO voipReportDTO = new VoipReportDTO();
        voipReportDTO.setStartTime(userAdoptionReportDTO.getStartTime());
        voipReportDTO.setEndTime(userAdoptionReportDTO.getEndTime());
        voipReportDTO.setTimeZone(userAdoptionReportDTO.getTimeZone());
        voipReportDTO.setTenantId(userAdoptionReportDTO.getSearchTenantId());
        if (CollectionUtils.isNotEmpty(userIds)) {
            voipReportDTO.setUserIdList(List.copyOf(userIds));
        }
        try {
            List<VoipReportVO> res = voipReportService.getVoipReportV2(userAdoptionReportDTO,voipReportDTO, null);
            if (CollectionUtils.isNotEmpty(res)) {
                return res.stream().collect(Collectors.toMap(VoipReportVO::getUserId, d -> new int[]{d.getTotalCalls(), d.getTotalCandidateCalls()}));
            }
        } catch (ExecutionException | InterruptedException e) {
            log.error("[UserAdoptionReportService: getCallReport] error when call voipReportService.getVoipReport, error msg: {}, with voipReportDTO: {}", e.getMessage(), voipReportDTO, e);
        }
        return Collections.emptyMap();
    }

    @Override
    public List<TeamAdoptionReportVO> getCallReportByTeamView(UserAdoptionReportDTO userAdoptionReportDTO, List<TeamInfoVO> teamInfoVOS) {
        VoipReportDTO voipReportDTO = new VoipReportDTO();
        List<Long> userIds = teamInfoVOS.stream().map(TeamInfoVO::getUserId).collect(Collectors.toList());
        voipReportDTO.setStartTime(userAdoptionReportDTO.getStartTime());
        voipReportDTO.setEndTime(userAdoptionReportDTO.getEndTime());
        voipReportDTO.setTimeZone(userAdoptionReportDTO.getTimeZone());
        voipReportDTO.setTenantId(SecurityUtils.getTenantId());
        if (CollectionUtils.isNotEmpty(userIds)) {
            voipReportDTO.setUserIdList(List.copyOf(userIds));
        }
        voipReportDTO.setTenantId(SecurityUtils.getTenantId());
        try {
            List<VoipContactDTO> res = voipService.findAllByVoipReport(voipReportDTO).getBody();
            if (CollectionUtils.isEmpty(res)) {
                return Collections.emptyList();
            }
            //填充团队信息
            res.forEach(d -> {
                for (TeamInfoVO teamInfoVO : teamInfoVOS) {
                    if (teamInfoVO.getUserId().equals(d.getUserId())) {
                        d.setTeamId(teamInfoVO.getTeamId());
                    }
                }
            });
            Map<Long, List<VoipContactDTO>> mapByTeamId = res.stream().collect(Collectors.groupingBy(VoipContactDTO::getTeamId));
            List<TeamAdoptionReportVO> teamAdoptionReportVOForCall = mapByTeamId.entrySet().stream().map(entry -> {
                TeamAdoptionReportVO teamAdoptionReportVO = new TeamAdoptionReportVO();
                Long teamId = entry.getKey();
                List<VoipContactDTO> value = entry.getValue();
                teamAdoptionReportVO.setTeamId(teamId);
                teamAdoptionReportVO.setCallCount(value.size());
                long count = value.stream().map(VoipContactDTO::getTalentId).distinct().count();
                teamAdoptionReportVO.setUniqueCalledTalentCount((int) count);
                return teamAdoptionReportVO;
            }).collect(Collectors.toList());
            return teamAdoptionReportVOForCall;
        } catch (Exception e) {
            log.error("[UserAdoptionReportService: getCallReport] error when call voipReportService.getVoipReport, error msg: {}, with voipReportDTO: {}", e.getMessage(), voipReportDTO, e);
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String,Integer> getCallReportForTotal(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds) {
        VoipReportDTO voipReportDTO = new VoipReportDTO();
        voipReportDTO.setStartTime(userAdoptionReportDTO.getStartTime());
        voipReportDTO.setEndTime(userAdoptionReportDTO.getEndTime());
        voipReportDTO.setTimeZone(userAdoptionReportDTO.getTimeZone());
        voipReportDTO.setTenantId(SecurityUtils.getTenantId());
        if (CollectionUtils.isNotEmpty(userIds)) {
            voipReportDTO.setUserIdList(List.copyOf(userIds));
        }
        Map<String,Integer> callMap = new HashMap<>();
        callMap.put("callCount",0);
        callMap.put("uniqueCalledTalentCount",0);
        try {
            List<VoipContactDTO> res = voipService.findAllByVoipReport(voipReportDTO).getBody();
            if (CollectionUtils.isNotEmpty(res)) {
               callMap.put("callCount",res.size());
               long count = res.stream().map(VoipContactDTO::getTalentId).distinct().count();
               callMap.put("uniqueCalledTalentCount",(int)count);
            }
        } catch (Exception e) {
            log.error("[UserAdoptionReportService: getCallReport] error when call voipReportService.getVoipReport, error msg: {}, with voipReportDTO: {}", e.getMessage(), voipReportDTO, e);
        }
        return callMap;
    }

    @ProcessConfidentialTalent
    @Override
    public List<VoipDetailReportVO> getCallDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable) {
        VoipDetailReportDTO voipDetailReportDTO = new VoipDetailReportDTO();
        voipDetailReportDTO.setStartTime(userAdoptionReportDTO.getStartTime());
        voipDetailReportDTO.setEndTime(userAdoptionReportDTO.getEndTime());
        voipDetailReportDTO.setTimeZone(userAdoptionReportDTO.getTimeZone());
        voipDetailReportDTO.setTenantId(SecurityUtils.getTenantId());
        Long createBySearchId;
        if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNotEmpty(userAdoptionReportDTO.getSearch().getCreatedBy())){
            createBySearchId = Long.valueOf(userAdoptionReportDTO.getSearch().getCreatedBy());
        } else  {
            createBySearchId = null;
        }
        if (E5ReportViewType.TEAM == userAdoptionReportDTO.getViewType()){
            HashSet<Long> teamIds = new HashSet<>(List.of(userAdoptionReportDTO.getTeamId()));
            Set<Long> teamUserIds = userService.getAllTeamUserIdsByPermissionTeamIdIn(teamIds).getBody();
            //重写逻辑
            Set<Long> realSearchUserIds;
            List<Long> userIdList = userAdoptionReportDTO.getUserIdList();
            List<Long> teamIdList = userAdoptionReportDTO.getTeamIdList();
            if(CollectionUtils.isEmpty(userIdList) && CollectionUtils.isEmpty(teamIdList)){
                realSearchUserIds = teamUserIds;
            } else if(CollectionUtils.isNotEmpty(userIdList) && CollectionUtils.isEmpty(teamIdList)){
                realSearchUserIds = userIdList.stream().filter(userId -> teamUserIds.contains(userId)).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(realSearchUserIds)){
                    return Collections.emptyList();
                }
            } else if(CollectionUtils.isEmpty(userIdList) && CollectionUtils.isNotEmpty(teamIdList)){
                if (teamIdList.contains(userAdoptionReportDTO.getTeamId())){
                    realSearchUserIds = teamUserIds;
                }else {
                    return Collections.emptyList();
                }
            }else {
                if (teamIdList.contains(userAdoptionReportDTO.getTeamId())){
                    realSearchUserIds = teamUserIds;
                }else {
                    realSearchUserIds = userIdList.stream().filter(userId -> teamUserIds.contains(userId)).collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(realSearchUserIds)){
                        return Collections.emptyList();
                    }
                }
            }
            if(createBySearchId != null){
                if(realSearchUserIds.contains(createBySearchId)){
                    voipDetailReportDTO.setUserIdList(List.of(createBySearchId));
                }else {
                    return Collections.emptyList();
                }
            }else {
                voipDetailReportDTO.setUserIdList(List.copyOf(realSearchUserIds));
            }
        } else {
            voipDetailReportDTO.setUserIdList(List.of(userAdoptionReportDTO.getUserId()));
            if (createBySearchId != null && !createBySearchId.equals(userAdoptionReportDTO.getUserId())){
                return Collections.emptyList();
            }
        }

        voipDetailReportDTO.setReportType(VoipDetailReportType.TOTAL_CALL);

        try {
            List<VoipDetailReportVO> res = voipReportService.getVoipDetailReport(voipDetailReportDTO, pageable.getSort());

            Set<Long> talentIds = res.stream().map(VoipDetailReportVO::getTalentId).collect(Collectors.toSet());

            Map<Long, Long> map =companyService.getContactIdsByTalentIds(talentIds).getBody().stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getTalentId, TalentClientContactRelationDTO::getContactId));
            Set<Long> isContactTalentIds = map.keySet();

            return res.stream()
                    .filter(r -> UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())
                            ? !isContactTalentIds.contains(r.getTalentId())
                            : isContactTalentIds.contains(r.getTalentId()))
                    .map(c -> {
                        if (UserAdoptionReportSearchType.CONTACT.equals(userAdoptionReportDTO.getType())) {
                            c.setContactId(map.getOrDefault(c.getTalentId(), null));
                        }
                        return c;
                    })
                    .collect(Collectors.toList());
        } catch (ExecutionException | InterruptedException e) {
            log.error("[UserAdoptionReportService: getCallDetailReport] error when call voipReportService.getVoipDetailReport, error msg: {}, with voipDetailReportDTO: {}", e.getMessage(), voipDetailReportDTO, e);
            throw new CustomParameterizedException("[UserAdoptionReportService: getCallDetailReport] error: {}", e.getMessage());
        }
    }


    @Override
    public void downloadCallDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        List<VoipDetailReportVO> res = self.getCallDetailReport(userAdoptionReportDTO, Pageable.unpaged());

        Map<Long, EnumVoipCallType> callTypeMap = enumCommonService.findAllEnumVoipCallType().stream().collect(Collectors.toMap(EnumVoipCallType::getId, Function.identity()));
        Map<Long, EnumVoipCallResult> callResultMap = enumCommonService.findAllEnumVoipCallResult().stream().collect(Collectors.toMap(EnumVoipCallResult::getId, Function.identity()));

//        Map<Long, UserBriefDTO> userMap = userMapFuture.join();

        List<VoipDetailReportExcelVO> list = new ArrayList<>();
        res.forEach(vo -> {
            try {
                VoipDetailReportExcelVO excelVO = new VoipDetailReportExcelVO();
                excelVO.setTalentName(vo.getTalentName());
                excelVO.setContactName(vo.getTalentName());
                excelVO.setCallType(callTypeMap.getOrDefault(vo.getCallType(), new EnumVoipCallType()).getEnDisplay());
                excelVO.setCallResult(callResultMap.getOrDefault(vo.getCallResult(), new EnumVoipCallResult()).getEnDisplay());
                excelVO.setNote(vo.getNote());
                excelVO.setAISummary(Objects.nonNull(vo.getAISummary()) ? vo.getAISummary().toStringPretty() : null);
                excelVO.setJobTitle(vo.getJobTitle() + " (" + vo.getJobId() + ")");

                LocalDateTime dateTime = LocalDateTime.ofInstant(vo.getDialingTime(), ZoneId.of(StringUtils.firstNonEmpty(userAdoptionReportDTO.getTimeZone(), "UTC")));
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                excelVO.setDialingTime(dateTime.format(formatter));

                excelVO.setTalkMinutes(vo.getTalkMinutes().toString());
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(excelVO);
                }

                list.add(excelVO);
            } catch (Exception e) {
                log.error("downloadCallDetailReport error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });

        LocalDate utcDate = LocalDate.now(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = utcDate.format(formatter);

        List<String> removeList = new ArrayList<>();
        if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())) {
            removeList.add("contactName");
        } else {
            removeList.add("talentName");
        }
        ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(VoipDetailReportExcelVO.class, removeList), ExcelUtil.convertToMap(list, VoipDetailReportExcelVO.class, removeList), "",  "Calls " + formattedDate + ".xlsx", false);

    }

    @Override
    public Map<Long, UserAdoptionReportApplicationStatsDTO> getApplicationStatsReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds) {
        RecruitingKpiReportSearchDto recruitingKpiReportSearchDto = new RecruitingKpiReportSearchDto();
        recruitingKpiReportSearchDto.setE5ReportFlag(true);
        recruitingKpiReportSearchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
        recruitingKpiReportSearchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
        recruitingKpiReportSearchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
        recruitingKpiReportSearchDto.setDateType(RecruitingKpiDateType.ADD);
        recruitingKpiReportSearchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
        recruitingKpiReportSearchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
        recruitingKpiReportSearchDto.setUser(new SearchUserDto(null, null, null, null, true,null));
        if (CollectionUtils.isNotEmpty(userIds)) {
            recruitingKpiReportSearchDto.setUserIdList(List.copyOf(userIds));
        }
        List<RecruitingKpiByUserVO> res = recruitingKpiService.searchRecruitingKpiReportByUser(recruitingKpiReportSearchDto);

        return res.stream().map(a -> new UserAdoptionReportApplicationStatsDTO(a.getUserId(),null, a.getSubmitToJobNum(), a.getInterviewNum(), a.getUniqueInterviewTalentNum(), a.getOnboardNum(), a.getNoteCount(), a.getUniqueTalentIds())).collect(Collectors.toMap(UserAdoptionReportApplicationStatsDTO::getUserId, Function.identity()));
    }

    @Override
    public Map<Long, UserAdoptionReportApplicationStatsDTO> getApplicationStatsReportV2(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds) {
        RecruitingKpiReportSearchDto recruitingKpiReportSearchDto = new RecruitingKpiReportSearchDto();
        recruitingKpiReportSearchDto.setSearchTenantId(userAdoptionReportDTO.getSearchTenantId());
        recruitingKpiReportSearchDto.setSearchUserId(userAdoptionReportDTO.getSearchUserId());
        recruitingKpiReportSearchDto.setXxlJobFlag(true);
        recruitingKpiReportSearchDto.setE5ReportFlag(true);
        recruitingKpiReportSearchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
        recruitingKpiReportSearchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
        recruitingKpiReportSearchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
        recruitingKpiReportSearchDto.setDateType(RecruitingKpiDateType.ADD);
        recruitingKpiReportSearchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
        recruitingKpiReportSearchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
        recruitingKpiReportSearchDto.setUser(new SearchUserDto(null, null, null, null, true,null));
        if (CollectionUtils.isNotEmpty(userIds)) {
            recruitingKpiReportSearchDto.setUserIdList(List.copyOf(userIds));
        }
        List<RecruitingKpiByUserVO> res = recruitingKpiService.searchRecruitingKpiReportByUser(recruitingKpiReportSearchDto);

        return res.stream().map(a -> new UserAdoptionReportApplicationStatsDTO(a.getUserId(),null, a.getSubmitToJobNum(), a.getInterviewNum(), a.getUniqueInterviewTalentNum(), a.getOnboardNum(), a.getNoteCount(), a.getUniqueTalentIds())).collect(Collectors.toMap(UserAdoptionReportApplicationStatsDTO::getUserId, Function.identity()));
    }

    @Override
    public Map<Long, UserAdoptionReportApplicationStatsDTO> getApplicationStatsReportByTeamView(UserAdoptionReportDTO userAdoptionReportDTO) {
        RecruitingKpiReportSearchDto recruitingKpiReportSearchDto = new RecruitingKpiReportSearchDto();
        recruitingKpiReportSearchDto.setE5ReportFlag(true);
        recruitingKpiReportSearchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
        recruitingKpiReportSearchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
        recruitingKpiReportSearchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
        recruitingKpiReportSearchDto.setDateType(RecruitingKpiDateType.ADD);
        recruitingKpiReportSearchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TEAM));
        recruitingKpiReportSearchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
        if (CollectionUtils.isNotEmpty(userAdoptionReportDTO.getTeamIdList())) {
            recruitingKpiReportSearchDto.setTeamIdList(userAdoptionReportDTO.getTeamIdList());
        }
        if(CollectionUtils.isNotEmpty(userAdoptionReportDTO.getUserIdList())){
            recruitingKpiReportSearchDto.setUserIdList(userAdoptionReportDTO.getUserIdList());
        }

        List<RecruitingKpiByUserVO> res = recruitingKpiService.searchRecruitingKpiReportByUser(recruitingKpiReportSearchDto);

        return res.stream().map(a -> new UserAdoptionReportApplicationStatsDTO(null,a.getTeamId(), a.getSubmitToJobNum(), a.getInterviewNum(), a.getUniqueInterviewTalentNum(), a.getOnboardNum(), a.getNoteCount(), a.getUniqueTalentIds())).collect(Collectors.toMap(UserAdoptionReportApplicationStatsDTO::getTeamId, Function.identity()));
    }

    @Override
    public List<RecruitingKpiByUserVO> getApplicationStatsReportForTotal(UserAdoptionReportDTO userAdoptionReportDTO) {
        RecruitingKpiReportSearchDto recruitingKpiReportSearchDto = new RecruitingKpiReportSearchDto();
        recruitingKpiReportSearchDto.setE5ReportFlag(true);
        recruitingKpiReportSearchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
        recruitingKpiReportSearchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
        recruitingKpiReportSearchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
        recruitingKpiReportSearchDto.setDateType(RecruitingKpiDateType.ADD);
        recruitingKpiReportSearchDto.setGroupByFieldList(List.of(userAdoptionReportDTO.getReportGroupByField()));
        recruitingKpiReportSearchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
        if (CollectionUtils.isNotEmpty(userAdoptionReportDTO.getTeamIdList())) {
            recruitingKpiReportSearchDto.setTeamIdList(userAdoptionReportDTO.getTeamIdList());
        }
        if(CollectionUtils.isNotEmpty(userAdoptionReportDTO.getUserIdList())){
            recruitingKpiReportSearchDto.setUserIdList(userAdoptionReportDTO.getUserIdList());
        }

        List<RecruitingKpiByUserVO> res = recruitingKpiService.searchRecruitingKpiReportByUser(recruitingKpiReportSearchDto);
        return res;
    }


    @Override
    public Page<? extends RecruitingKpiApplicationBaseDetailVO> getApplicationDetailsReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, ReportApplicationStatus applicationDetailReportType, Pageable pageable) {
        RecruitingKpiApplicationDetailSearchDto searchDto = new RecruitingKpiApplicationDetailSearchDto();
        searchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
        searchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
        searchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
        searchDto.setDateType(RecruitingKpiDateType.ADD);
        //searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
        searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);//TODO: double check
        if (userAdoptionReportDTO.getViewType() == E5ReportViewType.TEAM){
            searchDto.setTeamId(userAdoptionReportDTO.getTeamId());
        } else {
            searchDto.setUserId(userAdoptionReportDTO.getUserId());
        }
        searchDto.setUserIdList(userAdoptionReportDTO.getUserIdList());
        searchDto.setTeamIdList(userAdoptionReportDTO.getTeamIdList());
        searchDto.setReportApplicationStatus(applicationDetailReportType);

        if (Objects.nonNull(userAdoptionReportDTO.getSearch())) {
            searchDto.setDetail(userAdoptionReportDTO.getSearch());
        }

        //kpi report page从1开始
        if (Objects.nonNull(pageable)) {
            pageable = PageRequest.of(pageable.getPageNumber() + 1, pageable.getPageSize(), pageable.getSort());
        }

        Sort.Order firstOrder = pageable.getSort().stream().findFirst().orElse(null);
        if (Objects.nonNull(firstOrder)) {
            SearchSortDTO sortDTO = new SearchSortDTO();
            sortDTO.setDirection(firstOrder.getDirection().name());
            sortDTO.setProperty(firstOrder.getProperty());
            searchDto.setSort(sortDTO);
        }

        return recruitingKpiByUserService.searchApplicationDetailPage(searchDto, pageable, true);
    }

    @Override
    public void downloadApplicationDetailsReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, ReportApplicationStatus applicationDetailReportType, HttpServletResponse response) {
        RecruitingKpiApplicationDetailSearchDto searchDto = new RecruitingKpiApplicationDetailSearchDto();
        searchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
        searchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
        searchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
        searchDto.setDateType(RecruitingKpiDateType.ADD);
        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
        searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL); //TODO: double check
        if (userAdoptionReportDTO.getViewType() == E5ReportViewType.TEAM){
            searchDto.setTeamId(userAdoptionReportDTO.getTeamId());
        } else {
            searchDto.setUserId(userAdoptionReportDTO.getUserId());
        }
        searchDto.setReportApplicationStatus(applicationDetailReportType);

        if (Objects.nonNull(userAdoptionReportDTO.getSearch())) {
            searchDto.setDetail(userAdoptionReportDTO.getSearch());
        }

        searchDto.setDownloadExcelTimezone(userAdoptionReportDTO.getTimeZone());

        LocalDate utcDate = LocalDate.now(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = utcDate.format(formatter);
        String prefix = null;
        switch (applicationDetailReportType) {
            case SUBMIT_TO_JOB -> prefix = "Submit to Job ";
            case INTERVIEW -> prefix = "Interview ";
            case ON_BOARD -> prefix = "On Board ";
        }
        String filename = prefix + formattedDate + ".xlsx";

        recruitingKpiByUserService.exportApplicationDetailList(searchDto, response, filename);
    }

    @Override
    public Page<?> getNoteDetailReport(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable) {
        if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())) {
            RecruitingKpiTalentNoteDetailSearchDto searchDto = new RecruitingKpiTalentNoteDetailSearchDto();
            searchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
            searchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
            searchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
            searchDto.setUserIdList(userAdoptionReportDTO.getUserIdList());
            searchDto.setTeamIdList(userAdoptionReportDTO.getTeamIdList());
            searchDto.setDateType(RecruitingKpiDateType.ADD);
            if (E5ReportViewType.TEAM.equals(userAdoptionReportDTO.getViewType())){
                searchDto.setTeamId(userAdoptionReportDTO.getTeamId());
                searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TEAM));
            }else {
                searchDto.setUserId(userAdoptionReportDTO.getUserId());
                searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
            }
            TalentNoteDetailSearchDto searchDetail = new TalentNoteDetailSearchDto();
            if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNotEmpty(userAdoptionReportDTO.getSearch().getCreatedBy())){
                String createBySearch = userAdoptionReportDTO.getSearch().getCreatedBy();
                searchDetail.setCreatedBy(Long.valueOf(createBySearch));
            }
            searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
            if (Objects.nonNull(userAdoptionReportDTO.getNoteType())) {
                searchDto.setTalentNoteType(userAdoptionReportDTO.getNoteType());
            }
            if (!StringUtils.isAllBlank(userAdoptionReportDTO.getFullName(), userAdoptionReportDTO.getTitle(), userAdoptionReportDTO.getNote())) {

                if (StringUtils.isNotEmpty(userAdoptionReportDTO.getFullName())) {
                    searchDetail.setFullName(userAdoptionReportDTO.getFullName());
                }
                if (StringUtils.isNotEmpty(userAdoptionReportDTO.getTitle())) {
                    searchDetail.setTitle(userAdoptionReportDTO.getTitle());
                }
                if (StringUtils.isNotEmpty(userAdoptionReportDTO.getNote())) {
                    searchDetail.setNote(userAdoptionReportDTO.getNote());
                }
            }
            searchDto.setDetail(searchDetail);
            //kpi report page从1开始
            Pageable searchPageable = null;
            if (Objects.nonNull(pageable)) {
                searchPageable = PageRequest.of(pageable.getPageNumber() + 1, pageable.getPageSize(), pageable.getSort());
            }

            Sort.Order firstOrder = pageable.getSort().stream().findFirst().orElse(null);
            if (Objects.nonNull(firstOrder)) {
                SearchSortDTO sortDTO = new SearchSortDTO();
                sortDTO.setDirection(firstOrder.getDirection().name());
                sortDTO.setProperty(firstOrder.getProperty());
                searchDto.setSort(sortDTO);
            }

            Page<RecruitingKpiTalentNoteDetailVO> res = recruitingKpiByUserService.searchTalentNoteDetailPage(searchDto, searchPageable, true);
            List<TalentNoteDetailReportVO> convertedList = res.getContent().stream().map(parent -> {
                TalentNoteDetailReportVO vo = new TalentNoteDetailReportVO();
                BeanUtils.copyProperties(parent, vo);
                vo.setAttendees(parent.getOriginalAdditionalInfoString());
                return vo;
            }).collect(Collectors.toList());
            return new PageImpl<>(convertedList, pageable, res.getTotalElements());
        } else { // Contact Note
            CrmBDActivitySearchDetailDTO searchDto = new CrmBDActivitySearchDetailDTO();
            searchDto.setFromDate(userAdoptionReportDTO.getStartTime().toString());
            searchDto.setToDate(userAdoptionReportDTO.getEndTime().toString());
            searchDto.setTimeZone(userAdoptionReportDTO.getTimeZone());
            Long createBySearchId;
            if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNotEmpty(userAdoptionReportDTO.getSearch().getCreatedBy())){
                createBySearchId = Long.valueOf(userAdoptionReportDTO.getSearch().getCreatedBy());
            } else  {
                createBySearchId = null;
            }
            //判断是TEAM还是USER
            if (E5ReportViewType.TEAM.equals(userAdoptionReportDTO.getViewType())){
                HashSet<Long> teamIds = new HashSet<>(List.of(userAdoptionReportDTO.getTeamId()));
                Set<Long> teamUserIds = userService.getAllTeamUserIdsByPermissionTeamIdIn(teamIds).getBody();
                //重写逻辑
                Set<Long> realSearchUserIds;
                List<Long> userIdList = userAdoptionReportDTO.getUserIdList();
                List<Long> teamIdList = userAdoptionReportDTO.getTeamIdList();
                if(CollectionUtils.isEmpty(userIdList) && CollectionUtils.isEmpty(teamIdList)){
                    realSearchUserIds = teamUserIds;
                } else if(CollectionUtils.isNotEmpty(userIdList) && CollectionUtils.isEmpty(teamIdList)){
                    realSearchUserIds = userIdList.stream().filter(userId -> teamUserIds.contains(userId)).collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(realSearchUserIds)){
                        return Page.empty();
                    }
                } else if(CollectionUtils.isEmpty(userIdList) && CollectionUtils.isNotEmpty(teamIdList)){
                    if (teamIdList.contains(userAdoptionReportDTO.getTeamId())){
                        realSearchUserIds = teamUserIds;
                    }else {
                        return Page.empty();
                    }
                }else {
                    if (teamIdList.contains(userAdoptionReportDTO.getTeamId())){
                        realSearchUserIds = teamUserIds;
                    }else {
                        realSearchUserIds = userIdList.stream().filter(userId -> teamUserIds.contains(userId)).collect(Collectors.toSet());
                        if (CollectionUtils.isEmpty(realSearchUserIds)){
                            return Page.empty();
                        }
                    }
                }
                if(createBySearchId != null){
                    if(realSearchUserIds.contains(createBySearchId)){
                        searchDto.setUserIdList(List.of(createBySearchId));
                    }else {
                        return Page.empty();
                    }
                }else {
                    searchDto.setUserIdList(List.copyOf(realSearchUserIds));
                }
            }else {
                searchDto.setUserIdList(List.of(userAdoptionReportDTO.getUserId()));
                if (createBySearchId != null && !createBySearchId.equals(userAdoptionReportDTO.getUserId())){
                    return Page.empty();
                }
            }
            searchDto.setBusinessTypes(Arrays.asList("LEAD_SERVICE", "ACCOUNT_SERVICE"));

            String[] response = searchCrmNoteDetail(searchDto, pageable);

            String total = response[1];

            List<CrmBDActivityReportDetailVO> res = JSONUtil.toList(JSONUtil.parseArray(response[0]), CrmBDActivityReportDetailVO.class);

            return new PageImpl<>(res, pageable, Long.valueOf(total));

        }
    }

    @Override
    public void downloadNoteDetailReport(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())) {
            RecruitingKpiTalentNoteDetailSearchDto searchDto = new RecruitingKpiTalentNoteDetailSearchDto();
            searchDto.setStartDate(userAdoptionReportDTO.getStartTime().toString());
            searchDto.setEndDate(userAdoptionReportDTO.getEndTime().toString());
            searchDto.setTimezone(userAdoptionReportDTO.getTimeZone());
            searchDto.setDateType(RecruitingKpiDateType.ADD);
            if (E5ReportViewType.TEAM.equals(userAdoptionReportDTO.getViewType())){
                searchDto.setTeamId(userAdoptionReportDTO.getTeamId());
                searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TEAM));
            }else {
                searchDto.setUserId(userAdoptionReportDTO.getUserId());
                searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
            }
            TalentNoteDetailSearchDto searchDetail = new TalentNoteDetailSearchDto();
            if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNotEmpty(userAdoptionReportDTO.getSearch().getCreatedBy())){
                String createBySearch = userAdoptionReportDTO.getSearch().getCreatedBy();
                searchDetail.setCreatedBy(Long.valueOf(createBySearch));
            }
            searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
            if (Objects.nonNull(userAdoptionReportDTO.getNoteType())) {
                searchDto.setTalentNoteType(userAdoptionReportDTO.getNoteType());
            }
            if (!StringUtils.isAllBlank(userAdoptionReportDTO.getFullName(),
                    userAdoptionReportDTO.getTitle(),
                    userAdoptionReportDTO.getNote(),
                    userAdoptionReportDTO.getCreatedBy())) {
                if (StringUtils.isNotEmpty(userAdoptionReportDTO.getFullName())) {
                    searchDetail.setFullName(userAdoptionReportDTO.getFullName());
                }
                if (StringUtils.isNotEmpty(userAdoptionReportDTO.getTitle())) {
                    searchDetail.setTitle(userAdoptionReportDTO.getTitle());
                }
                if (StringUtils.isNotEmpty(userAdoptionReportDTO.getNote())) {
                    searchDetail.setNote(userAdoptionReportDTO.getNote());
                }
                if (StringUtils.isNotEmpty(userAdoptionReportDTO.getCreatedBy())){
                    searchDetail.setCreatedBy(Long.valueOf(userAdoptionReportDTO.getCreatedBy()));
                }
            }
            searchDto.setDetail(searchDetail);
            searchDto.setDownloadExcelTimezone(userAdoptionReportDTO.getTimeZone());

            LocalDate utcDate = LocalDate.now(ZoneId.of("UTC"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = utcDate.format(formatter);

            recruitingKpiByUserService.exportTalentNoteDetailList(searchDto, response, "Notes " + formattedDate + ".xlsx", true);

        } else { //Contact Note
            StopWatch stopWatch = new StopWatch("exportTalentNoteDetailList");
            stopWatch.start("1. search data");
            LocalDate utcDate = LocalDate.now(ZoneId.of("UTC"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = utcDate.format(formatter);
            CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();

            CrmBDActivitySearchDetailDTO searchDto = new CrmBDActivitySearchDetailDTO();
            searchDto.setFromDate(userAdoptionReportDTO.getStartTime().toString());
            searchDto.setToDate(userAdoptionReportDTO.getEndTime().toString());
            searchDto.setTimeZone(userAdoptionReportDTO.getTimeZone());
            //判断是TEAM还是USER
            if (E5ReportViewType.TEAM.equals(userAdoptionReportDTO.getViewType())){
                ResponseEntity<List<UserBriefDTO>> res = userService.findAllActivatedBriefByTeamId(userAdoptionReportDTO.getTeamId());
                List<UserBriefDTO> list = res.getBody();
                if (CollectionUtils.isEmpty(list)){
                    ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(CrmBDActivityReportDetailExcelVO.class, new ArrayList<>()), null, "",  "Notes " + formattedDate + ".xlsx", false);
                }
                searchDto.setUserIdList(list.stream().map(UserBriefDTO::getId).collect(Collectors.toList()));
            }else {
                searchDto.setUserId(userAdoptionReportDTO.getUserId());
            }
            searchDto.setBusinessTypes(Arrays.asList("LEAD_SERVICE", "ACCOUNT_SERVICE"));

            Pageable pageable = PageRequest.of(0, 10000);

            String[] res = searchCrmNoteDetail(searchDto, pageable);

            List<CrmBDActivityReportDetailVO> r = JSONUtil.toList(JSONUtil.parseArray(res[0]), CrmBDActivityReportDetailVO.class);

            //根据用户id查询用户名称，map
            if(CollectionUtils.isEmpty(r)){
                ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(CrmBDActivityReportDetailExcelVO.class, new ArrayList<>()), null, "",  "Notes " + formattedDate + ".xlsx", false);
            }

            //TODO: convert to excel
            Map<Integer, EnumCompanyServiceType> serviceTypeMap = enumCommonService.findAllEnumCompanyServiceType(SecurityUtils.getTenantId()).stream().collect(Collectors.toMap(EnumCompanyServiceType::getId, Function.identity()));

            Map<Long, UserBriefDTO> userMap = userMapFuture.join();

            List<CrmBDActivityReportDetailExcelVO> list = new ArrayList<>();
            r.forEach(vo -> {
                try {
                    CrmBDActivityReportDetailExcelVO excelVO = new CrmBDActivityReportDetailExcelVO();
                    UserBriefDTO userBriefDTO = userMap.get(vo.getCreatedBy());
                    if (userBriefDTO != null) {
                        String fullName = CommonUtils.formatFullName(userBriefDTO.getFirstName(), userBriefDTO.getLastName());
                        excelVO.setCreatedByName(fullName);
                    }
                    excelVO.setCompanyName(vo.getCompanyName());
                    excelVO.setNote(HtmlUtils.htmlToPlainText(vo.getNote()));
                    excelVO.setStage(vo.getModel().equals("ACCOUNT_SERVICE") ? "Accounts" : "Leads");
                    String accountContactsStr = vo.getAccountContacts() == null ? null :
                            StringUtils.join(vo.getAccountContacts().values(), ",");
                    excelVO.setClientContact(accountContactsStr);

                    ZonedDateTime dateTime = ZonedDateTime.parse(vo.getContactTime());
                    ZonedDateTime converted = dateTime.withZoneSameInstant(ZoneId.of(userAdoptionReportDTO.getTimeZone()));
                    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    excelVO.setContactTime(converted.format(outputFormatter));

                    String serviceTypes = Optional.ofNullable(vo.getServiceTypeIds()).map(ids -> ids.stream().map(id ->
                        serviceTypeMap.get(id).getEnDisplay()
                    ).collect(Collectors.joining(",")))
                            .orElse(null);
                    excelVO.setServiceType(serviceTypes);

                    String salesLeadOwners = Optional.ofNullable(vo.getSalesLeadOwnerIds()).map(ids -> ids.stream().map(id -> getUserNameByMap(id, userMap)).collect(Collectors.joining(","))).orElse(null);
                    excelVO.setSalesLeadOwners(salesLeadOwners);

                    String bdOwners = Optional.ofNullable(vo.getBdOwnerIds()).map(ids -> ids.stream().map(id -> getUserNameByMap(id, userMap)).collect(Collectors.joining(","))).orElse(null);
                    excelVO.setBdOwners(bdOwners);

                    list.add(excelVO);
                } catch (Exception e) {
                    log.error("downloadNoteDetailReport error {} ", ExceptionUtil.getAllExceptionMsg(e));
                }
            });
            stopWatch.stop();

            stopWatch.start("3. 输出 excel");

            ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(CrmBDActivityReportDetailExcelVO.class, new ArrayList<>()), ExcelUtil.convertToMap(list, CrmBDActivityReportDetailExcelVO.class, new ArrayList<>()), "",  "Notes " + formattedDate + ".xlsx", false);

        }
    }

    @Override
    public void downloadNoteDetailReportV2(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response, Pageable pageable) {
        PageRequest pageRequest = PageRequest.of(0, Integer.MAX_VALUE, pageable.getSort());
        Page<?> noteDetailReport = this.getNoteDetailReport(userAdoptionReportDTO, pageRequest);
        LocalDate utcDate = LocalDate.now(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = utcDate.format(formatter);
        if (noteDetailReport.getContent() != null && !noteDetailReport.getContent().isEmpty()) {
            CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
            if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())){
                Map<Integer, EnumMotivation> enumMotivationMap = enumMotivationService.findAllEnumMotivation().stream().collect(Collectors.toMap(EnumMotivation::getId, a -> a));
                List<TalentNoteDetailReportVO> voList = (List<TalentNoteDetailReportVO>) noteDetailReport.getContent();
                Map<Long, UserBriefDTO> userMap = userMapFuture.join();
                List<RecruitingKpiTalentNoteDetailExcelENVO> list = new ArrayList<>();
                voList.forEach(vo -> {
                    try {
                        RecruitingKpiTalentNoteDetailExcelENVO detailVo = new RecruitingKpiTalentNoteDetailExcelENVO();
                        BeanUtil.copyProperties(vo, detailVo, true);
                        setCreatedAndLastModified(vo.getCreatedBy(), vo.getCreatedDate(), vo.getLastModifiedBy(), vo.getLastModifiedDate(), StringUtils.firstNonBlank(userAdoptionReportDTO.getTimeZone(), userAdoptionReportDTO.getTimeZone()), detailVo, userMap);
                        setJobSearchStatusDisplay(enumMotivationMap, vo.getJobSearchStatus(), detailVo, null);
                        String attendees = vo.getAttendeeIds().stream().map(userId -> userMap.getOrDefault((userId), new UserBriefDTO()).getFullName())
                                .filter(StrUtil::isNotBlank)
                                .collect(Collectors.joining(","));
                        detailVo.setNoteType(vo.getNoteType() == null ? "" : vo.getNoteType().getEnDisplay());
                        detailVo.setAttendees(attendees);
                        if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                            ExcelUtil.maskConfidentialTalentData(detailVo);
                        }
                        list.add(detailVo);
                    } catch (Exception e) {
                        log.error("exportTalentDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
                    }
                });
                List<String> removeHeaderList = new ArrayList<>();
                removeHeaderList.add("jobSearchStatusDisplay");
                removeHeaderList.add("lastModifiedDateFormat");
                removeHeaderList.add("additionalInfo");
                ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(RecruitingKpiTalentNoteDetailExcelENVO.class, removeHeaderList), ExcelUtil.convertToMap(list, RecruitingKpiTalentNoteDetailExcelENVO.class, removeHeaderList) ,"",  "Notes " + formattedDate + ".xlsx", false);
            } else if (UserAdoptionReportSearchType.CONTACT.equals(userAdoptionReportDTO.getType())){
                List<CrmBDActivityReportDetailVO> r = (List<CrmBDActivityReportDetailVO>) noteDetailReport.getContent();
                Map<Integer, EnumCompanyServiceType> serviceTypeMap = enumCommonService.findAllEnumCompanyServiceType(SecurityUtils.getTenantId()).stream().collect(Collectors.toMap(EnumCompanyServiceType::getId, Function.identity()));
                Map<Long, UserBriefDTO> userMap = userMapFuture.join();
                List<CrmBDActivityReportDetailExcelVO> list = new ArrayList<>();
                r.forEach(vo -> {
                    try {
                        CrmBDActivityReportDetailExcelVO excelVO = new CrmBDActivityReportDetailExcelVO();
                        UserBriefDTO userBriefDTO = userMap.get(vo.getCreatedBy());
                        if (userBriefDTO != null) {
                            String fullName = CommonUtils.formatFullName(userBriefDTO.getFirstName(), userBriefDTO.getLastName());
                            excelVO.setCreatedByName(fullName);
                        }
                        excelVO.setCompanyName(vo.getCompanyName());
                        excelVO.setNote(HtmlUtils.htmlToPlainText(vo.getNote()));
                        excelVO.setStage(vo.getModel().equals("ACCOUNT_SERVICE") ? "Accounts" : "Leads");
                        String accountContactsStr = vo.getAccountContacts() == null ? null :
                                StringUtils.join(vo.getAccountContacts().values(), ",");
                        excelVO.setClientContact(accountContactsStr);

                        ZonedDateTime dateTime = ZonedDateTime.parse(vo.getContactTime());
                        ZonedDateTime converted = dateTime.withZoneSameInstant(ZoneId.of(userAdoptionReportDTO.getTimeZone()));
                        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        excelVO.setContactTime(converted.format(outputFormatter));

                        String serviceTypes = Optional.ofNullable(vo.getServiceTypeIds()).map(ids -> ids.stream().map(id ->
                                        serviceTypeMap.get(id).getEnDisplay()
                                ).collect(Collectors.joining(",")))
                                .orElse(null);
                        excelVO.setServiceType(serviceTypes);

                        String salesLeadOwners = Optional.ofNullable(vo.getSalesLeadOwnerIds()).map(ids -> ids.stream().map(id -> getUserNameByMap(id, userMap)).collect(Collectors.joining(","))).orElse(null);
                        excelVO.setSalesLeadOwners(salesLeadOwners);

                        String bdOwners = Optional.ofNullable(vo.getBdOwnerIds()).map(ids -> ids.stream().map(id -> getUserNameByMap(id, userMap)).collect(Collectors.joining(","))).orElse(null);
                        excelVO.setBdOwners(bdOwners);

                        list.add(excelVO);
                    } catch (Exception e) {
                        log.error("downloadNoteDetailReport error {} ", ExceptionUtil.getAllExceptionMsg(e));
                    }
                });
                ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(CrmBDActivityReportDetailExcelVO.class, new ArrayList<>()), ExcelUtil.convertToMap(list, CrmBDActivityReportDetailExcelVO.class, new ArrayList<>()), "",  "Notes " + formattedDate + ".xlsx", false);
            }
        }else {
            if (noteDetailReport.getContent() != null && !noteDetailReport.getContent().isEmpty()) {
                ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(RecruitingKpiTalentNoteDetailExcelENVO.class, new ArrayList<>()), null, "",  "Notes " + formattedDate + ".xlsx", false);
            }else if(UserAdoptionReportSearchType.CONTACT.equals(userAdoptionReportDTO.getType())){
                ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(CrmBDActivityReportDetailExcelVO.class, new ArrayList<>()), null, "",  "Notes " + formattedDate + ".xlsx", false);
            }
        }
    }

    private static Comparator<EmailDetailReportVO> getEmailDetailReportVOComparatorFromSort(Sort sort) {
        return sort.stream()
                .map(UserAdoptionReportDetailsServiceImpl::getEmailDetailReportVOComparator)
                .filter(Objects::nonNull)
                .reduce(Comparator::thenComparing)
                .orElse((u1, u2) -> 0); // 如果没有排序规则，则不变
    }

    private static Comparator<EmailDetailReportVO> getEmailDetailReportVOComparator(Sort.Order order) {
        Comparator<EmailDetailReportVO> comparator = null;

        switch (order.getProperty().toLowerCase()) {
            case "name" -> {
                Collator collator = Collator.getInstance(Locale.CHINA); // 中文拼音排序
                comparator = Comparator.comparing(
                        EmailDetailReportVO -> EmailDetailReportVO.getRecipients().get(0).getName(),
                        Comparator.nullsLast(collator)
                );
            }
            case "senttime" -> comparator = Comparator.comparing(EmailDetailReportVO::getSentTime, getNullHandlingComparator(order));
        }
        return (comparator != null && order.isDescending()) ? comparator.reversed() : comparator;
    }

    private static <T extends Comparable<T>> Comparator<T> getNullHandlingComparator(Sort.Order order) {
        return Comparator.nullsLast(Comparator.naturalOrder());
    }

    @Override
    public List<EmailDetailReportVO> getEmailDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO,Pageable pageable) {
        MailSearchDTO mailSearchDTO = getMailSearchDTO(userAdoptionReportDTO);
        if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())) {
            List<EmailLogOverviewDTO> details = emailService.searchSentEmailsAndCountReplied(mailSearchDTO).getBody();

            Set<Long> talentIds = new HashSet<>();
            details.forEach(e -> {

                List<Long> ids = extractTalentIds(e.getCustomerId2());
                e.setTalentIds(ids);

                talentIds.addAll(ids);
            });

            Set<TalentBriefVO> talents = talentService.findBriefTalentsByTalentIds(talentIds).getBody();
            Map<Long, TalentBriefVO> talentMap = talents.stream().collect(Collectors.toMap(TalentBriefVO::getId, Function.identity()));

            Set<Long> confidentialTalentIds = talentFeignClient.filterConfidentialTalentViewAble(talentIds).getBody();
            Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentFeignClient.getTalentConfidentialInfo(talentIds).getBody();

            String nameSearch;
            if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNoneBlank(userAdoptionReportDTO.getSearch().getName())) {
                nameSearch = userAdoptionReportDTO.getSearch().getName().toLowerCase(Locale.ROOT);
            } else {
                nameSearch = null;
            }

            Long createBySearch;
            if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNoneBlank(userAdoptionReportDTO.getSearch().getCreatedBy())) {
                createBySearch = Long.valueOf(userAdoptionReportDTO.getSearch().getCreatedBy());
            } else {
                createBySearch = null;
            }

            List<EmailDetailReportVO> res = new ArrayList<>(details.size());
            details.stream().forEach(e -> {
                EmailDetailReportVO vo = new EmailDetailReportVO();
                vo.setCreatedBy(e.getUserId());
                vo.setSubject(e.getSubject());
                vo.setSentTime(e.getSentTime());
                vo.setConversationCount(e.getMessageCount());

                List<EmailRecipientVO> recipients = new ArrayList<>();
                e.getTalentIds().forEach(t -> {
                    EmailRecipientVO r = new EmailRecipientVO();
                    r.setId(t);
                    r.setName(talentMap.get(t).getFullName());
                    recipients.add(r);
                });
                vo.setRecipients(recipients);

                if (StringUtils.isEmpty(nameSearch) && Objects.isNull(createBySearch)){
                    res.add(vo);
                }else if (StringUtils.isNotEmpty(nameSearch) && Objects.nonNull(createBySearch)){
                    boolean existsContains = recipients.stream()
                            .map(EmailRecipientVO::getName)
                            .filter(Objects::nonNull)
                            .anyMatch(name -> name.toLowerCase(Locale.ROOT).contains(nameSearch));
                    if (createBySearch.equals(vo.getCreatedBy()) && existsContains) {
                        res.add(vo);
                    }
                }else if (StringUtils.isEmpty(nameSearch) && Objects.nonNull(createBySearch)){
                    if (createBySearch.equals(vo.getCreatedBy())) {
                        res.add(vo);
                    }
                }else if (StringUtils.isNotEmpty(nameSearch)&& Objects.isNull(createBySearch)){
                    boolean existsContains = recipients.stream()
                            .map(EmailRecipientVO::getName)
                            .filter(Objects::nonNull)
                            .anyMatch(name -> name.toLowerCase(Locale.ROOT).contains(nameSearch));
                    if (existsContains) {
                        res.add(vo);
                    }
                }else {
                    res.add(vo);
                }

            });
            //排序
            res.sort(getEmailDetailReportVOComparatorFromSort(pageable.getSort()));
            return res.stream()
                    .peek(vo -> {
                        List<EmailRecipientVO> recipients = vo.getRecipients();
                        if (recipients == null) {
                            return;
                        }
                        recipients.forEach(r -> {
                            if (confidentialInfoMap == null || !confidentialInfoMap.containsKey(r.getId())) {
                                return;
                            }
                            if (confidentialTalentIds != null && !confidentialTalentIds.contains(r.getId())) {
                                r.setName(null);
                                r.setConfidentialTalentViewAble(false);
                            } else {
                                r.setConfidentialTalentViewAble(true);
                            }
                        });
                        if (recipients.stream().allMatch(r -> r.getConfidentialTalentViewAble() != null && !r.getConfidentialTalentViewAble())) {
                            vo.setConversationCount(null);
                            vo.setSentTime(null);
                            vo.setSubject(null);
                        }
                    })
                    .collect(Collectors.toList());
        } else {
            String response = getCrmEmailDetailReportWithCountReplied(mailSearchDTO);

            List<EmailDetailReportVO> list = JSON.parseArray(response, EmailDetailReportVO.class);
            if (CollectionUtils.isEmpty(list)){
                return new ArrayList<>();
            }
            list.forEach(o -> o.setCreatedBy(o.getUserId()));
            String nameSearch;
            if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNoneBlank(userAdoptionReportDTO.getSearch().getName())) {
                nameSearch = userAdoptionReportDTO.getSearch().getName().toLowerCase(Locale.ROOT);
            } else {
                nameSearch = null;
            }

            Long createBySearch;
            if (Objects.nonNull(userAdoptionReportDTO.getSearch()) && StringUtils.isNoneBlank(userAdoptionReportDTO.getSearch().getCreatedBy())) {
                createBySearch = Long.valueOf(userAdoptionReportDTO.getSearch().getCreatedBy());
            } else {
                createBySearch = null;
            }


            list = list.stream()
                    .filter(e -> {
                        if (StringUtils.isNotEmpty(nameSearch) && createBySearch == null) {
                            return e.getRecipients().stream()
                                    .map(EmailRecipientVO::getName)
                                    .filter(Objects::nonNull)
                                    .anyMatch(name -> name.toLowerCase(Locale.ROOT).contains(nameSearch));
                        } else if (!StringUtils.isNotEmpty(nameSearch) && Objects.nonNull(createBySearch)){
                            return createBySearch.equals(e.getCreatedBy());
                        } else if (StringUtils.isNotEmpty(nameSearch) && Objects.nonNull(createBySearch)){
                            return e.getRecipients().stream()
                                    .map(EmailRecipientVO::getName)
                                    .filter(Objects::nonNull)
                                    .anyMatch(name -> name.toLowerCase(Locale.ROOT).contains(nameSearch)) && createBySearch.equals(e.getCreatedBy());
                        }else {
                            return true;
                        }
                    }).collect(Collectors.toList());
            //排序
            list.sort(getEmailDetailReportVOComparatorFromSort(pageable.getSort()));
            return list;
        }

    }

    @Nullable
    private MailSearchDTO getMailSearchDTO(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO) {
        MailSearchDTO mailSearchDTO = new MailSearchDTO();
        mailSearchDTO.setFromDate(userAdoptionReportDTO.getStartTime());
        mailSearchDTO.setToDate(userAdoptionReportDTO.getEndTime());
        if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())){
            mailSearchDTO.setCustomerId2(List.of("APN:TALENT"));
        }else {
            mailSearchDTO.setCustomerId2(List.of("AC"));
        }
        mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
        if (E5ReportViewType.TEAM.equals(userAdoptionReportDTO.getViewType())){
            ResponseEntity<List<UserBriefDTO>> res = userService.findAllActivatedBriefByTeamId(userAdoptionReportDTO.getTeamId());
            List<UserBriefDTO> list = res.getBody();
            if (CollectionUtils.isEmpty(list)){
                return null;
            }
            List<Long> teamUserIds = list.stream().map(UserBriefDTO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty((userAdoptionReportDTO.getTeamIdList()))){
                //判断teamIdList是否包含teamId
                if (userAdoptionReportDTO.getTeamIdList().contains(userAdoptionReportDTO.getTeamId())){
                    mailSearchDTO.setUserIds(teamUserIds);
                }else {
                    //取交集
                    if (CollectionUtils.isNotEmpty(userAdoptionReportDTO.getUserIdList())){
                        List<Long> userIdsResult = teamUserIds.stream().filter(userAdoptionReportDTO.getUserIdList()::contains).collect(Collectors.toList());
                        mailSearchDTO.setUserIds(userIdsResult);
                    } else {
                        mailSearchDTO.setUserIds(list.stream().map(UserBriefDTO::getId).collect(Collectors.toList()));
                    }
                }
            }else {
                //取交集
                if (CollectionUtils.isNotEmpty(userAdoptionReportDTO.getUserIdList())){
                    List<Long> userIdsResult = teamUserIds.stream().filter(userAdoptionReportDTO.getUserIdList()::contains).collect(Collectors.toList());
                    mailSearchDTO.setUserIds(userIdsResult);
                } else {
                    mailSearchDTO.setUserIds(list.stream().map(UserBriefDTO::getId).collect(Collectors.toList()));
                }
            }

        }else {
            mailSearchDTO.setUserIds(List.of(userAdoptionReportDTO.getUserId()));
        }
        mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
        return mailSearchDTO;
    }

    private List<Long> extractTalentIds(String input) {
        String[] parts = StringUtils.split(input, ",");
        List<Long> talentIds = new ArrayList<>(parts.length);
        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("APN:TALENT")) {
                talentIds.add(Long.parseLong(part.substring("APN:TALENT".length())));
            } else if (part.startsWith("VOIPNOTICE:TALENT")) {
                talentIds.add(Long.parseLong(part.substring("VOIPNOTICE:TALENT".length())));
            }
        }
        return talentIds;
    }

    private String getCrmEmailDetailReportWithCountReplied(MailSearchDTO mailSearchDTO) {
        String searchCrmEmailDetailUrl = getCrmEmailDetailReportUrl();
//        String authorizationHeader = getAuthorizationHeader();
        String body = JsonUtil.toJson(mailSearchDTO);

        log.info("[UserAdoptionReportDetailsServiceImpl: getCrmEmailDetailReport] url: {}, authorizationHeader: {}, searchDTO: {}, body: {}", searchCrmEmailDetailUrl, SecurityUtils.getCurrentUserToken(), mailSearchDTO, body);
        cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createRequest(Method.POST, searchCrmEmailDetailUrl)
                .header("Authorization", TOKEN_TYPE + " " + SecurityUtils.getCurrentUserToken())
                .body(body).execute();

        log.info("[UserAdoptionReportDetailsServiceImpl: getCrmEmailDetailReport] response code : {}, response message: {}", response.getStatus(), response.body());
        int code = response.getStatus();
        if (HttpStatus.UNAUTHORIZED.value() == code) {
            log.error("[UserAdoptionReportDetailsServiceImpl: getCrmEmailDetailReport] No crm permission with response code: {}", HttpStatus.FORBIDDEN.value());
            throw new ExternalServiceInterfaceException("No crm permission", HttpStatus.FORBIDDEN.value());
        } else if (HttpStatus.OK.value() == code || HttpStatus.NOT_FOUND.value() == code) {
            return response.body();
        } else {
            if (response.getStatus() == cn.hutool.http.Status.HTTP_PRECON_FAILED) {
                JSONObject res = JSONUtil.parseObj(response.body());
                throw new WithDataException(res.getStr("message"), cn.hutool.http.Status.HTTP_PRECON_FAILED, res.get("data"));
            } else {
                throw new ExternalServiceInterfaceException(response.body(), response.getStatus());
            }
        }
    }

    private String getCrmEmailDetailReportUrl() {
        return applicationProperties.getCrmUrl() + CRM_EMAIL_DETAIL_REPORT;
    }

    @Override
    public void downloadEmailDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        List<EmailDetailReportVO> list = this.getEmailDetailReport(userAdoptionReportDTO,Pageable.unpaged());
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        Map<Long, UserBriefDTO> userMap = userMapFuture.join();
        List<EmailDetailReportExcelVO> res = list.stream().map(vo -> {
            EmailDetailReportExcelVO excelVO = new EmailDetailReportExcelVO();
            // 发送邮件所有的候选人都是当前用户没有查看权限的保密候选人时，隐藏所有信息
            if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType()) &&
                    vo.getRecipients().stream().allMatch(r -> r.getConfidentialTalentViewAble() != null && !r.getConfidentialTalentViewAble())) {
                excelVO.setCreatedByName("***");
                excelVO.setSubject("***");
                excelVO.setConversationCount(null);
                excelVO.setCandidateNames("***");
                excelVO.setSentTime(null);
            } else {
                excelVO.setSubject(vo.getSubject());
                excelVO.setConversationCount(vo.getConversationCount());

                ZoneId zone = ZoneId.of(StringUtils.firstNonBlank(userAdoptionReportDTO.getTimeZone(), "UTC"));
                ZonedDateTime zdt = vo.getSentTime().atZone(zone);
                DateTimeFormatter formatter =
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String sentTime = zdt.format(formatter);
                excelVO.setSentTime(sentTime);

                // 不是发送邮件所有的候选人都是当前用户没有查看权限的保密候选人时，只隐藏保密候选人名字
                String names = Optional.ofNullable(vo.getRecipients()).map(recipients ->
                        recipients.stream().map(v -> v.getConfidentialTalentViewAble() != null && !v.getConfidentialTalentViewAble() ? "***" : v.getName())
                                .collect(Collectors.joining(","))).orElse(null);
                excelVO.setContactNames(names);
                excelVO.setCandidateNames(names);
                UserBriefDTO userBriefDTO = userMap.get(vo.getCreatedBy());
                if (userBriefDTO != null) {
                    String fullName = CommonUtils.formatFullName(userBriefDTO.getFirstName(), userBriefDTO.getLastName());
                    excelVO.setCreatedByName(fullName);
                }
            }
            return excelVO;
        }).collect(Collectors.toList());

        List<String> removeList = new ArrayList<>();
        if (UserAdoptionReportSearchType.CANDIDATE.equals(userAdoptionReportDTO.getType())) {
            removeList.add("contactNames");
        } else {
            removeList.add("candidateNames");
        }

        LocalDate utcDate = LocalDate.now(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = utcDate.format(formatter);
        ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(EmailDetailReportExcelVO.class, removeList), ExcelUtil.convertToMap(res, EmailDetailReportExcelVO.class, removeList), "",  "Emails " + formattedDate + ".xlsx", false);
    }

    @Override
    public void downloadEmailDetailReportV2(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response, Pageable pageable) {

    }

    @Override
    public List<UserAdoptionReportCrmNoteStatsDTO> getCrmNoteReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds) {

        UserAdoptionReportCrmNoteSearchDTO searchDTO = new UserAdoptionReportCrmNoteSearchDTO();
        searchDTO.setFromDate(userAdoptionReportDTO.getStartTime().toString());
        searchDTO.setToDate(userAdoptionReportDTO.getEndTime().toString());
        searchDTO.setTimeZone(userAdoptionReportDTO.getTimeZone());
        searchDTO.setTargetType("USER");
        if (CollectionUtils.isNotEmpty(userIds)) {
            searchDTO.setTargetIds(userIds);
        }

        String response = searchCrmNote(searchDTO);

        List<UserAdoptionReportCrmNoteStatsDTO> res = JSONUtil.toList(JSONUtil.parseArray(response), UserAdoptionReportCrmNoteStatsDTO.class);

        return res;
    }

    @Override
    public Map<Long, List<UserAdoptionReportCrmNoteStatsDTO>> getCrmNoteReportByTeamView(UserAdoptionReportDTO userAdoptionReportDTO, List<TeamInfoVO> teamInfoVOS) {
        List<Long> userIds = teamInfoVOS.stream().map(TeamInfoVO::getUserId).collect(Collectors.toList());
        List<UserAdoptionReportCrmNoteStatsDTO> res = getCrmNoteReport(userAdoptionReportDTO, userIds);
        if(CollectionUtils.isEmpty(res)){
            return Collections.emptyMap();
        }
        Set<Long> contactIds = res.parallelStream()
                .flatMap(a -> a.getContactIds().stream())
                .collect(Collectors.toSet());
        List<TalentClientContactRelationDTO> relation = companyService.getTalentIdsByContactIds(contactIds).getBody();
        if (Objects.nonNull(relation)) {
            Map<Long, Long> contactToTalentMap = relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId));
            res.parallelStream().forEach(item -> {
                Set<Long> talentIds = item.getContactIds().stream()
                        .map(contactToTalentMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                item.setTalentIds(talentIds);
                item.setUniqueContactIdCount(item.getContactIds().size() - talentIds.size());
            });
        }
        res.stream().forEach( d -> {
            for (TeamInfoVO teamInfoVO : teamInfoVOS) {
                if (teamInfoVO.getUserId().equals(d.getId())) {
                    d.setTeamId(teamInfoVO.getTeamId());
                }
            }
        });
        return res.stream().collect(Collectors.groupingBy(UserAdoptionReportCrmNoteStatsDTO::getTeamId));
    }

    @Override
    public UserAdoptionReportCrmNoteStatsDTO getCrmNoteReportForTotal(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds) {
        UserAdoptionReportCrmNoteStatsDTO result = new UserAdoptionReportCrmNoteStatsDTO();
        Set<Long> talentIdList = new HashSet<>();
        AtomicReference<Integer> uniqueContactIdCount = new AtomicReference<>(0);
        AtomicReference<Long> noteCount = new AtomicReference<>(0L);

        List<UserAdoptionReportCrmNoteStatsDTO> res = getCrmNoteReport(userAdoptionReportDTO, userIds);
        Set<Long> contactIds = res.stream()
                .flatMap(a -> a.getContactIds().stream())
                .collect(Collectors.toSet());
        List<TalentClientContactRelationDTO> relation = companyService.getTalentIdsByContactIds(contactIds).getBody();
        if (Objects.nonNull(relation)) {
            Map<Long, Long> contactToTalentMap = relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId));
            res.stream().forEach(item -> {
                Set<Long> talentIds = item.getContactIds().stream()
                        .map(contactToTalentMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                noteCount.updateAndGet(v -> v + item.getNoteCount());
                talentIdList.addAll(talentIds);
                int i = item.getContactIds().size() - talentIds.size();
                uniqueContactIdCount.updateAndGet(v -> v + i);
            });
            result.setTalentIds(talentIdList);
            result.setUniqueContactIdCount(uniqueContactIdCount.get());
            result.setNoteCount(noteCount.get());
        }
        return result;
    }

    private String searchCrmNote(UserAdoptionReportCrmNoteSearchDTO searchDTO) {
        String searchCrmNoteUrl = getCrmNoteSearchUrl();
//        String authorizationHeader = getAuthorizationHeader();
        String body = JsonUtil.toJson(searchDTO);
        log.info("[UserAdoptionReportDetailsServiceImpl: searchCrmNote] url: {}, authorizationHeader: {}, searchDTO: {}, body: {}", searchCrmNoteUrl, SecurityUtils.getCurrentUserToken(), searchDTO, body);
        cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createRequest(Method.POST, searchCrmNoteUrl)
                .header("Authorization", TOKEN_TYPE + " " + SecurityUtils.getCurrentUserToken())
                .body(body).execute();

        log.info("[UserAdoptionReportDetailsServiceImpl: searchCrmNote] response code : {}, response message: {}", response.getStatus(), response.body());
        int code = response.getStatus();
        if (HttpStatus.UNAUTHORIZED.value() == code) {
            throw new ExternalServiceInterfaceException("No crm permission", HttpStatus.FORBIDDEN.value());
        } else if (HttpStatus.OK.value() == code || HttpStatus.NOT_FOUND.value() == code) {
            return response.body();
        } else {
            if (response.getStatus() == cn.hutool.http.Status.HTTP_PRECON_FAILED) {
                JSONObject res = JSONUtil.parseObj(response.body());
                throw new WithDataException(res.getStr("message"), cn.hutool.http.Status.HTTP_PRECON_FAILED, res.get("data"));
            } else {
                throw new ExternalServiceInterfaceException(response.body(), response.getStatus());
            }
        }
    }

    private String[] searchCrmNoteDetail(CrmBDActivitySearchDetailDTO searchDTO, Pageable pageable) {
        String searchCrmNoteDetailUrl = getCrmNoteDetailSearchUrl(pageable);
//        String authorizationHeader = getAuthorizationHeader();
        String body = JsonUtil.toJson(searchDTO);
        log.info("[UserAdoptionReportDetailsServiceImpl: searchCrmNoteDetail] url: {}, authorizationHeader: {}, searchDTO: {}, body: {}", searchCrmNoteDetailUrl, SecurityUtils.getCurrentUserToken(), searchDTO, body);
        cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createRequest(Method.POST, searchCrmNoteDetailUrl)
                .header("Authorization", TOKEN_TYPE + " " + SecurityUtils.getCurrentUserToken())
                .body(body).execute();

        log.info("[UserAdoptionReportDetailsServiceImpl: searchCrmNoteDetail] response code : {}, response message: {}", response.getStatus(), response.body());
        int code = response.getStatus();
        if (HttpStatus.UNAUTHORIZED.value() == code) {
            throw new ExternalServiceInterfaceException("No crm permission", HttpStatus.FORBIDDEN.value());
        } else if (HttpStatus.OK.value() == code || HttpStatus.NOT_FOUND.value() == code) {
            String paginationCount = response.header("Pagination-Count");
            return new String[]{response.body(), paginationCount };
        } else {
            if (response.getStatus() == cn.hutool.http.Status.HTTP_PRECON_FAILED) {
                JSONObject res = JSONUtil.parseObj(response.body());
                throw new WithDataException(res.getStr("message"), cn.hutool.http.Status.HTTP_PRECON_FAILED, res.get("data"));
            } else {
                throw new ExternalServiceInterfaceException(response.body(), response.getStatus());
            }
        }
    }

    private String getCrmNoteSearchUrl() {
        return applicationProperties.getCrmUrl() + CRM_NOTE_LIST;
    }

    private String getCrmNoteDetailSearchUrl(Pageable pageable) {
        String url = applicationProperties.getCrmUrl() + CRM_NOTE_DETAIL_URI;
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url += "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                for (Sort.Order sort : pageable.getSort()) {
                    url += "&sort=" + sort.getProperty() + StrUtil.COMMA + sort.getDirection();
                }
            } else {
                url += "&sort=contactTime,desc";
            }
        }
        return url;
    }
}
