package com.altomni.apn.report.repository.v2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.SearchKpiCompanyDto;
import com.altomni.apn.common.dto.recruiting.SearchKpiJobDto;
import org.jooq.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.altomni.apn.report.repository.v2.Constants.*;
import static org.jooq.impl.DSL.*;
import static org.jooq.impl.DSL.field;

public interface RecruitingKpiConditionBuilder {

    static Condition userSearchCondition(RecruitingKpiReportSearchDto searchDto) {
        Condition condition = trueCondition();
        if (CollUtil.isNotEmpty(searchDto.getUserIdList()) && CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            condition = USER_ID.in(searchDto.getUserIdList()).or(TEAM_ID.in(searchDto.getTeamIdList()));
        } else {
            if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
                condition = USER_ID.in(searchDto.getUserIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
                condition = TEAM_ID.in(searchDto.getTeamIdList());
            }
        }
        if (searchDto.getUser() != null && searchDto.getUser().getUserActiveStatus() != null) {
            condition = condition.and(USER_ACTIVATED.eq(searchDto.getUser().getUserActiveStatus()));
        }

        if (searchDto.getParentId() != null) {
            condition = condition.and(TEAM_PARENT_ID.eq(searchDto.getParentId()));
        }
        return condition;
    }

    static String userRoleCondition(RecruitingKpiReportSearchDto searchDto) {
        // 确定要查询的用户角色列表
        List<Integer> targetUserRoles = determineTargetUserRoles(searchDto);
        return "BITMAP_HAS_ANY(user_roles, array_to_bitmap(%s))".formatted(targetUserRoles);

    }

    static Condition jobDataPermissionCondition(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        if (teamDTO == null) {
            return trueCondition();
        }
        // 私有job列表的基础条件
        Condition privateJobCondition = JOB_ID.in(searchDto.getPrivateJobIds());

        // 构建排除私有团队job的条件（如果需要）
        Condition excludePrivateTeamCondition = Optional.ofNullable(teamDTO.getTeamIdForPrivateJob())
                .map(JOB_PTEAM_ID::ne)
                .orElse(trueCondition());

        // 根据权限类型构建主要条件
        Condition mainCondition;
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            // Self权限：用户ID匹配 + 排除私有团队条件
            if (teamDTO.getTeamIdForPrivateJob() != null) {
                mainCondition = USER_ID.eq(searchDto.getSearchUserId()).and(excludePrivateTeamCondition);
            } else {
                mainCondition = USER_ID.eq(searchDto.getSearchUserId());
            }
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            // Team权限：团队ID在允许列表中
            mainCondition = TEAM_ID.in(teamDTO.getNestedTeamIds()).and(excludePrivateTeamCondition);
        } else {
            // All权限
            if (teamDTO.getTeamIdForPrivateJob() != null) {
                // 排除私有团队的job
                mainCondition = excludePrivateTeamCondition;
            } else {
                // 只包含有团队的job
                mainCondition = JOB_PTEAM_ID.isNotNull();
            }
        }

        return mainCondition.or(privateJobCondition);
    }

    static Condition reportDataPermissionCondition(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Condition condition = trueCondition();
        if (Boolean.TRUE.equals(teamDTO.getSelf())) {
            condition = condition.and(USER_ID.eq(searchDto.getSearchUserId()));
        }
        if (teamDTO.getNestedTeamIds() != null && !teamDTO.getNestedTeamIds().isEmpty()) {
            condition = condition.and(TEAM_ID.in(teamDTO.getNestedTeamIds()));
        }
        return condition;
    }

    static Optional<Condition> jobSearchCondition(RecruitingKpiReportSearchDto searchDto) {
        SelectJoinStep<Record1<Long>> jobSubQuery = dsl.selectDistinct(field("j.id", Long.class)).from(table("job").as("j"));

        if (searchDto.getJob() == null) {
            return Optional.empty();
        }

        SearchKpiJobDto jobSearch = searchDto.getJob();
        // 根据条件需要JOIN不同的表
        if (StrUtil.isNotBlank(jobSearch.getCountry())) {
            jobSubQuery = jobSubQuery.leftJoin(table("job_location").as("jl")).on(field("j.id").eq(field("jl.job_id")));
        }

        if (CollUtil.isNotEmpty(jobSearch.getIndustries())) {
            jobSubQuery = jobSubQuery.leftJoin(table("company_industry_relation").as("cir")).on(field("j.company_id").eq(field("cir.company_id")));
        }

        if (CollUtil.isNotEmpty(jobSearch.getJobFunctions())) {
            jobSubQuery = jobSubQuery.leftJoin(table("job_job_function_relation").as("jjfr")).on(field("j.id").eq(field("jjfr.job_id")));
        }

        if (CollUtil.isNotEmpty(jobSearch.getTypeList())) {
            jobSubQuery = jobSubQuery.leftJoin(table("recruitment_process").as("rp")).on(field("j.recruitment_process_id").eq(field("rp.id")));
        }

        // 构建WHERE条件
        Condition condition = trueCondition();
        if (StrUtil.isNotBlank(jobSearch.getTitle())) {
            condition = condition.and(field("j.title").like("%" + jobSearch.getTitle() + "%"));
        }

        if (CollUtil.isNotEmpty(jobSearch.getStatusList())) {
            condition = condition.and(field("j.status").in(jobSearch.getStatusList().stream().map(JobStatus::toDbValue).toList()));
        }

        if (StrUtil.isNotBlank(jobSearch.getCountry())) {
            condition = condition.and(field("jl.official_country").eq(jobSearch.getCountry()));
        }

        if (CollUtil.isNotEmpty(jobSearch.getIndustries())) {
            condition = condition.and(field("cir.industry_id").in(jobSearch.getIndustries()));
        }

        if (CollUtil.isNotEmpty(jobSearch.getJobFunctions())) {
            condition = condition.and(field("jjfr.job_function_id").in(jobSearch.getJobFunctions()));
        }

        if (CollUtil.isNotEmpty(jobSearch.getTypeList())) {
            condition = condition.and(field("rp.job_type").in(jobSearch.getTypeList().stream().map(JobType::toDbValue).toList()));
        }

        if (CollUtil.isNotEmpty(jobSearch.getUserIdList())) {
            Condition andCondition = field("j.puser_id").in(jobSearch.getUserIdList()).and(field("j.pteam_id").ne(searchDto.getPermissionRespDTO().getTeamIdForPrivateJob()));
            condition = condition.and(andCondition.or(field("j.id").in(searchDto.getPrivateJobIds())));
        }

        if (CollUtil.isNotEmpty(jobSearch.getTeamIdList())) {
            condition = condition.and(field("j.pteam_id").in(jobSearch.getTeamIdList()).or(field("j.id").in(searchDto.getPrivateJobIds())));
        }

        return Optional.of(JOB_ID.in(jobSubQuery.where(condition)));
    }

    static Optional<Condition> companySearchCondition(RecruitingKpiReportSearchDto searchDto) {
        SearchKpiCompanyDto companySearch = searchDto.getCompany();
        if (Objects.isNull(companySearch) || Objects.isNull(companySearch.getIdList()) && Objects.isNull(companySearch.getIndustries())) {
            return Optional.empty();
        }
        SelectJoinStep<Record1<Long>> companySubQuery = dsl.selectDistinct(field("ca.id", Long.class).as("id")).from(table("company").as("ca"));
        if (CollUtil.isNotEmpty(companySearch.getIndustries())) {
            companySubQuery = companySubQuery.innerJoin(table("company_industry_relation").as("cir"))
                    .on(field("cir.company_id").eq(field("ca.id")));
        }

        Condition companySubCondition = trueCondition();
        if (ObjectUtil.isNotEmpty(companySearch)) {
            if (CollUtil.isNotEmpty(companySearch.getIdList())) {
                companySubCondition = companySubCondition.and(field("ca.id").in(companySearch.getIdList()));
            }
            if (CollUtil.isNotEmpty(companySearch.getIndustries())) {
                companySubCondition = companySubCondition.and(field("cir.industry_id").in(companySearch.getIndustries()));
            }
        }
        return Optional.of(COMPANY_ID.in(companySubQuery.where(companySubCondition)));

    }

    /**
     * 根据搜索条件确定目标用户角色列表
     * 因为操作角色增加了流程操作者，只有选择角色才会查询，所有前端不传递值时默认查询kpi user的角色数据
     */
    private static List<Integer> determineTargetUserRoles(RecruitingKpiReportSearchDto searchDto) {
        // 如果没有用户搜索条件，使用默认角色
        if (searchDto.getUser() == null) {
            return defaultUserRole();
        }

        boolean isProcessOperator = BooleanUtil.isTrue(searchDto.getUser().getProcessOperator());

        // 如果指定了用户角色列表
        if (CollUtil.isNotEmpty(searchDto.getUser().getUserRoleList())) {
            List<Integer> roleList = new ArrayList<>(getTalentUserRoleList(searchDto.getUser().getUserRoleList()));
            if (isProcessOperator) {
                roleList.add(10); // 添加流程操作者角色
            }
            return roleList;
        }

        // 如果没有指定角色列表，根据是否为流程操作者决定
        if (isProcessOperator) {
            return List.of(10); // 流程操作者特定角色
        }

        // 默认使用所有KPI用户角色
        return defaultUserRole();
    }

    private static List<Integer> defaultUserRole() {
        List<Integer> ALL_USER_ROLES = new ArrayList<>();
        for (UserRole userRole : UserRole.values()) {
            if (userRole.equals(UserRole.PR) || userRole.equals(UserRole.NODE_CREATED_BY)) {
                continue;
            }
            ALL_USER_ROLES.add(userRole.toDbValue());
        }
        // 通用版的 kpi_user 表，user_role 是 null, 在 物化视图里面处理成-1 了
        ALL_USER_ROLES.add(-1);
        return ALL_USER_ROLES;
    }

    private static List<Integer> getTalentUserRoleList(List<UserRole> userRoleList) {
        return userRoleList.stream().map(UserRole::toDbValue).filter(ObjectUtil::isNotNull).toList();
    }
}
