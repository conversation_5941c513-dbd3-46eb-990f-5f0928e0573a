package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivisionConverter;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.report.domain.constants.ReportHiresConstants;
import com.altomni.apn.report.domain.enumeration.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ContractorHireVo implements Serializable {

    @Id
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "Candidate Full Name", index = 0)
    private String fullName;

    @ExcelProperty(value = "Email", index = 1)
    private String email;

    @ExcelProperty(value = "Job ID", index = 2)
    private Long jobId;

    @ExcelProperty(value = "Job Code", index = 3)
    private String jobCode;

    @ExcelProperty(value = "Job Title", index = 4)
    private String jobTitle;

    @ExcelProperty(value = "Assignment Division", index = 5, converter = AssignmentDivisionDataConverter.class)
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision assignmentDivision;

    @ExcelProperty(value = "Company", index = 6)
    private String company;

    @ExcelProperty(value = "Department", index = 7)
    private String department;

    @ExcelProperty(value = "Hiring Manager", index = 8)
    private String hiringManager;

    @ExcelProperty(value = "AM", index = 9)
    private String am;

    @ExcelProperty(value = "Address", index = 10)
    private String address;

    @ExcelProperty(value = "City", index = 11)
    private String city;

    @ExcelProperty(value = "State", index = 12)
    private String state;

    @ExcelProperty(value = "Country", index = 13)
    private String country;

    @ExcelProperty(value = "Start Status", index = 14, converter = StartReportStatusDataConverter.class)
    @Convert(converter = StartReportStatusConverter.class)
    private StartReportStatus startStatus;

    @ExcelProperty(value = "Start Date", index = 15, converter = LocalDateDataConverter.class)
    private LocalDate startDate;

    @ExcelProperty(value = "Est. End Date", index = 16, converter = LocalDateDataConverter.class)
    private LocalDate endDate;

    @ExcelIgnore
    private Instant onboardDate;

    @Transient
    @ExcelProperty(value = "Onboard Date", index = 17)
    private String onboardDateFormat;

    @ExcelProperty(value = "Recuiter", index = 18)
    private String recruiter;

    @ExcelProperty(value = "Recruiter Team", index = 19)
    private String recruiterTeam;

    @ExcelIgnore
    private BigDecimal billRate;

    @ExcelIgnore
    private BigDecimal payRate;

    @Transient
    @ExcelProperty(value = "Bill Rate", index = 20)
    private String billRateFormat;

    @Transient
    @ExcelProperty(value = "Pay Rate", index = 22)
    private String payRateFormat;


    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType billRateUnit;

    @Transient
    @ExcelProperty(value = "Bill Rate Unit", index = 21)
    private String billRateUnitFormat;

    @Transient
    @ExcelProperty(value = "Pay Rate Unit", index = 23)
    private String payRateUnitFormat;

    @ExcelIgnore
    @Transient
    private String billCurrency;

    @ExcelIgnore
    @Transient
    private String payCurrency;

    @ExcelIgnore
    private String currency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType payRateUnit;

    @ExcelIgnore
    private BigDecimal hourlyMargin;

    @Transient
    @ExcelProperty(value = "Hourly Margin", index = 24)
    private String hourlyMarginFormat;

    @ExcelProperty(value = "Hourly Margin %", index = 25)
    private String hourlyMarginPercent;

    @ExcelIgnore
    private String sourcingChannel;

    @Transient
    @ExcelProperty(value = "Source Channel", index = 27)
    private String sourcingChannelFormat;

    @ExcelProperty(value = "Previously Hired", index = 26, converter = BooleanConverter.class)
    private Boolean previouslyHired;

    @ExcelIgnore
    private String timeZone;

    public String getBillCurrency() {
        return currency;
    }

    public String getPayCurrency() {
        return currency;
    }

    public String getBillRateUnitFormat() {
        if (Objects.isNull(billRateUnit)){
            return null;
        }
        return getBillCurrency() + "/" + billRateUnit.name().substring(0,1).toLowerCase();
    }

    public String getPayRateUnitFormat() {
        if (Objects.isNull(payRateUnit)){
            return null;
        }
        return getPayCurrency() + "/" + payRateUnit.name().substring(0,1).toLowerCase();
    }

    public String getHourlyMarginFormat() {
        if (hourlyMargin != null) {
            return hourlyMargin.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getTimeZone() {
        if (StrUtil.isBlank(timeZone)) {
            return DateUtil.US_LA_TIMEZONE;
        }
        return timeZone;
    }

    public String getOnboardDateFormat() {
        if (onboardDate != null) {
            return DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.of(getTimeZone())).format(onboardDate);
        }
        return null;
    }

    public String getBillRateFormat() {
        if (billRate != null) {
            return billRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getPayRateFormat() {
        if (payRate != null) {
            return payRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getHourlyMarginPercent() {
        if (StrUtil.isNotBlank(hourlyMarginPercent)) {
            return new BigDecimal(hourlyMarginPercent).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.DOWN) + "%";
        }
        return null;
    }

    public String getSourcingChannelFormat() {
        if (StrUtil.isNotBlank(sourcingChannel)) {
            return ReportHiresConstants.sourcingChannelMap.get(sourcingChannel);
        }
        return sourcingChannel;
    }

}
