package com.altomni.apn.company.service.report.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.utils.ExcelUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.company.domain.enumeration.report.PerformanceReportType;
import com.altomni.apn.company.domain.enumeration.report.ReportTableType;
import com.altomni.apn.company.domain.vm.CompanyReportCountVM;
import com.altomni.apn.company.domain.vm.PerformanceDataVM;
import com.altomni.apn.company.domain.vm.ReportApplicationCreatedDateVM;
import com.altomni.apn.company.domain.vm.ReportApplicationVM;
import com.altomni.apn.company.domain.vo.PerformanceDataVO;
import com.altomni.apn.company.repository.job.JobCompanyBriefRepository;
import com.altomni.apn.company.service.dto.report.PerformanceReportSearchDTO;
import com.altomni.apn.company.service.report.PerformanceReportService;
import com.altomni.apn.company.vo.report.PerformanceReportSummaryVO;
import com.altomni.apn.company.vo.report.PerformanceReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;

import static com.altomni.apn.common.utils.DateUtil.US_LA_TIMEZONE;

/**
 * performanceReportService
 * <AUTHOR>
 */
@Slf4j
@Service("performanceReportService")
public class PerformanceReportServiceImpl implements PerformanceReportService {

    @Resource
    private EntityManager entityManager;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    private JobCompanyBriefRepository jobCompanyBriefRepository;

    @Resource
    private PerformanceReportServiceImpl self;


    private final static List<ReportTableType> COUNT_BY_COMPANY_TYPE_LIST = Arrays.asList(ReportTableType.SUBMIT_TO_JOB, ReportTableType.SUBMIT_TO_CLIENT
            , ReportTableType.INTERVIEW, ReportTableType.OFFER, ReportTableType.OFFER_ACCEPT, ReportTableType.ON_BOARD);

    @Override
    public PerformanceReportVO findPerformanceReport(PerformanceReportSearchDTO condition) {
        PerformanceReportVO pReportVO  = new PerformanceReportVO();

        Map<Integer, Object> defaultParamMap = new ConcurrentHashMap<>(16);
        defaultParamMap.put(1,SecurityUtils.getTenantId());
        defaultParamMap.put(2,condition.getCompanyId());
        defaultParamMap.put(3, convertJobTypes(condition.getJobType()));
        defaultParamMap.put(4, convertUserRole(condition.getUserRole()));

        Map<Integer, Object> filterDefaultParamMap = new ConcurrentHashMap<>(16);

        PerformanceReportSummaryVO performanceReportSummaryVO = new PerformanceReportSummaryVO();

        Map<ReportTableType, CompletableFuture<List<CompanyReportCountVM>>> summaryMap = new HashMap<>();
        COUNT_BY_COMPANY_TYPE_LIST.forEach(type -> {
            var itemCountList = CompletableFuture.supplyAsync(() -> partitionCountSearch(condition, CompanyReportCountVM.class, defaultParamMap, type)
                .stream().filter(Objects::nonNull).collect(Collectors.toList()), executor);
            summaryMap.put(type, itemCountList);
        });
        CompletableFuture.allOf(summaryMap.values().toArray(new CompletableFuture[]{})).join();

        summaryMap.forEach((type, itemCountListFuture) -> {
            List<CompanyReportCountVM> itemCountList = itemCountListFuture.join();
            int count = 0;
            String ids = "";
            if (CollUtil.isNotEmpty(itemCountList)) {
                count = itemCountList.get(0).getCount();
                ids = itemCountList.get(0).getApplicationIds();
            }
            switch (type) {
                case SUBMIT_TO_JOB: performanceReportSummaryVO.setSubmitToAm(count).setSubmitToAmIds(ids); break;
                case SUBMIT_TO_CLIENT: performanceReportSummaryVO.setSubmitToClient(count).setSubmitToClientIds(ids); break;
                case INTERVIEW: performanceReportSummaryVO.setInterview(count).setInterviewIds(ids); break;
                case OFFER: performanceReportSummaryVO.setOfferByClient(count).setOfferByClientIds(ids); break;
                case OFFER_ACCEPT: performanceReportSummaryVO.setOfferAccept(count).setOfferAcceptIds(ids); break;
                case ON_BOARD: performanceReportSummaryVO.setOnBoard(count).setOnBoardIds(ids); break;
                default: log.error("[APN: PerformanceReportService @{}] Failed to query data : {}", SecurityUtils.getUserId(), condition); break;
            }
        });

        pReportVO.setSummary(performanceReportSummaryVO);
        List<Long> filterApplicationIds = new ArrayList<>();
        if (condition.getApplicationType() != null) {
            switch (condition.getApplicationType()) {
                case SUBMIT_TO_JOB: filterApplicationIds = StringUtils.isNotBlank(performanceReportSummaryVO.getSubmitToAmIds()) ? Arrays.stream(performanceReportSummaryVO.getSubmitToAmIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList(); break;
                case SUBMIT_TO_CLIENT: filterApplicationIds = StringUtils.isNotBlank(performanceReportSummaryVO.getSubmitToClientIds()) ? Arrays.stream(performanceReportSummaryVO.getSubmitToClientIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList(); break;
                case INTERVIEW: filterApplicationIds = StringUtils.isNotBlank(performanceReportSummaryVO.getInterviewIds()) ? Arrays.stream(performanceReportSummaryVO.getInterviewIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList(); break;
                case OFFER: filterApplicationIds = StringUtils.isNotBlank(performanceReportSummaryVO.getOfferByClientIds()) ? Arrays.stream(performanceReportSummaryVO.getOfferByClientIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList(); break;
                case OFFER_ACCEPT: filterApplicationIds = StringUtils.isNotBlank(performanceReportSummaryVO.getOfferAcceptIds()) ? Arrays.stream(performanceReportSummaryVO.getOfferAcceptIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList(); break;
                case ON_BOARD: filterApplicationIds = StringUtils.isNotBlank(performanceReportSummaryVO.getOnBoardIds()) ? Arrays.stream(performanceReportSummaryVO.getOnBoardIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList(); break;
                default: log.error("[APN: PerformanceReportService @{}] Failed to query data : {}", SecurityUtils.getUserId(), condition); break;
            }
        } else {
            filterApplicationIds.addAll(StringUtils.isNotBlank(performanceReportSummaryVO.getSubmitToAmIds()) ? Arrays.stream(performanceReportSummaryVO.getSubmitToAmIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList());
            filterApplicationIds.addAll(StringUtils.isNotBlank(performanceReportSummaryVO.getSubmitToClientIds()) ? Arrays.stream(performanceReportSummaryVO.getSubmitToClientIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList());
            filterApplicationIds.addAll(StringUtils.isNotBlank(performanceReportSummaryVO.getInterviewIds()) ? Arrays.stream(performanceReportSummaryVO.getInterviewIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList());
            filterApplicationIds.addAll(StringUtils.isNotBlank(performanceReportSummaryVO.getOfferByClientIds()) ? Arrays.stream(performanceReportSummaryVO.getOfferByClientIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList());
            filterApplicationIds.addAll(StringUtils.isNotBlank(performanceReportSummaryVO.getOfferAcceptIds()) ? Arrays.stream(performanceReportSummaryVO.getOfferAcceptIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList());
            filterApplicationIds.addAll(StringUtils.isNotBlank(performanceReportSummaryVO.getOnBoardIds()) ? Arrays.stream(performanceReportSummaryVO.getOnBoardIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : Collections.emptyList());
        }

        Set<Long> eliminatedApplicationIds = searchEliminatedApplicationIds(filterApplicationIds.stream().distinct().collect(Collectors.toList()));

        filterDefaultParamMap.put(1, filterApplicationIds.stream().filter(o -> !eliminatedApplicationIds.contains(o)).distinct().collect(Collectors.toList()));
        List<PerformanceDataVO> jobVoList = self.findPerformanceJobData(condition, filterDefaultParamMap);
        pReportVO.setJobData(jobVoList);

        return pReportVO;
    }

    @ProcessConfidentialTalent(operation = ProcessConfidentialTalent.operation.DO_NOTHING)
    public List<PerformanceDataVO> findPerformanceJobData(PerformanceReportSearchDTO condition, Map<Integer, Object> filterDefaultParamMap) {
        List<PerformanceDataVM> jobDataList = self.partitionCountSearch(condition, PerformanceDataVM.class, filterDefaultParamMap);
        setJobDataListLastUpdateStatusDate(jobDataList);
        Set<Long> privateJobTeamIds = jobCompanyBriefRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId());
        List<PerformanceDataVO> jobVoList = jobDataList.stream().map(o -> {
            PerformanceDataVO performanceDataVO = new PerformanceDataVO();
            ServiceUtils.myCopyProperties(o, performanceDataVO);
            performanceDataVO.setActivityStatus(NodeType.fromDbValue(o.getActivityStatus().toDbValue()));
            if (ReportTableType.INTERVIEW.equals(o.getActivityStatus())) {
                performanceDataVO.setEventStage(o.getInterviewInfoProgress());
                performanceDataVO.setEventType(o.getInterviewInfoType());
                performanceDataVO.setLatestInterviewDate(o.getInterviewInfoAppointment());
                performanceDataVO.setEventTimeZone(o.getInterviewInfoTimeZone());
                performanceDataVO.setInterviewInfo(getInterviewInfo(o.getInterviewInfoProgress(), o.getInterviewInfoType(), o.getInterviewInfoAppointment(), o.getInterviewInfoTimeZone()));
                performanceDataVO.setInterviewCount(o.getInterviewCount());
            }
            performanceDataVO.setIsPrivateJob(privateJobTeamIds.contains(o.getPteamId()));

            if (BooleanUtils.isTrue(performanceDataVO.getConvertedToFte())){
                performanceDataVO.setResigned(Boolean.FALSE);
            }

            return performanceDataVO;
        }).toList();
        return jobVoList.stream().sorted(sortJobDataVOList()).toList();
    }

    @Override
    public void downloadReport(PerformanceReportSearchDTO condition, HttpServletResponse response)
    {
        PerformanceReportVO reportVO = findPerformanceReport(condition);
        try {
            String fileName = "Internal_Performance_Report.xlsx";
            response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            createAmReportExcel(reportVO, response.getOutputStream(), condition.getUserId(), condition.getUserRole());
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    public void createAmReportExcel(PerformanceReportVO amReportVO , OutputStream outputStream, List<Long> users, UserRole userRole)
    {

        List<PerformanceDataVO> reportData = amReportVO.getJobData().stream().map(d -> d.setActivityStatus(BooleanUtils.isTrue(d.getResigned()) ? NodeType.OFF_BOARDED : d.getActivityStatus())).toList();

        PerformanceReportSummaryVO summaryVO = amReportVO.getSummary();
        List<PerformanceReportSummaryVO> summaryVOS = new LinkedList<>();
        summaryVOS.add(summaryVO);
        ExcelWriter excelWriter = null;
        try {

            Map<JobType, List<PerformanceDataVO>>  typeMap  = reportData.stream().collect(Collectors.groupingBy(PerformanceDataVO::getJobType));

            excelWriter  = EasyExcel.write(outputStream).build();

            int sheetIndex = 0;
            //export summary

            WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex++,"SUMMARY").head(PerformanceReportSummaryVO.class).needHead(true).build();
            excelWriter.write(summaryVOS, writeSheet);

            String tagName = "";
            if(CollectionUtils.isNotEmpty(reportData))
            {
                String name   = userRole == UserRole.SOURCER ? reportData.get(0).getSourcer() : reportData.get(0).getRecruiter();
                if(userRole != null && users != null && users.size()==1)
                {
                    tagName  = "-" + name + "("+userRole+")";
                }
            }

            int i=0;
            List<String> titleData = new ArrayList<>();

            for(Map.Entry<JobType,List<PerformanceDataVO>> entry :typeMap.entrySet())
            {

                titleData.clear();
                titleData.add(entry.getKey().getName()+tagName);
                List<List<String>> head = new LinkedList<>();
                head.add(titleData);
                writeSheet = EasyExcel.writerSheet(sheetIndex++, entry.getKey().getName()).build();
                WriteTable writeTable = EasyExcel.writerTable(i++).head(head).needHead(true).build();

                excelWriter.write(titleData, writeSheet,writeTable);

                writeTable = EasyExcel.writerTable(i++).head(PerformanceDataVO.class).needHead(true).build();
                List<PerformanceDataVO> values = entry.getValue();
                // 没有保密候选人查看权限的情况下，需要将保密数据屏蔽
                values.forEach(data -> {
                    if (data.getConfidentialTalentViewAble() != null && !data.getConfidentialTalentViewAble()) {
                        ExcelUtil.maskConfidentialTalentData(data);
                    }
                });
                excelWriter.write(values, writeSheet,writeTable);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(excelWriter != null) {
                excelWriter.finish();
            }
            if(outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private String getInterviewInfo(Integer interviewInfoProgress, InterviewType interviewInfoType, Instant interviewInfoAppointment, String timeZone) {
        if (interviewInfoProgress == null || interviewInfoType == null || interviewInfoAppointment == null) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm a").withZone(TimeZone.getTimeZone(timeZone != null ? timeZone : US_LA_TIMEZONE).toZoneId());
        return generateOrdinalSuffix(interviewInfoProgress) + " Round, " + interviewInfoType + " (" + (df.format(interviewInfoAppointment) + " " + timeZone) + ")";
    }

    private String generateOrdinalSuffix(int number) {
        if (number >= 11 && number <= 13) {
            return number + "th";
        }

        int lastDigit = number % 10;

        switch (lastDigit) {
            case 1:
                return number + "st";
            case 2:
                return number + "nd";
            case 3:
                return number + "rd";
            default:
                return number + "th";
        }
    }

    private <T> List<T> partitionCountSearch(PerformanceReportSearchDTO condition, Class<T> clazz, Map<Integer, Object> defaultParamMap, ReportTableType type) {
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> map = new HashMap<>(16);
        map.putAll(defaultParamMap);
        switch (type) {
            case SUBMIT_TO_JOB: createQuerySubmitToJobCount(sb, map, condition); break;
            case SUBMIT_TO_CLIENT: createQuerySubmitToClientCount(sb, map, condition); break;
            case INTERVIEW: createQueryInterviewCount(sb, map, condition); break;
            case OFFER: createQueryOfferedCount(sb, map, condition); break;
            case OFFER_ACCEPT: createQueryOfferedAcceptedCount(sb, map, condition); break;
            case ON_BOARD: createQueryOnboardCount(sb, map, condition); break;
            default: log.error("[APN: PerformanceReportService @{}] Failed to query data : {}", SecurityUtils.getUserId(), condition); break;
        }
        return searchData(sb.toString(), clazz, map);
    }

    private void createQuerySubmitToJobCount(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        sb.append("""
                SELECT 
                   c.id company_id, 
                   COUNT( DISTINCT trp.id ) count, 
                   GROUP_CONCAT(DISTINCT trp.id) application_ids, 
                   10 type  
                  FROM 
                   talent_recruitment_process_submit_to_job trj 
                   INNER JOIN talent_recruitment_process trp ON trp.id = trj.talent_recruitment_process_id 
                   INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id 
                   INNER JOIN job j ON j.id = trp.job_id
                   INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id 
                   INNER JOIN company c ON c.id = j.company_id  
                  WHERE 
                   c.tenant_id = ?1
                   AND tru.user_role IN ?4
                   AND c.id = ?2 
                   AND trp.tenant_id = ?1 
                   AND rp.job_type IN ?3
                """);

        setFilterUserIds(sb, paramMap, condition);

        setFilterSalesLeadId(sb, paramMap, condition);

        setFilterDate(sb, paramMap, condition, "trj");

    }

    private void createQuerySubmitToClientCount(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        if (PerformanceReportType.SUBMISSION_DATE.equals(condition.getReportType())) {
            sb.append("""
                    SELECT 
                      c.id company_id, 
                      COUNT( DISTINCT trp.id ) count,  
                      GROUP_CONCAT(DISTINCT trp.id) application_ids, 
                      20 type  
                     FROM 
                      talent_recruitment_process_submit_to_client trs 
                      INNER JOIN talent_recruitment_process trp ON trp.id = trs.talent_recruitment_process_id 
                      INNER JOIN talent_recruitment_process_submit_to_job trj ON trj.talent_recruitment_process_id = trp.id 
                      INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id 
                      INNER JOIN job j ON j.id = trp.job_id
                      INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id 
                      INNER JOIN company c ON c.id = j.company_id 
                     WHERE 
                      c.tenant_id = ?1
                      AND tru.user_role IN ?4
                      AND c.id = ?2 
                      AND trp.tenant_id = ?1 
                      AND rp.job_type IN ?3
                    """);
        } else {
            sb.append("""
                    SELECT 
                      c.id company_id, 
                      COUNT( DISTINCT trp.id ) count,  
                      GROUP_CONCAT(DISTINCT trp.id) application_ids, 
                      20 type  
                     FROM 
                      talent_recruitment_process_submit_to_client trs 
                      INNER JOIN talent_recruitment_process trp ON trp.id = trs.talent_recruitment_process_id 
                      INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id 
                      INNER JOIN job j ON j.id = trp.job_id
                      INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id 
                      INNER JOIN company c ON c.id = j.company_id 
                     WHERE 
                      c.tenant_id = ?1
                      AND tru.user_role IN ?4
                      AND c.id = ?2 
                      AND trp.tenant_id = ?1 
                      AND rp.job_type IN ?3
                    """);
        }

        setFilterUserIds(sb, paramMap, condition);

        setFilterSalesLeadId(sb, paramMap, condition);

        setFilterDate(sb, paramMap, condition, "trs");

    }

    private void createQueryInterviewCount(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        if (PerformanceReportType.SUBMISSION_DATE.equals(condition.getReportType())) {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  30 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_interview tri \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = tri.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_submit_to_job trj ON trj.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        } else {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  30 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_interview tri \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = tri.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        }

        setFilterUserIds(sb, paramMap, condition);

        setFilterSalesLeadId(sb, paramMap, condition);

        setFilterDate(sb, paramMap, condition, "tri");
    }

    private void createQueryOfferedCount(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        if (PerformanceReportType.SUBMISSION_DATE.equals(condition.getReportType())) {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  40 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_offer tro \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = tro.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_submit_to_job trj ON trj.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        } else {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  40 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_offer tro \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = tro.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        }

        setFilterUserIds(sb, paramMap, condition);

        setFilterSalesLeadId(sb, paramMap, condition);

        setFilterDate(sb, paramMap, condition, "tro");
    }

    private void createQueryOfferedAcceptedCount(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        if (PerformanceReportType.SUBMISSION_DATE.equals(condition.getReportType())) {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  41 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_ipg_offer_accept troac \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = troac.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_submit_to_job trj ON trj.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        } else {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  41 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_ipg_offer_accept troac \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = troac.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        }

        setFilterUserIds(sb, paramMap, condition);

        setFilterSalesLeadId(sb, paramMap, condition);

        setFilterDate(sb, paramMap, condition, "troac");

    }

    private void createQueryOnboardCount(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        if (PerformanceReportType.SUBMISSION_DATE.equals(condition.getReportType())) {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  60 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_onboard trobd \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = trobd.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1 \n" +
                    "  INNER JOIN talent_recruitment_process_submit_to_job trj ON trj.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        } else {
            sb.append("SELECT \n" +
                    "  c.id company_id, \n" +
                    "  COUNT( DISTINCT trp.id ) count,  \n" +
                    "  GROUP_CONCAT(DISTINCT trp.id) application_ids, \n " +
                    "  60 type  \n" +
                    " FROM \n" +
                    "  talent_recruitment_process_onboard trobd \n" +
                    "  INNER JOIN talent_recruitment_process trp ON trp.id = trobd.talent_recruitment_process_id \n" +
                    "  INNER JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1 \n" +
                    "  INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id \n" +
                    "  INNER JOIN job j ON j.id = trp.job_id\n" +
                    "  INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                    "  INNER JOIN company c ON c.id = j.company_id \n" +
                    " WHERE \n" +
                    "  c.tenant_id = ?1" +
                    "  AND tru.user_role IN ?4" +
                    "  AND c.id = ?2 \n" +
                    "  AND trp.tenant_id = ?1 \n" +
//                    "  AND j.`status` IN ( 5, 0) \n" +
                    "  AND rp.job_type IN ?3");
        }

        setFilterUserIds(sb, paramMap, condition);

        setFilterSalesLeadId(sb, paramMap, condition);

        setFilterDate(sb, paramMap, condition, "trobd");

    }


    private void createSearchJobListSql(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        sb.append("SELECT\n" +
                "	t.full_name AS 'full_name',\n" +
                "	t.id AS 'candidate_id',\n" +
                "	trp.id AS id,\n" +
                "	trp.id AS 'application_id',\n" +
                "	j.id AS 'job_id',\n" +
                "	j.title AS 'job_title',\n" +
                "	j.created_date AS 'job_created_date',\n" +
                "	j.pteam_id,\n" +
                "	rp.job_type AS 'job_type',\n" +
                "	MAX(tpn.node_type) AS 'activity_status',\n" +
                "	trj.created_date AS 'submit_date',\n" +
                "	interview.progress AS 'interview_info_progress',\n" +
                "	interview.interview_type AS 'interview_info_type',\n" +
                "	interview.from_time AS 'interview_info_appointment',\n" +
                "	interview.time_zone AS 'interview_info_time_zone',\n" +
                "	cct.full_name AS 'hr_contact',\n" +
                "	trp.last_modified_date AS 'status_update_date',\n" +
                "	group_concat(\n" +
                "		DISTINCT (\n" +
                "		concat( recruiter.first_name, ' ', recruiter.last_name ))) AS 'recruiter',\n" +
                "	group_concat(\n" +
                "		DISTINCT (\n" +
                "		concat( sourcer.first_name, ' ', sourcer.last_name ))) AS 'sourcer'," +
                "  (select count(trpia.id) from talent_recruitment_process_interview trpia where trp.id = trpia.talent_recruitment_process_id) interview_count, \n" +
                "  if(resign.id is null, false, true) resigned, \n" +
                "  if(star.id is null, false, true) as converted_to_fte \n" +
                "FROM\n" +
                "	talent_recruitment_process trp\n" +
                "   LEFT JOIN recruitment_process rp on rp.id = trp.recruitment_process_id " +
                "	LEFT JOIN job j ON j.id = trp.job_id\n" +
                "	LEFT JOIN job_company_contact_relation jcr ON jcr.job_id = j.id \n" +
                "	LEFT JOIN company_sales_lead_client_contact cc ON cc.id = jcr.client_contact_id\n" +
                "   LEFT JOIN talent cct ON cc.talent_id = cct.id\n" +
                "	LEFT JOIN talent t ON trp.talent_id = t.id\n" +
                "	LEFT JOIN talent_recruitment_process_kpi_user acr ON trp.id = acr.talent_recruitment_process_id \n" +
                "	AND acr.user_role = 1\n" +
                "	LEFT JOIN `user` recruiter ON acr.user_id = recruiter.id\n" +
                "	LEFT JOIN talent_recruitment_process_kpi_user acs ON trp.id = acs.talent_recruitment_process_id \n" +
                "	AND acs.user_role = 2\n" +
                "	LEFT JOIN `user` sourcer ON acs.user_id = sourcer.id\n" +
                "	LEFT JOIN (\n" +
                "    SELECT talent_recruitment_process_id, MAX(progress) AS max_progress\n" +
                "    FROM talent_recruitment_process_interview\n" +
                "    GROUP BY talent_recruitment_process_id\n" +
                "  ) maxInterview ON maxInterview.talent_recruitment_process_id = trp.id\n" +
                "   LEFT JOIN talent_recruitment_process_interview interview ON interview.talent_recruitment_process_id = maxInterview.talent_recruitment_process_id AND interview.progress = maxInterview.max_progress \n" +
                "	LEFT JOIN talent_recruitment_process_submit_to_job trj ON trj.talent_recruitment_process_id = trp.id \n" +
                "	LEFT JOIN talent_recruitment_process_resignation resign ON resign.talent_recruitment_process_id = trp.id \n" +
                "	left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 \n" +
                "	INNER JOIN company cp ON cp.id = j.company_id\n" +
                "	INNER JOIN talent_recruitment_process_node tpn ON tpn.talent_recruitment_process_id = trp.id AND tpn.node_status IN (1)\n" +
                "WHERE\n" +
                "	trp.id IN ?1\n" +
                " GROUP BY trp.id");
    }

    private void setFilterSalesLeadId(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        if (condition.getSalesLeadList() == null || CollUtil.isEmpty(condition.getSalesLeadList())) {
            return;
        }

        sb.append(" AND j.sales_lead_id in  ?");
        sb.append(paramMap.size() + 1);
        paramMap.put(paramMap.size() + 1, condition.getSalesLeadList());
    }

    private void setFilterUserIds(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition) {
        if (condition.getUserId() == null || CollUtil.isEmpty(condition.getUserId())) {
            return;
        }

        sb.append(" AND tru.user_id IN ?");
        sb.append(paramMap.size() + 1);
        paramMap.put(paramMap.size() + 1, condition.getUserId());
    }

    private void setFilterDate(StringBuilder sb, Map<Integer, Object> paramMap, PerformanceReportSearchDTO condition, String tableName) {
        if (condition.getFromDate() == null && condition.getToDate() == null) {
            return;
        }

        if (condition.getFromDate() != null) {
            if (condition.getReportType().equals(PerformanceReportType.SUBMISSION_DATE)) {
                sb.append(" AND trj.created_date > ?");
            } else {
                sb.append(" AND ").append(tableName).append(".created_date > ?");
            }
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, condition.getFromDate());
        }

        if (condition.getToDate() != null) {
            if (condition.getReportType().equals(PerformanceReportType.SUBMISSION_DATE)) {
                sb.append(" AND trj.created_date < ?");
            } else {
                sb.append(" AND ").append(tableName).append(".created_date < ?");
            }
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, condition.getToDate());
        }
    }

    private Set<Long> searchEliminatedApplicationIds(List<Long> applicationIds) {
        String sb = "SELECT DISTINCT talent_recruitment_process_id application_id FROM talent_recruitment_process_node WHERE talent_recruitment_process_id IN ?1 AND node_status = 4";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, applicationIds);
        return searchData(sb, ReportApplicationVM.class, paramMap).stream().map(ReportApplicationVM::getApplicationId).collect(Collectors.toSet());
    }

    private List<ReportApplicationCreatedDateVM> searchApplicationLastUpdateStatusDate(List<Long> applicationIds, ReportTableType type) {
        String sb = "SELECT talent_recruitment_process_id application_id, MIN(created_date) created_date FROM " + type.getDbTableName() + " WHERE talent_recruitment_process_id IN ?1 GROUP BY talent_recruitment_process_id";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, applicationIds);
        return searchData(sb, ReportApplicationCreatedDateVM.class, paramMap);
    }

    public <T> List<T> partitionCountSearch(PerformanceReportSearchDTO condition, Class<T> clazz, Map<Integer, Object> defaultParamMap) {
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> map = new HashMap<>(16);
        map.putAll(defaultParamMap);
        createSearchJobListSql(sb, map, condition);
        return searchData(sb.toString(), clazz, map);
    }

    @NotNull
    private List<Integer> convertJobTypes(List<JobType> jobTypes)
    {
        if(jobTypes == null)
        {
            jobTypes = new ArrayList<>();
            jobTypes.add(JobType.CONTRACT);
            jobTypes.add(JobType.FULL_TIME);
            jobTypes.add(JobType.MSP);
        }

        List<Integer> jobTypeStr=new ArrayList<>();
        for(JobType jobType:jobTypes)
        {
            jobTypeStr.add(jobType.toDbValue());
        }
        return jobTypeStr;
    }

    @NotNull
    private List<Integer> convertUserRole(UserRole userRole)
    {
        List<Integer> userRoles = new ArrayList<>();
        if (userRole == null)
        {
            userRoles.add(UserRole.RECRUITER.toDbValue());
            userRoles.add(UserRole.SOURCER.toDbValue());
        } else {
            userRoles.add(userRole.toDbValue());
        }

        return userRoles;
    }

    private void setJobDataListLastUpdateStatusDate(List<PerformanceDataVM> jobDataList) {
        Map<ReportTableType, List<Long>> dataMap = jobDataList.stream()
                .collect(Collectors.groupingBy(PerformanceDataVM::getActivityStatus,
                        Collectors.mapping(PerformanceDataVM::getApplicationId, Collectors.toList())));

        List<CompletableFuture<List<ReportApplicationCreatedDateVM>>> result = new ArrayList<>();
        dataMap.forEach((activityStatus, applicationIds) -> {
            result.add(CompletableFuture.supplyAsync(() -> searchApplicationLastUpdateStatusDate(applicationIds, activityStatus), executor));
        });
        CompletableFuture.allOf(result.toArray(new CompletableFuture[]{})).join();


//        Map<Long, Instant> resultMap = result.stream().collect(Collectors.toMap(ReportApplicationCreatedDateVM::getApplicationId, ReportApplicationCreatedDateVM::getCreatedDate));
        Map<Long, Instant> resultMap = result.stream()
            .map(CompletableFuture::join)
            .flatMap(Collection::stream)
            .collect(Collectors.toMap(
                    ReportApplicationCreatedDateVM::getApplicationId,
                    ReportApplicationCreatedDateVM::getCreatedDate,
                    BinaryOperator.maxBy(Instant::compareTo)
            ));
        jobDataList.forEach(o -> o.setStatusUpdateDate(resultMap.get(o.getApplicationId())));
    }


    private Comparator<PerformanceDataVO> sortJobDataVOList() {
        Comparator<PerformanceDataVO> submitDateComparator = (u1, u2) -> {
            if (u1.getSubmitDate() == null && u2.getSubmitDate() == null) {
                return 0;
            }
            if (u1.getSubmitDate() == null) {
                return -1;
            }
            if (u2.getSubmitDate() == null) {
                return 1;
            }
            LocalDate u1SubmitDate = LocalDate.ofInstant(u1.getSubmitDate(), ZoneId.systemDefault());
            LocalDate u2SubmitDate = LocalDate.ofInstant(u2.getSubmitDate(), ZoneId.systemDefault());
            return u1SubmitDate.compareTo(u2SubmitDate);
        };
        Comparator<PerformanceDataVO> createDateComparator = (u1, u2) -> {
            if (u1.getJobCreatedDate() == null && u2.getJobCreatedDate() == null) {
                return 0;
            }
            if (u1.getJobCreatedDate() == null) {
                return -1;
            }
            if (u2.getJobCreatedDate() == null) {
                return 1;
            }
            LocalDate u1CreateDate = LocalDate.ofInstant(u1.getJobCreatedDate(), ZoneId.systemDefault());
            LocalDate u2CreateDate = LocalDate.ofInstant(u2.getJobCreatedDate(), ZoneId.systemDefault());
            return u1CreateDate.compareTo(u2CreateDate);
        };
        Comparator<PerformanceDataVO> recruiterComparator = (u1, u2) -> {
            if (u1.getRecruiter() == null && u2.getRecruiter() == null) {
                return 0;
            }
            if (u1.getRecruiter() == null) {
                return -1;
            }
            if (u2.getRecruiter() == null) {
                return 1;
            }
            return u1.getRecruiter().compareTo(u2.getRecruiter());
        };

        return submitDateComparator.thenComparing(createDateComparator).thenComparing(recruiterComparator);
    }
    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            map.put(key, values);
            return doSearchData(queryStr, clazz, map);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new RuntimeException("query sql in condition list > 1000 more than 1");
        }
        return keyList.get(0);
    }

    private Long searchCount(String queryStr, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        map.forEach((k,v) -> ReflectUtil.invoke(query, method, k, v));
        return Long.parseLong(String.valueOf(query.getSingleResult()));
    }

}
























