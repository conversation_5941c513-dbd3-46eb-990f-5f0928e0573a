package com.altomni.apn.finance.service.invoicing.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import com.altomni.apn.common.domain.enumeration.company.*;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.calendar.CompleteSystemCalendarDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.InvoiceTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NoPermissionException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.finance.config.env.ApplicationProperties;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.invoicing.*;
import com.altomni.apn.finance.domain.start.StartFteRate;
import com.altomni.apn.finance.repository.invoicing.*;
import com.altomni.apn.finance.repository.start.StartFteRateRepository;
import com.altomni.apn.finance.service.common.HttpService;
import com.altomni.apn.finance.service.company.CompanyService;
import com.altomni.apn.finance.service.dto.invoicing.*;
import com.altomni.apn.finance.service.dto.start.StartInfoForInvoicingVO;
import com.altomni.apn.finance.service.dto.start.StartSearchInvoicingDTO;
import com.altomni.apn.finance.service.invoicing.InvoicingApplicationInfoService;
import com.altomni.apn.finance.service.invoicing.InvoicingCandidateInfoService;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.finance.service.system.SequenceService;
import com.altomni.apn.finance.service.user.UserService;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.finance.service.vo.invoicing.*;
import com.altomni.apn.finance.utils.InvoicingUtils;
import com.altomni.apn.user.service.calendar.CalendarService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InvoicingApplicationInfoServiceImpl implements InvoicingApplicationInfoService {

    @Resource
    private InvoicingNativeRepository invoicingNativeRepository;

    @Resource
    private InvoicingApplicationInfoRepository invoicingApplicationInfoRepository;

    @Resource
    private InvoicingCandidateInfoRepository invoicingCandidateInfoRepository;

    @Resource
    private InvoicingApplicationNotesAttachmentRepository invoicingApplicationNotesAttachmentRepository;

    @Resource
    SequenceService sequenceService;

    @Resource
    private InvoicingRecordLogRepository invoicingRecordLogRepository;

    @Resource
    private InvoicingRecordPaymentInfoRepository invoicingRecordPaymentInfoRepository;

    @Resource
    InvoicingRecordPaymentDetailRepository invoicingRecordPaymentDetailRepository;

    @Resource
    CompanyService companyService;

    @Resource
    UserService userService;

    @Resource
    EnumInvoicingServiceTaxRepository enumInvoicingServiceTaxRepository;

    @Resource
    PrepaymentInvoicingRelationRepository prepaymentInvoicingRelationRepository;

    @Resource
    InvoicingCandidateDetailRepository invoicingCandidateDetailRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Resource
    CommonRedisService commonRedisService;

    @Resource
    private StartFteRateRepository startFteRateRepository;

    @Resource
    private InvoicingCandidateInfoService invoicingCandidateInfoService;

    @Resource
    protected InitiationService initiationService;

    @Resource
    protected StartService startService;

    @Resource
    private InvoicingOutstandingCloseRepository invoicingOutstandingCloseRepository;

    @Resource
    private HttpService httpService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Value("${application.crmUrl}")
    private String crmUrl;

    @Override
    public Page<InvoicingApplicationInfoVO> searchAll(InvoicingApplicationInfoSearchDTO dto, Pageable pageable) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateChinaInvoicingDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        return invoicingNativeRepository.searchGroupInvoiceList(dto, pageable, teamDataPermission);
    }

    @Override
    public void download(HttpServletResponse response, InvoicingApplicationInfoSearchDTO dto) {
        try {
            String fileName = "Invoicing_" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD) + ".xls";
            ExcelUtil.getResource(response,
                    fileName
                    , InvoicingUtils.INVOICING_DOCUMENTS_HEADERS
                    , InvoicingUtils.INVOICING_DOCUMENTS_FIELDS, getList(dto)
                    , null, null, null);
        } catch (Exception e) {
            log.error("[invoicing download]: error:{}", e);
        }
    }

    private List<Object> getList(InvoicingApplicationInfoSearchDTO dto) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateChinaInvoicingDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        List<InvoicingApplicationInfoDownloadVO> voList = invoicingNativeRepository.searchGroupInvoiceListDownload(dto, teamDataPermission);
        List<Long> idList = voList.stream().map(InvoicingApplicationInfoDownloadVO::getId).map(v -> v.longValue()).toList();
        List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdInAndStatus(idList, 1);
        Map<Long, List<InvoicingRecordPaymentInfo>> mapPaymentInfo = paymentInfos.stream().collect(Collectors.groupingBy(InvoicingRecordPaymentInfo::getInvoicingId));

        List<InvoicingCandidateDetail> invoicingCandidateDetailList = invoicingCandidateDetailRepository.findByInvoiceApplicationIdInAndStatus(idList, 1);
        Map<Long, List<InvoicingCandidateDetail>> mapCandidateDetail = invoicingCandidateDetailList.stream().collect(Collectors.groupingBy(InvoicingCandidateDetail::getInvoiceApplicationId));

        List<Long> userIdList = invoicingCandidateDetailList.stream().map(InvoicingCandidateDetail::getUserId).collect(Collectors.toList());
        List<UserLeaderVO> userLeaderVOS = invoicingNativeRepository.searchLeaderByUserId(userIdList);
        Map<BigInteger, List<UserLeaderVO>> userLeaderMap = userLeaderVOS.stream().collect(Collectors.groupingBy(UserLeaderVO::getUserId));

        List<UserLeaderVO> teamCodeList = invoicingNativeRepository.searchTeamCodeByUserId(userIdList);
        Map<BigInteger, String> teamCodeMap = teamCodeList.stream().collect(Collectors.toMap(UserLeaderVO::getUserId, UserLeaderVO::getTeamCode));

        List<PermissionTeamLeaderProfitVO> profitOwnerProperties = invoicingNativeRepository.searchPermissionTeamLeaderProfit();

        List<InvoicingApplicationInfoDownloadVO> downloadVOS = new ArrayList<>();
        Map<BigInteger, List<InvoicingApplicationInfoDownloadVO>> mapInvoicingAppInfo = voList.stream().collect(Collectors.groupingBy(InvoicingApplicationInfoDownloadVO::getId));
        mapInvoicingAppInfo.forEach((k, v) -> {
           /* BigDecimal gpAmount = v.stream().filter(p -> p.getAmountReceived() != null)
                    .map(p -> p.getAmountReceived())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
*/
            List<InvoicingCandidateDetail> candidateDetailList = mapCandidateDetail.get(k.longValue());
            Map<Long, List<InvoicingCandidateDetail>> talentMap = new HashMap<>();
            if (null != candidateDetailList) {
                talentMap = candidateDetailList.stream().collect(Collectors.groupingBy(x -> x.getTalentId()));
            }

            //计算各种值
            for (InvoicingApplicationInfoDownloadVO vo : v) {
                if (vo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.getDescription())) {
                    vo.setUninvoicedAmount(new BigDecimal("0.00"));
                } else {
                    //应收金额(含税)
                    Double tax = 1.0;
                    if (vo.getTaxPayerType() != null && StringUtils.equals(TaxPayerType.CLIENT.getDescription(), vo.getTaxPayerType())) {
                        tax = 1 + vo.getInvoicingTax() / 100;
                    }
                    BigDecimal amountReceivedTax = vo.getAmountReceived().multiply(BigDecimal.valueOf(tax)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    vo.setAmountReceivedTax(amountReceivedTax);
                    vo.setInvoicingAmount(amountReceivedTax);
                    vo.setInvoiceTax(amountReceivedTax.subtract(vo.getAmountReceived()));

                    BigDecimal gpAmount = vo.getTalentGpAmount().multiply(BigDecimal.valueOf(tax)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    vo.setGpAmount(gpAmount);
                    vo.setUninvoicedAmount(vo.getGpAmount().subtract(amountReceivedTax));
                }

                if (StringUtils.isNotBlank(vo.getInvoiceFormat())) {
                    List<String> formatStr = new ArrayList<>();
                    String[] formats = vo.getInvoiceFormat().split(",");
                    for (int i = 0; i < formats.length; i++) {
                        if (formats[i].equals("0")) {
                            formatStr.add("Pdf");
                        } else if (formats[i].equals("1")) {
                            formatStr.add("Ofd");
                        } else if (formats[i].equals("2")) {
                            formatStr.add("Xml");
                        }
                    }
                    vo.setInvoiceFormat(StringUtils.join(formatStr, ","));
                }

                BigDecimal payAmount = BigDecimal.ZERO;
                List<InvoicingRecordPaymentInfo> paymentInfoList = mapPaymentInfo.get(vo.getId().longValue());
                if (null != paymentInfoList) {
                    for (InvoicingRecordPaymentInfo pay : paymentInfoList) {
                        if (vo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.getDescription())) {
                            payAmount = payAmount.add(pay.getPaymentAmount());
                        } else {
                            if (StringUtils.isNotBlank(pay.getCandidateJson())) {
                                JSONArray objArry = JSONUtil.parseArray(pay.getCandidateJson());
                                for (int i = 0; i < objArry.size(); i++) {
                                    JSONObject obj = objArry.getJSONObject(i);
                                    Long talentId = obj.getLong("talentId");
                                    Long jobId = obj.getLong("jobId");
                                    if (talentId.equals(vo.getTalentId().longValue()) && jobId.equals(vo.getJobId().longValue())) {
                                        String paymentAmount = obj.getStr("paymentAmount");
                                        if (StringUtils.isNotBlank(paymentAmount)) {
                                            payAmount = payAmount.add(new BigDecimal(paymentAmount));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                vo.setPaymentAmount(payAmount);
                if (vo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.getDescription())) {
                    vo.setAmountDue(vo.getInvoicingAmount().subtract(vo.getPaymentAmount()));
                } else {
                    if (vo.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION.getDescription())
                            && StringUtils.isNotBlank(vo.getVoidFlag())) {
                        if (vo.getAmountReceivedTax().subtract(vo.getPaymentAmount()).compareTo(new BigDecimal("0.00")) < 0) {
                            vo.setAmountDue(BigDecimal.ZERO);
                        } else {
                            vo.setAmountDue(vo.getAmountReceivedTax().subtract(vo.getPaymentAmount()));
                        }
                    } else {
                        if (StringUtils.isBlank(vo.getCloseFlag())) {
                            vo.setAmountDue(BigDecimal.ZERO);
                        } else {
                            if (vo.getAmountReceivedTax().subtract(vo.getPaymentAmount()).compareTo(new BigDecimal("0.00")) < 0) {
                                vo.setAmountDue(BigDecimal.ZERO);
                            } else {
                                vo.setAmountDue(vo.getAmountReceivedTax().subtract(vo.getPaymentAmount()));
                            }
                        }
                    }
                }

                if (null != talentMap && !talentMap.isEmpty()) {
                    if (talentMap.containsKey(vo.getTalentId().longValue())) {
                        List<InvoicingCandidateDetail> talentList = talentMap.get(vo.getTalentId().longValue());
                        Map<Long, List<InvoicingCandidateDetail>> userRoleList = talentList.stream().collect(Collectors.groupingBy(InvoicingCandidateDetail::getUserId));
                        userRoleList.forEach((userId, va) -> {
                            InvoicingApplicationInfoDownloadVO copyDownloadVo = new InvoicingApplicationInfoDownloadVO();
                            BeanUtils.copyProperties(vo, copyDownloadVo);
                            Double percentage = 0.0;
                            List<String> roles = new ArrayList<>();
                            for (InvoicingCandidateDetail detail : va) {
                                percentage = percentage + Double.valueOf(detail.getPercentage().replace("%", ""));
                                //1、am 2、recruiter 3、dm 4、sourcer 5、owner 6、bdManager 7、salesLeadManager 8、coam
                                if (detail.getUserRole() != null) {
                                    roles.add(getUserRole(detail.getUserRole()));
                                }
                            }
                            copyDownloadVo.setParticipateName(va.get(0).getUserName());
                            roles = roles.stream().distinct().collect(Collectors.toList());
                            copyDownloadVo.setParticipateRole(StringUtils.join(roles, ","));
                            copyDownloadVo.setParticipateUserPercentage(percentage + "%");

                            BigDecimal participateGp = copyDownloadVo.getInvoicingAmount().multiply(BigDecimal.valueOf(percentage)).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                            copyDownloadVo.setParticipateUserGp(participateGp.toString());

                            BigDecimal participateCashGp = copyDownloadVo.getPaymentAmount().multiply(BigDecimal.valueOf(percentage)).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                            copyDownloadVo.setParticipateCashGp(participateCashGp.toString());

                            if (null != userLeaderMap && !userLeaderMap.isEmpty()) {
                                if (userLeaderMap.containsKey(BigInteger.valueOf(userId))) {
                                    List<UserLeaderVO> leaderVOS = userLeaderMap.get(BigInteger.valueOf(userId));
                                    List<String> leaderNames = leaderVOS.stream().map(UserLeaderVO::getLeaderUserName).collect(Collectors.toList());
                                    copyDownloadVo.setParticipateTeamLeader(StringUtils.join(leaderNames, ","));
                                }
                            }


                            if (null != teamCodeMap && !teamCodeMap.isEmpty() && null != profitOwnerProperties) {
                                if (teamCodeMap.containsKey(BigInteger.valueOf(userId))) {
                                    String teamCode = teamCodeMap.get(BigInteger.valueOf(userId));
                                    for (PermissionTeamLeaderProfitVO profit : profitOwnerProperties) {
                                        if (teamCode.startsWith(profit.getCode())) {
                                            copyDownloadVo.setProfitOwner(profit.getUserName());
                                            break;
                                        }
                                    }
                                }
                            }

                            downloadVOS.add(copyDownloadVo);
                        });
                    }
                } else {
                    downloadVOS.add(vo);
                }
            }

        });
        return new ArrayList<>(downloadVOS);
    }

    private String getUserRole(Integer role) {
        String value = null;
        if (role.equals(1)) {
            value = "AM";
        } else if (role.equals(2)) {
            value = "Recruiter";
        } else if (role.equals(3)) {
            value = "DM";
        } else if (role.equals(4)) {
            value = "Sourcer";
        } else if (role.equals(5)) {
            value = "Owner";
        } else if (role.equals(6)) {
            value = "Sales Lead Owner";
        } else if (role.equals(7)) {
            value = "BD Owner";
        } else if (role.equals(8)) {
            value = "CoAm";
        }
        return value;
    }

    @Override
    public InvoicingApplicationPrepaymentVO findPrepaymentByCompanyId(Long companyId, InvoicingBusinessType invoicingBusinessType) {
        List<InvoicingApplicationInfo> infos = invoicingApplicationInfoRepository.findByCompanyId(companyId, 1,
                Arrays.asList(InvoicingStatus.FULLY_COLLECTED.toDbValue(), InvoicingStatus.PARTIALLY_COLLECTED.toDbValue(), InvoicingStatus.OVERDUE.toDbValue())
                , InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue(), SecurityUtils.getTenantId(), invoicingBusinessType.toDbValue());
        InvoicingApplicationPrepaymentVO vo = new InvoicingApplicationPrepaymentVO();
        if (!infos.isEmpty()) {
            BigDecimal totalBalance = infos.stream()
                    .map(p -> p.getAmountDue())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setInvoicingAmount(totalBalance);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvoicingApplicationInfoViewVO save(InvoicingApplicationInfoDTO dto) {

        InvoicingApplicationInfoViewVO vo = new InvoicingApplicationInfoViewVO();

        String sequence = getSequence(dto);
        if (sequence == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_GETSEQUENCE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        String hashCode = InvoicingUtils.getHashValue(dto);
        final String key = "invoicing:save:" + hashCode;
        if (Objects.equals(commonRedisService.setNxAndExpire(key, "1", 120L), 1L)) {

            String note = SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "提交%s，等待财务进行审批。";
            if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION)) {

                if (dto.getCandidateInfoList() == null || dto.getCandidateInfoList().isEmpty()) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                }

                checkCandidateInfo(dto);
                if (!SecurityUtils.isAdmin()) {
                    // 判断是不是am ac dm 等
                    if (!invoicingNativeRepository.selectIsStartAm(dto.getCompanyId(), Arrays.asList(0, 1, 2, 3, 5, 7, 8, 9))) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.NOT_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                    }
                }
                InvoicingApplicationInfo info = assemblingInvoicingDate(dto, sequence, InvoicingApplicationType.INVOICING_APPLICATION);
                invoicingApplicationInfoRepository.save(info);
                log.info("[invoicing save]: save info param:{}", JSONUtil.toJsonStr(info));

                //保存候选人数据
                BigDecimal prepayment = assemblingCandidateDate(dto, info.getId(), new ArrayList<>(), true);
                log.info("[invoicing save]: finish save candidate info");

                //保存开票附件
                addInvoiceAttachment(dto, info.getId());
                log.info("[invoicing save]: finish save attachment info");

                String subTitle = String.format(note, "开票申请");
                if (!prepayment.equals(BigDecimal.ZERO)) {
                    subTitle = subTitle + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "使用预付金抵扣" + prepayment + "。";
                }

                addLogInfo("提交开票申请", subTitle, info.getId(), null, null, InvoicingLogStatus.APPLICATION, "save", null, null, null);

                vo.setId(info.getId());
            } else if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION)) {
                if (dto.getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE)) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_TYPE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                }
                InvoicingApplicationInfo info = assemblingInvoicingDate(dto, sequence, InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION);
                //系统添加预付金
                info.setPrepaymentType(2);
                info.setAmountDue(BigDecimal.ZERO);
                invoicingApplicationInfoRepository.save(info);
                log.info("invoicing save: save prepayment invoicing info param:{}", JSONUtil.toJsonStr(info));
                addLogInfo("提交预付金开票申请", String.format(note, "预付金开票申请"), info.getId(), null, null, InvoicingLogStatus.APPLICATION, "save", null, null, null);

                //保存开票附件
                addInvoiceAttachment(dto, info.getId());
                log.info("[invoicing save]: finish save attachment info");

                vo.setId(info.getId());
            } else if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.VOID_INVOICING_APPLICATION)) {

                //校验原始数据
                InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(dto.getInvalidInvoicingId(), true, Arrays.asList(UserRole.AM.toDbValue(), UserRole.CO_AM.toDbValue()));

                if (applicationInfo.getStatus().equals(0) || !InvoicingUtils.voidInvoicingStatus.contains(applicationInfo.getInvoicingStatus()) || applicationInfo.getVoidInvoicing() != null) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                }

                List<InvoicingApplicationInfo> infos = invoicingApplicationInfoRepository.findByInvalidInvoicingIdAndStatus(applicationInfo.getId(), 1);
                if (!infos.isEmpty()) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_VOID_APPLICATION_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                }

                InvoicingApplicationInfo info = assemblingInvoicingDate(dto, sequence, InvoicingApplicationType.fromDbValue(applicationInfo.getInvoicingApplicationType()));
                info.setInvalidInvoicingId(dto.getInvalidInvoicingId());
                info.setInvoicingReason(dto.getInvoicingReason());
                //废票标识
                info.setVoidInvoicing(1);
                if (dto.getInvoicingServerTaxId() == null) {
                    info.setInvoicingServerTaxId(0L);
                }

                invoicingApplicationInfoRepository.save(info);
                log.info("[invoicing save]: save void invoicing info param:{}", JSONUtil.toJsonStr(info));

                //保存开票附件
                addInvoiceAttachment(dto, info.getId());
                log.info("[invoicing save]: finish save attachment info");

                if (null != dto.getCandidateInfoList()) {
                    //保存候选人数据
                    assemblingCandidateDate(dto, info.getId(), new ArrayList<>(), false);
                    log.info("[invoicing save]: finish save candidate info");
                }

                String codeNumber = applicationInfo.getElectronicInvoiceNumber();
                String newStr = "被红冲蓝字数电票号码";
                if (applicationInfo.getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE)) {
                    codeNumber = applicationInfo.getCodeNumber();
                    newStr = "原始发票开票编码";
                }
                addLogInfo("提交废票申请", String.format(note, "废票申请"), info.getId(), newStr, null, InvoicingLogStatus.APPLICATION, "save", codeNumber, applicationInfo.getId(), null);

                vo.setId(info.getId());

            }
            commonRedisService.delete(key);
        } else {
            throw new CustomParameterizedException("Too many requests, please try again later");
        }
        return vo;
    }

    private void addInvoiceAttachment(InvoicingApplicationInfoDTO dto, Long id) {
        if (null != dto.getInvoicingUrlList() && !dto.getInvoicingUrlList().isEmpty()) {
            List<InvoicingApplicationNotesAttachment> attachments = new ArrayList<>();
            dto.getInvoicingUrlList().forEach(v -> {
                InvoicingApplicationNotesAttachment vo = new InvoicingApplicationNotesAttachment();
                vo.setAttachmentUrl(v.getInvoicingUrl());
                vo.setInvoiceApplicationId(id);
                vo.setStatus(1);
                vo.setCreatedBy(SecurityUtils.getUserId() + "");
                vo.setCreatedDate(Instant.now());
                attachments.add(vo);
            });

            if (!attachments.isEmpty()) {
                invoicingApplicationNotesAttachmentRepository.saveAll(attachments);
            }
        }
    }

    private BigDecimal assemblingCandidateDate(InvoicingApplicationInfoDTO dto, Long id, List<InvoicingApplicationInfo> prepaymentInfoList, boolean flag) {

//        //查询bd manager
//        List<Long> bdInfos = dto.getCandidateInfoList().stream().map(InvoicingCandidateInfoDTO::getJobId).collect(Collectors.toList());
//        List<InvoicingKpiUserRoleVO> bdManagerVOs = invoicingNativeRepository.selectBdManagerByBusinessId(bdInfos);
//        Map<BigInteger, List<InvoicingKpiUserRoleVO>> bdManagerMap = bdManagerVOs.stream().collect(Collectors.groupingBy(InvoicingKpiUserRoleVO::getId));


        List<Long> amInfos = dto.getCandidateInfoList().stream().map(InvoicingCandidateInfoDTO::getStartId).collect(Collectors.toList());
        List<InvoicingKpiUserRoleVO> kpiUserRoleVOs = invoicingNativeRepository.selectKpiUserRoleByStartIds(amInfos);
        Map<BigInteger, List<InvoicingKpiUserRoleVO>> kpiUserMap = kpiUserRoleVOs.stream().collect(Collectors.groupingBy(InvoicingKpiUserRoleVO::getId));

        //候选人信息
        List<InvoicingCandidateInfo> candidateInfoList = new ArrayList<>();
        //候选人明细
        List<InvoicingCandidateDetail> detailList = new ArrayList<>();
        dto.getCandidateInfoList().forEach(ca -> {
            InvoicingCandidateInfo bean = new InvoicingCandidateInfo();
            bean.setInvoiceApplicationId(id);
            bean.setTalentId(ca.getTalentId());
            bean.setTalentName(ca.getTalentName());
            bean.setCompanyId(ca.getCompanyId());
            bean.setCompanyName(ca.getCompanyName());
            bean.setJobId(ca.getJobId());
            bean.setJobName(ca.getJobName());
            bean.setStartId(ca.getStartId());
            bean.setGpAmount(ca.getGpAmount());
            if (ca.getAmountPercentage() != null) {
                bean.setAmountPercentage(ca.getAmountPercentage());
            }
            bean.setAmountReceived(ca.getAmountReceived());
            Double tax = 1.0;
            if (TaxPayerType.CLIENT.equals(dto.getTaxPayerType())) {
                tax = 1 + dto.getInvoicingTax() / 100;
            }
            BigDecimal invoicingAmount = ca.getAmountReceived().multiply(BigDecimal.valueOf(tax)).setScale(2, BigDecimal.ROUND_HALF_UP);
            bean.setAmountReceivedTax(invoicingAmount);

            if (ca.getPrepaymentAmount() != null) {
                bean.setAmountPrepayment(ca.getPrepaymentAmount());
            }
            bean.setInvoiceArea(ca.getChinaOrder() ? 1 : 0);
            bean.setStatus(1);
            if (ca.getExpectedInvoicingDate() != null) {
                bean.setExpectedInvoicingDate(ca.getExpectedInvoicingDate());
            }
            if (ca.getExpectedInvoicingAmount() != null) {
                bean.setExpectedInvoicingAmount(ca.getExpectedInvoicingAmount());
            }
            // am info
            if (kpiUserMap.containsKey(BigInteger.valueOf(ca.getStartId()))) {
                List<InvoicingKpiUserRoleVO> voList = kpiUserMap.getOrDefault(BigInteger.valueOf(ca.getStartId()), new ArrayList<>());
                List<InvoicingCandidateDetail> am = getRoleAndPercentage(voList, "0", id, 1, ca.getTalentId(), ca.getStartId());
                detailList.addAll(am);

                List<InvoicingCandidateDetail> recruiter = getRoleAndPercentage(voList, "1", id, 2, ca.getTalentId(), ca.getStartId());
                detailList.addAll(recruiter);

                List<InvoicingCandidateDetail> sourcer = getRoleAndPercentage(voList, "2", id, 4, ca.getTalentId(), ca.getStartId());
                detailList.addAll(sourcer);

                List<InvoicingCandidateDetail> dm = getRoleAndPercentage(voList, "3", id, 3, ca.getTalentId(), ca.getStartId());
                detailList.addAll(dm);

                List<InvoicingCandidateDetail> owner = getRoleAndPercentage(voList, "4", id, 5, ca.getTalentId(), ca.getStartId());
                detailList.addAll(owner);

                List<InvoicingCandidateDetail> coAm = getRoleAndPercentage(voList, "7", id, 8, ca.getTalentId(), ca.getStartId());
                detailList.addAll(coAm);

                List<InvoicingCandidateDetail> salesLeadOwner = getRoleAndPercentage(voList, "9", id, 6, ca.getTalentId(), ca.getStartId());
                detailList.addAll(salesLeadOwner);

                List<InvoicingCandidateDetail> bdOwner = getRoleAndPercentage(voList, "8", id, 7, ca.getTalentId(), ca.getStartId());
                detailList.addAll(bdOwner);
            }

            candidateInfoList.add(bean);
        });

        invoicingCandidateInfoRepository.saveAll(candidateInfoList);
        log.info("[invoicing save]: save candidate info param:{}", JSONUtil.toJsonStr(candidateInfoList));

        if (!detailList.isEmpty()) {
            candidateInfoList.forEach(v -> {
                detailList.forEach(x -> {
                    if (x.getTalentId().equals(v.getTalentId()) && x.getStartId().equals(v.getStartId()) && x.getInvoiceApplicationId().equals(v.getInvoiceApplicationId())) {
                        x.setInvoiceCandidateId(v.getId());
                    }
                });
            });
            invoicingCandidateDetailRepository.saveAll(detailList);
            log.info("[invoicing save]: save candidate detail param:{}", JSONUtil.toJsonStr(detailList));
        }

        if (flag) {
            BigDecimal prepaymentAmountSum = dto.getCandidateInfoList().stream().filter(p -> p.getPrepaymentAmount() != null)
                    .map(p -> p.getPrepaymentAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (prepaymentAmountSum.compareTo(BigDecimal.ZERO) > 0) {
                log.info("[invoicing save]: start save prepayment record amount:{}", prepaymentAmountSum);
                savePrepaymentInfo(dto.getCompanyId(), id, prepaymentAmountSum, dto.getInvoicingAmount(), dto.getCandidateInfoList(), prepaymentInfoList, dto.getInvoicingBusinessType(), false);
                return prepaymentAmountSum;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 使用预付金信息
     *
     * @param companyId
     * @param invoicingId
     * @param prepaymentAmountSum   预付金金额
     * @param invoicingAmount       付款总金额
     * @param candidateInfoList
     * @param prepaymentInfoList
     * @param invoicingBusinessType
     * @param flag                  true 抵扣类型使用预付金
     * @return 返回实际扣除预付金金额
     */
    private BigDecimal savePrepaymentInfo(Long companyId, Long invoicingId, BigDecimal prepaymentAmountSum, Double invoicingAmount,
                                          List<InvoicingCandidateInfoDTO> candidateInfoList, List<InvoicingApplicationInfo> prepaymentInfoList,
                                          InvoicingBusinessType invoicingBusinessType, boolean flag) {

        //预付金
        List<InvoicingApplicationInfo> prepaymentApplicationList = invoicingApplicationInfoRepository.findByCompanyId(companyId, 1,
                Arrays.asList(InvoicingStatus.FULLY_COLLECTED.toDbValue(), InvoicingStatus.PARTIALLY_COLLECTED.toDbValue(), InvoicingStatus.OVERDUE.toDbValue()),
                InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue(), SecurityUtils.getTenantId(), invoicingBusinessType.toDbValue());
        if (flag) {
            if (null == prepaymentApplicationList || prepaymentApplicationList.isEmpty()) {
                return null;
            }
        } else {
            if (null == prepaymentApplicationList || prepaymentApplicationList.isEmpty()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_PREPAYMENT_INSUFFICIENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
        }

        /**
         * 修改发票时候 还原了预付金金，但是可能没刷到数据库里面，替换一下金额
         */
        if (!prepaymentInfoList.isEmpty()) {
            prepaymentApplicationList.forEach(v -> {
                for (InvoicingApplicationInfo a : prepaymentInfoList) {
                    if (a.getId().equals(v.getId())) {
                        v.setAmountDue(a.getAmountDue());
                    }
                }
            });
        }

        BigDecimal totalBalance = prepaymentApplicationList.stream()
                .map(p -> p.getAmountDue())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal paymentAmount = prepaymentAmountSum;
        //抵扣发票类型，预付金小于待支付金额
        if (!flag) {
            if (prepaymentAmountSum.compareTo(totalBalance) > 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_PREPAYMENT_INSUFFICIENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
        } else {
            if (prepaymentAmountSum.compareTo(totalBalance) > 0) {
                paymentAmount = totalBalance;
            }
        }

        //支付信息
        InvoicingRecordPaymentInfo info = new InvoicingRecordPaymentInfo();
        info.setInvoicingId(invoicingId);
        info.setPaymentDate(Instant.now());
        info.setPaymentMethod(InvoicingPaymentMethodType.PREPAYMENT);
        info.setPaymentAmount(paymentAmount);
        //预付金类型回款
        info.setPrepaymentId(1L);
        info.setStatus(1);
        JSONArray candidateArray = new JSONArray();
        List<InvoicingRecordPaymentDetail> paymentDetails = new ArrayList<>();

        //抵扣类型需要顺序扣款，增加候选人带扣款金额
        BigDecimal candidatePayAmount = paymentAmount;
        for (InvoicingCandidateInfoDTO v : candidateInfoList) {
            InvoicingRecordPaymentDetail detail = new InvoicingRecordPaymentDetail();

            JSONObject json = new JSONObject();
            json.put("gpAmount", v.getGpAmount());
            json.put("talentId", v.getTalentId());
            json.put("talentName", v.getTalentName());
            json.put("jobId", v.getJobId());
            if (flag) {
                if (candidatePayAmount.compareTo(new BigDecimal("0.00")) == 0) {
                    json.put("paymentAmount", BigDecimal.ZERO);
                } else {
                    BigDecimal x = v.getPaymentAmount().subtract(candidatePayAmount);
                    if (x.compareTo(new BigDecimal("0.00")) == 0) {
                        json.put("paymentAmount", v.getPaymentAmount());
                        candidatePayAmount = BigDecimal.ZERO;
                    } else if (x.compareTo(new BigDecimal("0.00")) > 0) {
                        json.put("paymentAmount", candidatePayAmount);
                        candidatePayAmount = BigDecimal.ZERO;
                    } else if (x.compareTo(new BigDecimal("0.00")) == -1) {
                        candidatePayAmount = candidatePayAmount.subtract(v.getPaymentAmount());
                        json.put("paymentAmount", v.getPaymentAmount());
                    }
                }
            } else {
                json.put("paymentAmount", v.getPrepaymentAmount());
            }

            detail.setTalentId(json.getLong("talentId"));
            detail.setTalentName(json.getStr("talentName"));
            detail.setGpAmount(json.getBigDecimal("gpAmount"));
            detail.setPaymentAmount(json.getBigDecimal("paymentAmount"));
            detail.setPaymentDate(info.getPaymentDate());
            detail.setJobId(json.getLong("jobId"));
            detail.setStatus(1);
            paymentDetails.add(detail);
            candidateArray.add(json);
        }

        info.setCandidateJson(JSONUtil.toJsonStr(candidateArray));
        invoicingRecordPaymentInfoRepository.save(info);
        log.info("[invoicing save]: save payment info, param:{}", JSONUtil.toJsonStr(info));

        if (!paymentDetails.isEmpty()) {
            paymentDetails.forEach(v -> {
                v.setPaymentId(info.getId());
            });

            invoicingRecordPaymentDetailRepository.saveAll(paymentDetails);
            log.info("[invoicing save]: save payment talent detail");
        }

        //组装预付金与支付信息关系
        List<PrepaymentInvoicingRelation> relations = new ArrayList<>();

        //支付金额
        BigDecimal prepayment = prepaymentAmountSum;
        for (InvoicingApplicationInfo pre : prepaymentApplicationList) {
            if (pre.getAmountDue().equals(BigDecimal.ZERO)) {
                continue;
            }
            if (prepayment.compareTo(new BigDecimal("0.00")) == 0) {
                break;
            }
            BigDecimal x = pre.getAmountDue().subtract(prepayment);
            if (x.compareTo(new BigDecimal("0.00")) == 0) {
                relations.add(createPaymentRelationInfo(invoicingId, info.getId(), prepayment, pre.getId()));
                pre.setAmountDue(BigDecimal.ZERO);
                prepayment = BigDecimal.ZERO;
            } else if (x.compareTo(new BigDecimal("0.00")) > 0) {
                relations.add(createPaymentRelationInfo(invoicingId, info.getId(), prepayment, pre.getId()));
                pre.setAmountDue(x);
                prepayment = BigDecimal.ZERO;
            } else if (x.compareTo(new BigDecimal("0.00")) == -1) {
                relations.add(createPaymentRelationInfo(invoicingId, info.getId(), pre.getAmountDue(), pre.getId()));
                prepayment = prepayment.subtract(pre.getAmountDue());
                pre.setAmountDue(BigDecimal.ZERO);
            }
        }

        //插入回款记录
        if (!relations.isEmpty()) {
            prepaymentInvoicingRelationRepository.saveAll(relations);
            log.info("[invoicing save]: save payment invoicing relation, param:{}", JSONUtil.toJsonStr(relations));
        }

        if (flag) {
            final String title = "回款-预付金";
            final String subTitle = DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + " 创建预付金回款记录";
            String paymentAmountStr = null;
            String amountDueStr = null;
            if (prepayment.compareTo(new BigDecimal("0.00")) == 0) {
                paymentAmountStr = invoicingAmount.toString();
                amountDueStr = "0.00";
            } else {
                paymentAmountStr = BigDecimal.valueOf(invoicingAmount).subtract(prepayment).toString();
                amountDueStr = prepayment.toString();
            }
            //插入日志
            addLogInfo(title, subTitle, invoicingId, null, getLogJson(amountDueStr, paymentAmountStr, DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS), InvoicingPaymentMethodType.PREPAYMENT), InvoicingLogStatus.PAYMENT, "save", null, null, null);
        }

        //更新预付金金额
        invoicingApplicationInfoRepository.saveAll(prepaymentApplicationList);
        log.info("[invoicing save]: prepayment update amount due,param:{}", JSONUtil.toJsonStr(prepaymentApplicationList));

        return prepayment;
    }

    /**
     * 支付与预付金关系
     *
     * @param invoicingId
     * @param paymentId    支付id
     * @param amount
     * @param prepaymentId 预付金id
     * @return
     */
    private PrepaymentInvoicingRelation createPaymentRelationInfo(Long invoicingId, Long paymentId, BigDecimal amount, Long prepaymentId) {
        PrepaymentInvoicingRelation relation = new PrepaymentInvoicingRelation();
        relation.setInvoicingId(invoicingId);
        relation.setPaymentId(paymentId);
        relation.setPrepaymentInvoicingId(prepaymentId);
        relation.setInvoicingAmount(amount);
        relation.setRelationType(InvoicingRelationType.USE_RELATION);
        relation.setStatus(1);
        return relation;
    }

    private String getLogJson(String amountDue, String paymentAmount, String date, InvoicingPaymentMethodType type) {
        JSONObject object = new JSONObject();
        object.put("paymentAmount", paymentAmount);
        object.put("amountDue", amountDue);
        object.put("paymentDate", date);
        object.put("paymentMethod", type);
        return JSONUtil.toJsonStr(object);
    }

    /**
     * 记录开票时json
     *
     * @param elecInvoiceNumber
     * @param dueWithinDays
     * @param invoicingDate
     * @param paymentDueDate
     * @return
     */
    private String getLogJsonByInvoicing(String elecInvoiceNumber, String dueWithinDays, Instant invoicingDate, Instant paymentDueDate) {
        JSONObject object = new JSONObject();
        object.put("elecInvoiceNumber", elecInvoiceNumber);
        if (StringUtils.isNotBlank(dueWithinDays)) {
            object.put("dueWithinDays", dueWithinDays);
        }
        if (null != invoicingDate) {
            object.put("invoicingDate", DateUtil.fromInstantToDate(invoicingDate, DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS));
        }
        if (null != paymentDueDate) {
            object.put("paymentDueDate", DateUtil.fromInstantToDate(paymentDueDate, DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS));
        }
        return JSONUtil.toJsonStr(object);
    }

    /**
     * add log info
     *
     * @param title
     * @param invoicingId
     * @param note
     * @param json
     * @return
     */
    private void addLogInfo(String title, String subTitle, Long invoicingId, String note, String json, InvoicingLogStatus status, String method, String electronicInvoiceNumber, Long referenceId, Long voidReferenceId) {
        InvoicingRecordLog recordLog = addLogInfoOperate(title, subTitle, invoicingId, note, json, status, electronicInvoiceNumber, referenceId, voidReferenceId);
        invoicingRecordLogRepository.save(recordLog);
        log.info("[invoicing {}]: save record log info, param:{}", method, JSONUtil.toJsonStr(recordLog));
    }

    /**
     * add log info
     *
     * @param title
     * @param invoicingId
     * @param note
     * @param json
     * @return
     */
    private InvoicingRecordLog addLogInfoOperate(String title, String subTitle, Long invoicingId,
                                                 String note, String json, InvoicingLogStatus status,
                                                 String electronicInvoiceNumber, Long referenceId, Long voidReferenceId) {
        InvoicingRecordLog recordLog = new InvoicingRecordLog();
        recordLog.setInvoicingId(invoicingId);
        recordLog.setInvoicingStatus(status);
        recordLog.setTitle(title);
        recordLog.setOperatedById(SecurityUtils.getUserId());
        recordLog.setOperatedByName(SecurityUtils.getUserName());
        recordLog.setOperatedDate(Instant.now());
        recordLog.setNote(note);
        recordLog.setNoteJson(json);
        recordLog.setSubTitle(subTitle);
        recordLog.setElectronicInvoiceNumber(electronicInvoiceNumber);
        recordLog.setReferenceId(referenceId);
        recordLog.setVoidReferenceId(voidReferenceId);
        return recordLog;
    }

    /**
     * @param voList
     * @param userRole        application kpi user role {@link UserRole}
     * @param invoiceUserRole user role defined for invoice module use ONLY!
     *                        1、am 2、recruiter 3、dm 4、sourcer 5、owner 6、salesLeadManager 7、bdManager 8、coAm
     * @return
     */
    private List<InvoicingCandidateDetail> getRoleAndPercentage(List<InvoicingKpiUserRoleVO> voList, String userRole,
                                                                Long applicationId, Integer invoiceUserRole, Long talentId, Long startId) {
        if (voList.isEmpty()) {
            return new ArrayList<>();
        }

        List<InvoicingCandidateDetail> detailList = new ArrayList<>();
        voList.forEach(vo -> {
            if (vo.getUserRole().equals(userRole)) {
                InvoicingCandidateDetail bean = new InvoicingCandidateDetail();
                bean.setInvoiceApplicationId(applicationId);
                bean.setUserRole(invoiceUserRole);
//                bean.setPercentage(getPercentage(flag, vo.getPercentage()) + "%");
                bean.setPercentage(vo.getPercentage().setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
                bean.setUserId(vo.getUserId().longValue());
                bean.setUserName(vo.getUserName());
                bean.setStatus(1);
                bean.setTalentId(talentId);
                bean.setStartId(startId);
                if (vo.getCountry() != null) {
                    bean.setCountry(vo.getCountry());
                }
                detailList.add(bean);
            }
        });
        return detailList;
    }

    //中国区开票逻辑，已弃用
    private BigDecimal getPercentage(String flag, BigDecimal percentage) {
        if (flag.equals("1")) {
            return percentage.multiply(BigDecimal.valueOf(10)).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return percentage.multiply(BigDecimal.valueOf(90)).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private InvoicingApplicationInfo assemblingInvoicingDate(InvoicingApplicationInfoDTO dto, String sequence, InvoicingApplicationType type) {
        InvoicingApplicationInfo info = new InvoicingApplicationInfo();
        info.setInvoicingStatus(InvoicingStatus.PENDING_APPROVAL);
        info.setCodeNumber(sequence);
        info.setClientInvoicingId(dto.getClientInvoicingId());
        info.setTaxPayerType(dto.getTaxPayerType());
        info.setCompanyId(dto.getCompanyId());
        info.setTenantId(SecurityUtils.getTenantId());
        info.setClientInvoicingId(dto.getClientInvoicingId());
        info.setInvoicingBody(dto.getInvoicingBody());
        info.setInvoicingType(dto.getInvoicingType());
        info.setInvoicingBusinessType(dto.getInvoicingBusinessType());
        info.setInvoicingServerTaxId(dto.getInvoicingServerTaxId());
        info.setInvoicingTax(dto.getInvoicingTax());
        info.setInvoiceFormat(dto.getInvoicingFormat());
        info.setConfirmationLetterUrl(dto.getConfirmationLetterUrl());
        info.setInvoiceUrl(dto.getInvoiceUrl());
        info.setInvoicingAmount(BigDecimal.valueOf(dto.getInvoicingAmount()));
        info.setAmountDue(BigDecimal.valueOf(dto.getInvoicingAmount()));
        info.setInvoiceTax(BigDecimal.valueOf(dto.getInvoiceTax()));
        info.setTaxesNotIncluded(BigDecimal.valueOf(dto.getTaxesNotIncluded()));
        info.setGpAmount(BigDecimal.valueOf(dto.getGpAmount()));
        info.setUninvoicedAmount(BigDecimal.valueOf(dto.getUninvoicedAmount()));
        info.setStatus(1);
        info.setInvoicingApplicationType(type.toDbValue());
        info.setInvoiceNode(dto.getInvoiceNote());
        info.setDueWithinDays(dto.getDueWithinDays());
        info.setNote(dto.getNote());

        if (type.equals(InvoicingApplicationType.INVOICING_APPLICATION)) {
            //开票预付金金额=实际支付金额
            BigDecimal prepaymentAmountSum = dto.getCandidateInfoList().stream().filter(p -> p.getPrepaymentAmount() != null)
                    .map(p -> p.getPrepaymentAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (prepaymentAmountSum.compareTo(BigDecimal.ZERO) > 0) {
                if (prepaymentAmountSum.compareTo(BigDecimal.valueOf(dto.getGpAmount())) == 0) {
                    info.setAmountDue(BigDecimal.ZERO);
                } else {
                    info.setAmountDue(BigDecimal.valueOf(dto.getInvoicingAmount()).subtract(prepaymentAmountSum));
                }
            }
        }
        return info;
    }

    /**
     * 获取编码
     *
     * @param dto
     * @return
     */
    private String getSequence(InvoicingApplicationInfoDTO dto) {
        if (dto.getInvoicingBody().equals(InvoicingBody.SH)) {
            return sequenceService.getCommonSequence(InvoicingBody.SH.name(), 10, "SH", 1000000000L);
        } else if (dto.getInvoicingBody().equals(InvoicingBody.SZ)) {
            return sequenceService.getCommonSequence(InvoicingBody.SZ.name(), 10, "SZ", 1000000000L);
        } else if (dto.getInvoicingBody().equals(InvoicingBody.YT)) {
            return sequenceService.getCommonSequence(InvoicingBody.YT.name(), 10, "YT", 1000000000L);
        }
        return null;
    }

    /**
     * 检查候选人数据、预付金、等信息
     *
     * @param dto
     */
    private void checkCandidateInfo(InvoicingApplicationInfoDTO dto) {
        //应收金额(含税)
        Double tax = 1 + dto.getInvoicingTax() / 100;

        //判断每个候选人应收金额大小
        dto.getCandidateInfoList().forEach(ca -> {

            BigDecimal amountReceivedTax = ca.getAmountReceived().multiply(BigDecimal.valueOf(tax)).setScale(2, BigDecimal.ROUND_HALF_UP);

            if (ca.getAmountReceived() != null && ca.getAmountReceived().compareTo(ca.getGpAmount()) > 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_INPUTAMOUNTGREATERTHANGPAMOUNT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
            if (ca.getPrepaymentAmount() != null && ca.getPrepaymentAmount().compareTo(ca.getGpAmount()) > 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_INPUTAMOUNTGREATERTHANGPAMOUNT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
            if (ca.getPrepaymentAmount() != null && ca.getPrepaymentAmount().compareTo(amountReceivedTax) > 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_INPUTAMOUNTGREATERTHANAMOUNTRECEIVED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(ca.getTalentName()), financeApiPromptProperties.getFinanceService()));
            }
            if (ca.getAmountReceived().compareTo(new BigDecimal("0.00")) == 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_PREPAYMENT_AMOUNT_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
            if (ca.getGpAmount().compareTo(new BigDecimal("0.00")) == 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_PREPAYMENT_AMOUNT_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }

            StartFteRate startFteRate = startFteRateRepository.findByStartId(ca.getStartId());
            BigDecimal gpAmount = startFteRate.getTotalBillAmount();

            BigDecimal useAmount = invoicingCandidateInfoService.getAmountReceived(ca.getStartId(), ca.getTalentId(), ca.getJobId());
            if (!useAmount.equals(BigDecimal.ZERO) && !gpAmount.equals(BigDecimal.ZERO)) {
                gpAmount = gpAmount.subtract(useAmount);
            }

            if (gpAmount.compareTo(ca.getAmountReceived()) < 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_PREPAYMENT_AMOUNT_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
        });

        //校验总金额大小
        /*double amountReceivedSum = dto.getCandidateInfoList().stream().map(InvoicingCandidateInfoDTO::getAmountReceived).mapToDouble(Double::doubleValue).sum();
        double prepaymentAmountSum = dto.getCandidateInfoList().stream().map(InvoicingCandidateInfoDTO::getPrepaymentAmount).mapToDouble(Double::doubleValue).sum();
        BigDecimal total = BigDecimal.valueOf(prepaymentAmountSum);
        //total = total.add(BigDecimal.valueOf(prepaymentAmountSum));
        if (total.compareTo(BigDecimal.valueOf(amountReceivedSum)) > 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_INPUTAMOUNTGREATERTHANGPAMOUNT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }*/

        if (dto.getInvoicingAmount() == 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_PREPAYMENT_AMOUNT_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Set<Long> companyIds = dto.getCandidateInfoList().stream().map(InvoicingCandidateInfoDTO::getCompanyId).collect(Collectors.toSet());
        if (companyIds.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_MULT_COMPANY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
    }

    private InvoicingApplicationInfo checkStatusAndPermission(Long id, Boolean flag, List<Integer> userRole) {

        if (id == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Optional<InvoicingApplicationInfo> applicationInfoOpt = invoicingApplicationInfoRepository.findById(id);
        if (!applicationInfoOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        InvoicingApplicationInfo applicationInfo = applicationInfoOpt.get();
        if (!SecurityUtils.isAdmin() && flag) {
            if (applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION)) {
                if (!applicationInfo.getTenantId().equals(SecurityUtils.getTenantId())) {
                    if (userRole != null) {
                        // 判断是不是am
                        if (!invoicingNativeRepository.selectIsStartAm(applicationInfo.getCompanyId(), userRole)) {
                            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.NOT_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                        }
                        //Arrays.asList(0, 1, 2, 3, 5)
                    } else {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.NOT_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                    }
                }
            }

        }
        return applicationInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvoicingApplicationInfoViewVO modify(InvoicingApplicationInfoDTO dto) {

        InvoicingApplicationInfoViewVO vo = new InvoicingApplicationInfoViewVO();

        InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(dto.getId(), true, Arrays.asList(UserRole.AM.toDbValue(), UserRole.CO_AM.toDbValue(), UserRole.RECRUITER.toDbValue(), UserRole.SOURCER.toDbValue(), UserRole.DM.toDbValue(), UserRole.AC.toDbValue(), UserRole.BD_OWNER.toDbValue(), UserRole.SALES_LEAD_OWNER.toDbValue()));

        if (applicationInfo.getStatus().equals(0) || !applicationInfo.getInvoicingStatus().equals(InvoicingStatus.REJECTED_APPROVAL)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION)) {
            if (null != dto.getCandidateInfoList()) {
                checkCandidateInfo(dto);
            }
        }

        List<InvoicingApplicationInfo> prepaymentInfoList = deleteInvoicingDerivativeInformation(applicationInfo.getId(), "modify", true);

        applicationInfo.setInvoicingStatus(InvoicingStatus.PENDING_APPROVAL);
        applicationInfo.setTaxPayerType(dto.getTaxPayerType());
        applicationInfo.setClientInvoicingId(dto.getClientInvoicingId());
        applicationInfo.setCompanyId(dto.getCompanyId());
        applicationInfo.setTenantId(SecurityUtils.getTenantId());
        applicationInfo.setClientInvoicingId(dto.getClientInvoicingId());
        applicationInfo.setInvoicingBody(dto.getInvoicingBody());
        applicationInfo.setInvoicingType(dto.getInvoicingType());
        applicationInfo.setInvoicingBusinessType(dto.getInvoicingBusinessType());
        applicationInfo.setInvoicingServerTaxId(dto.getInvoicingServerTaxId());
        applicationInfo.setInvoicingTax(dto.getInvoicingTax());
        applicationInfo.setInvoiceFormat(dto.getInvoicingFormat());
        applicationInfo.setConfirmationLetterUrl(dto.getConfirmationLetterUrl());
        applicationInfo.setInvoiceUrl(dto.getInvoiceUrl());
        applicationInfo.setInvoicingAmount(BigDecimal.valueOf(dto.getInvoicingAmount()));
        applicationInfo.setAmountDue(BigDecimal.valueOf(dto.getInvoicingAmount()));
        applicationInfo.setInvoiceTax(BigDecimal.valueOf(dto.getInvoiceTax()));
        applicationInfo.setTaxesNotIncluded(BigDecimal.valueOf(dto.getTaxesNotIncluded()));
        applicationInfo.setGpAmount(BigDecimal.valueOf(dto.getGpAmount()));
        applicationInfo.setUninvoicedAmount(BigDecimal.valueOf(dto.getUninvoicedAmount()));
        applicationInfo.setStatus(1);
        applicationInfo.setInvoicingApplicationType(dto.getInvoicingApplicationType().toDbValue());
        applicationInfo.setNote(dto.getNote());
        applicationInfo.setInvoiceNode(dto.getInvoiceNote());

        if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.VOID_INVOICING_APPLICATION)) {
            applicationInfo.setVoidInvoicing(1);
            applicationInfo.setInvalidInvoicingId(dto.getInvalidInvoicingId());
            applicationInfo.setInvoicingReason(dto.getInvoicingReason());

            Optional<InvoicingApplicationInfo> infoOtp = invoicingApplicationInfoRepository.findById(dto.getInvalidInvoicingId());
            if (infoOtp.isPresent()) {
                applicationInfo.setInvoicingApplicationType(infoOtp.get().getInvoicingApplicationType());
            }
        } else {
            applicationInfo.setVoidInvoicing(null);
        }
        if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION)) {
            //开票预付金金额=实际支付金额
            BigDecimal prepaymentAmountSum = dto.getCandidateInfoList().stream().filter(p -> p.getPrepaymentAmount() != null)
                    .map(p -> p.getPrepaymentAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (prepaymentAmountSum.compareTo(BigDecimal.ZERO) > 0) {
                if (prepaymentAmountSum.compareTo(BigDecimal.valueOf(dto.getGpAmount())) == 0) {
                    applicationInfo.setAmountDue(BigDecimal.ZERO);
                } else {
                    applicationInfo.setAmountDue(BigDecimal.valueOf(dto.getInvoicingAmount()).subtract(prepaymentAmountSum));
                }
            }
        } else if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION)) {
            applicationInfo.setAmountDue(BigDecimal.ZERO);
        }
        invoicingApplicationInfoRepository.saveAndFlush(applicationInfo);
        log.info("[invoicing modify]: save application info param:{}", JSONUtil.toJsonStr(applicationInfo));

        String note = SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "提交%s，等待财务进行审批。";

        //全开票
        if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION)) {
            String subTitle = String.format(note, "开票申请");
            if (null != dto.getCandidateInfoList()) {
                //保存候选人数据
                BigDecimal prepayment = assemblingCandidateDate(dto, applicationInfo.getId(), prepaymentInfoList, true);
                log.info("[invoicing modify]: finish save candidate info");

                if (!prepayment.equals(BigDecimal.ZERO)) {
                    subTitle = subTitle + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "使用预付金抵扣" + prepayment + "。";
                }
            }
            addLogInfo("提交开票申请", subTitle, applicationInfo.getId(), null, null, InvoicingLogStatus.APPLICATION, "modify", null, null, null);
        } else if (dto.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION)) {
            addLogInfo("提交预付金开票申请", String.format(note, "预付金开票申请"), applicationInfo.getId(), null, null, InvoicingLogStatus.APPLICATION, "modify", null, null, null);
        } else {
            if (null != dto.getCandidateInfoList()) {
                //checkCandidateInfo(dto);
                //保存候选人数据
                assemblingCandidateDate(dto, applicationInfo.getId(), prepaymentInfoList, false);
                log.info("[invoicing modify]: finish save candidate info");
            }
            Optional<InvoicingApplicationInfo> infoOtp = invoicingApplicationInfoRepository.findById(dto.getInvalidInvoicingId());
            if (infoOtp.isPresent()) {
                String codeNumber = infoOtp.get().getElectronicInvoiceNumber();
                String newStr = "被红冲数蓝字电票号码";
                if (infoOtp.get().getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE)) {
                    codeNumber = infoOtp.get().getCodeNumber();
                    newStr = "原始发票开票编码";
                }
                addLogInfo("提交废票申请", String.format(note, "废票申请"), dto.getId(), newStr, null, InvoicingLogStatus.APPLICATION, "modify", codeNumber, dto.getInvalidInvoicingId(), null);
            }
        }

        vo.setId(dto.getId());

        invoicingApplicationNotesAttachmentRepository.updateStatusByInvoicingId(applicationInfo.getId());
        //保存开票附件
        addInvoiceAttachment(dto, applicationInfo.getId());
        log.info("[invoicing modify]: finish save attachment info");

        return vo;
    }

    /**
     * 删除开票相关信息
     *
     * @param id
     * @param method
     * @param flag   true 修改开票信息
     */
    private List<InvoicingApplicationInfo> deleteInvoicingDerivativeInformation(Long id, String method, boolean flag) {

        List<InvoicingApplicationInfo> prepaymentInfoList = new ArrayList<>();

        //删除payment info
        List<InvoicingRecordPaymentInfo> recordPaymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdAndStatus(id, 1);
        List<Long> paymentIds = recordPaymentInfos.stream().filter(a -> a.getPrepaymentId() != null)
                .map(InvoicingRecordPaymentInfo::getId).collect(Collectors.toList());
        //还原预付金
        if (!paymentIds.isEmpty()) {
            //查询支付与预付金关系表
            List<PrepaymentInvoicingRelation> prepaymentInvoicingRelationList = prepaymentInvoicingRelationRepository.findByPaymentIdInAndRelationTypeAndStatus(paymentIds, InvoicingRelationType.USE_RELATION, 1);

            if (prepaymentInvoicingRelationList != null && !prepaymentInvoicingRelationList.isEmpty()) {
                List<Long> prepaymentIds = prepaymentInvoicingRelationList.stream()
                        .map(PrepaymentInvoicingRelation::getPrepaymentInvoicingId).collect(Collectors.toList());
                List<InvoicingApplicationInfo> prepaymentList = invoicingApplicationInfoRepository.findByIdIn(prepaymentIds);
                for (InvoicingApplicationInfo pre : prepaymentList) {
                    for (PrepaymentInvoicingRelation relation : prepaymentInvoicingRelationList) {
                        if (pre.getId().equals(relation.getPrepaymentInvoicingId())) {
                            pre.setAmountDue(pre.getAmountDue().add(relation.getInvoicingAmount()));
                        }
                    }
                }

                invoicingApplicationInfoRepository.saveAllAndFlush(prepaymentList);
                log.info("[invoicing {}]: update payment info by invoicing id:{},application info:{}", method, id, JSONUtil.toJsonStr(prepaymentList));
                prepaymentInfoList = prepaymentList;

                //删除关联数据
                prepaymentInvoicingRelationRepository.updateStatusByInvoicingIdAndPaymentId(id, paymentIds);
                log.info("[invoicing {}}]: delete payment relation info param:{}", method, id);
            }
        }
        invoicingRecordPaymentInfoRepository.updateStatusByInvoicingId(id);
        log.info("[invoicing {}]: delete payment info by invoicing id:{}", method, id);

        if (flag) {
            invoicingCandidateInfoRepository.updateStatusByInvoicingId(id);
            log.info("[invoicing {}]: delete candidate info by invoicing id:{}", method, id);

            invoicingCandidateDetailRepository.updateStatusByInvoicingId(id);
            log.info("[invoicing {}]: delete candidate detail by invoicing id:{}", method, id);
        } else {
            //更新预付金金额
            invoicingCandidateInfoRepository.updatePrepaymentByInvoicingId(id);
            log.info("[invoicing {}]: update candidate prepayment is 0 by invoicing id:{}", method, id);
        }

        return prepaymentInfoList;
    }

    @Override
    public InvoicingApplicationInfoViewVO view(Long id) {

        if (id == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Optional<InvoicingApplicationInfo> applicationInfoOpt = invoicingApplicationInfoRepository.findById(id);
        if (!applicationInfoOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        InvoicingApplicationInfo applicationInfo = applicationInfoOpt.get();

        //查询候选人
        List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdAndStatus(id, 1);
        List<InvoicingCandidateDetail> candidateDetailList = invoicingCandidateDetailRepository.findByInvoiceApplicationIdAndStatus(id, 1);
        Map<Long, List<InvoicingCandidateDetail>> listMap = candidateDetailList.stream().collect(Collectors.groupingBy(InvoicingCandidateDetail::getInvoiceCandidateId));

        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateChinaInvoicingDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if (!SecurityUtils.isAdmin()) {
//            List<Long> candidateList = candidateDetailList.stream().filter(v -> v.getUserRole() != 6).filter(v -> v.getUserRole() != 7).map(InvoicingCandidateDetail::getUserId).collect(Collectors.toList());
            List<Long> candidateList = candidateDetailList.stream().map(InvoicingCandidateDetail::getUserId).collect(Collectors.toList());
            if (teamDataPermission.getSelf()) {
                if (!applicationInfo.getPermissionUserId().equals(SecurityUtils.getUserId()) && !candidateList.contains(SecurityUtils.getUserId())) {
                    throw new NoPermissionException();
                }
            } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                List<UserLeaderVO> userList = invoicingNativeRepository.findUserIdByTeamId(teamDataPermission.getNestedTeamIds());
                if (!teamDataPermission.getNestedTeamIds().contains(applicationInfo.getPermissionTeamId())
                        && !candidateList.contains(SecurityUtils.getUserId())
                        && !userList.contains(SecurityUtils.getUserId())) {
                    throw new NoPermissionException();
                }
            }
        }

        if (!SecurityUtils.getTenantId().equals(applicationInfo.getTenantId())) {
            throw new NoPermissionException();
        }

        List<Long> userIdList = candidateDetailList.stream().map(InvoicingCandidateDetail::getUserId).collect(Collectors.toList());
        List<UserLeaderVO> userLeaderVOS = invoicingNativeRepository.searchLeaderByUserId(userIdList);
        Map<BigInteger, List<UserLeaderVO>> userLeaderMap = userLeaderVOS.stream().collect(Collectors.groupingBy(UserLeaderVO::getUserId));

        List<UserLeaderVO> teamCodeList = invoicingNativeRepository.searchTeamCodeByUserId(userIdList);
        Map<BigInteger, String> teamCodeMap = teamCodeList.stream().collect(Collectors.toMap(UserLeaderVO::getUserId, UserLeaderVO::getTeamCode));

        List<PermissionTeamLeaderProfitVO> profitOwnerProperties = invoicingNativeRepository.searchPermissionTeamLeaderProfit();

        List<Long> jobIds = candidateInfoList.stream().map(InvoicingCandidateInfo::getJobId).collect(Collectors.toList());
        List<JobSalesLeadVO> jobSalesLeadVOS = invoicingNativeRepository.searchJobId(jobIds);

        List<InvoiceContractVO> contractVOS = null;
        if (null != candidateInfoList && !candidateInfoList.isEmpty()) {
            //contractList
            String url = getCrmUrl(candidateInfoList.get(0).getCompanyId());
            String authorizationHeader = getAuthorizationHeader();
            cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createGet(url)
                    .header("Authorization", authorizationHeader)
                    .execute();

            if (response.isOk()) {
                contractVOS = JSONUtil.toList(JSONUtil.parseArray(response.body()), InvoiceContractVO.class);
            }
        }

        InvoicingApplicationInfoViewVO vo = new InvoicingApplicationInfoViewVO();

        //查询支付信息id
        Long invoicingId = applicationInfo.getId();
        if (applicationInfo.getVoidInvoicing() != null && applicationInfo.getInvalidInvoicingId() != null) {
            Optional<InvoicingApplicationInfo> oldInfoOpt = invoicingApplicationInfoRepository.findById(applicationInfo.getInvalidInvoicingId());
            if (oldInfoOpt.isPresent()) {
                vo.setOldConfirmationLetterUrl(oldInfoOpt.get().getConfirmationLetterUrl());
            }
            invoicingId = applicationInfo.getInvalidInvoicingId();
        }

        //payment
        List<InvoicingRecordPaymentInfoVO> recordPaymentInfoVOList = new ArrayList<>();
        List<InvoicingRecordPaymentInfo> recordPaymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdAndStatus(invoicingId, 1);
        for (InvoicingRecordPaymentInfo info : recordPaymentInfos) {
            InvoicingRecordPaymentInfoVO bean = new InvoicingRecordPaymentInfoVO();
            bean.setId(info.getId());
            bean.setNote(info.getNote());
            bean.setInvoicingId(info.getInvoicingId());
            bean.setPaymentDate(info.getPaymentDate());
            bean.setCandidateJson(info.getCandidateJson());
            bean.setPaymentMethod(info.getPaymentMethod());
            bean.setPaymentAmount(info.getPaymentAmount());
            recordPaymentInfoVOList.add(bean);
        }

        vo.setRecordPaymentInfoList(recordPaymentInfoVOList);

        List<InvoicingCandidateInfoVO> candidateInfoVOList = new ArrayList<>();
        List<InvoiceContractVO> finalContractVOS = contractVOS;
        candidateInfoList.forEach(info -> {
            candidateInfoVOList.add(formToVO(info, listMap, recordPaymentInfos,
                    userLeaderMap, teamCodeMap, profitOwnerProperties,
                    finalContractVOS, jobSalesLeadVOS));
        });

        if (!candidateInfoVOList.isEmpty()) {
            candidateInfoVOList.forEach(item -> {
                List<StartSearchInvoicingDTO> searchInvoicingDTOS = startService.searchInvoicingStarts(item.getTalentId() + "");
                if (!searchInvoicingDTOS.isEmpty()) {
                    searchInvoicingDTOS.forEach(start -> {
                        if (start.getStartId().equals(item.getStartId()) && start.getTalentId().equals(item.getTalentId()) && start.getJobId().equals(item.getJobId())) {
                            item.setStartInvoicing(start);
                        }
                    });
                }
            });
        }

        vo.setInvalidInvoicingId(applicationInfo.getInvalidInvoicingId());
        vo.setTaxPayerType(applicationInfo.getTaxPayerType());
        vo.setInvoicingReason(applicationInfo.getInvoicingReason());
        vo.setId(applicationInfo.getId());
        vo.setInvoicingApplicationType(applicationInfo.getInvoicingApplicationType().toString());
        vo.setClientInvoicingId(applicationInfo.getClientInvoicingId());
        vo.setInvalidInvoicingId(applicationInfo.getInvalidInvoicingId());
        vo.setInvoicingReason(applicationInfo.getInvoicingReason());
        vo.setCandidateInfoList(candidateInfoVOList);
        vo.setCodeNumber(applicationInfo.getCodeNumber());
        vo.setCreatedDate(applicationInfo.getCreatedDate());
        Company company = companyService.getCompany(applicationInfo.getCompanyId());
        vo.setCompanyName(company.getFullBusinessName());
        vo.setCompanyId(BigInteger.valueOf(applicationInfo.getCompanyId()));
        vo.setGpAmount(applicationInfo.getGpAmount());
        vo.setInvoicingAmount(applicationInfo.getInvoicingAmount());
        vo.setInvoiceTax(applicationInfo.getInvoiceTax());
        vo.setTaxesNotIncluded(applicationInfo.getTaxesNotIncluded());
        vo.setInvoicingTax(applicationInfo.getInvoicingTax());
        vo.setUninvoicedAmount(applicationInfo.getUninvoicedAmount());
        vo.setInvoicingStatus(applicationInfo.getInvoicingStatus());
        vo.setElecInvoiceNumber(applicationInfo.getElectronicInvoiceNumber());
        vo.setInvoicingDate(applicationInfo.getInvoicingDate());
        vo.setDueWithinDays(applicationInfo.getDueWithinDays());
        vo.setPaymentDueDate(applicationInfo.getPaymentDueDate());
        vo.setInvoicingBody(applicationInfo.getInvoicingBody().name());
        vo.setInvoicingBusinessType(applicationInfo.getInvoicingBusinessType());
        vo.setInvoicingType(applicationInfo.getInvoicingType());
        vo.setInvoicingFormat(applicationInfo.getInvoiceFormat());
        vo.setConfirmationLetterUrl(applicationInfo.getConfirmationLetterUrl());
        vo.setInvoiceUrl(applicationInfo.getInvoiceUrl());
        vo.setNote(applicationInfo.getNote());
        vo.setInvoiceNote(applicationInfo.getInvoiceNode());
        vo.setVoidInvoicing(applicationInfo.getVoidInvoicing());
        vo.setCreateUserId(applicationInfo.getPermissionUserId());
        //是不是被开具废票
        List<InvoicingApplicationInfo> infos = invoicingApplicationInfoRepository.findByInvalidInvoicingIdAndStatus(applicationInfo.getId(), 1);
        if (!infos.isEmpty()) {
            vo.setVoidInvoicingApplication("1");
        }

        if (applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue())) {
            List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdAndStatus(applicationInfo.getId(), 1);
            if (paymentInfos.isEmpty()) {
                vo.setAmountDue(BigDecimal.ZERO);
            } else {
                BigDecimal totalBalance = paymentInfos.stream()
                        .map(p -> p.getPaymentAmount())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setAmountDue(applicationInfo.getInvoicingAmount().subtract(totalBalance));
            }
        } else {
            vo.setAmountDue(applicationInfo.getAmountDue());
        }

        if (null != applicationInfo.getInvoicingServerTaxId()) {
            //服务类型
            Optional<EnumInvoicingServiceTax> serviceTax = enumInvoicingServiceTaxRepository.findById(applicationInfo.getInvoicingServerTaxId());
            if (serviceTax.isPresent()) {
                vo.setInvoicingTaxName(serviceTax.get().getServiceName());
                vo.setInvoicingServerTaxId(BigInteger.valueOf(applicationInfo.getInvoicingServerTaxId()));
            }
        }

        //客户信息
        CompanyClientInvoicingInfoVO clientInvoicingInfoVO = companyService.getInvoicingClientInfoById(applicationInfo.getClientInvoicingId());
        vo.setClientName(clientInvoicingInfoVO.getClientName());
        vo.setSocialCreditCode(clientInvoicingInfoVO.getSocialCreditCode());
        vo.setBankName(clientInvoicingInfoVO.getBankName());
        vo.setBankAccount(clientInvoicingInfoVO.getBankAccount());
        vo.setInvoicingAddress(clientInvoicingInfoVO.getInvoicingAddress());
        vo.setPhone(clientInvoicingInfoVO.getPhone());

        //log
        List<InvoicingRecordLog> recordLogs = invoicingRecordLogRepository.findByInvoicingId(id);
        List<InvoicingRecordLogVO> recordLogVOList = recordLogs.stream().map(InvoicingRecordLogVO::formToVO).sorted(Comparator.comparing(InvoicingRecordLogVO::getOperatedDate).reversed()).collect(Collectors.toList());
        vo.setRecordLogList(recordLogVOList);

        //attachmentList
        List<InvoicingApplicationNotesAttachment> attachments = invoicingApplicationNotesAttachmentRepository.findByInvoiceApplicationIdAndStatus(applicationInfo.getId(), 1);
        List<InvoicingNoteDTO> attachmentVOList = attachments.stream()
                .map(v -> InvoicingNoteDTO.fromToVo(v.getAttachmentUrl())).collect(Collectors.toList());
        vo.setInvoicingUrlList(attachmentVOList);


        return vo;
    }

    private final String CRM_ACCOUNT_COMPANY_ID = "/account/api/v1/contracts/companyId/businessId/";

    private String getCrmUrl(Long id) {
        return crmUrl + CRM_ACCOUNT_COMPANY_ID + id;
        //return "http://localhost:9028" + CRM_ACCOUNT_COMPANY_ID + id;
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    public InvoicingCandidateInfoVO formToVO(InvoicingCandidateInfo info, Map<Long, List<InvoicingCandidateDetail>> listMap,
                                             List<InvoicingRecordPaymentInfo> recordPaymentInfos,
                                             Map<BigInteger, List<UserLeaderVO>> userLeaderMap,
                                             Map<BigInteger, String> teamCodeMap,
                                             List<PermissionTeamLeaderProfitVO> profitOwnerProperties,
                                             List<InvoiceContractVO> contractVOS,
                                             List<JobSalesLeadVO> jobSalesLeadVOS) {
        InvoicingCandidateInfoVO vo = new InvoicingCandidateInfoVO();

        vo.setId(info.getId());
        vo.setChinaOrder(info.getInvoiceArea() == 1 ? true : false);
        vo.setJobId(info.getJobId());
        vo.setJobName(info.getJobName());
        vo.setTalentId(info.getTalentId());
        vo.setStartId(info.getStartId());
        vo.setTalentName(info.getTalentName());
        vo.setGpAmount(info.getGpAmount().toString());
        vo.setAmountReceived(info.getAmountReceived().toString());
        vo.setAmountReceivedTax(info.getAmountReceivedTax() != null ? info.getAmountReceivedTax().toString() : "");
        vo.setAmountPercentage(info.getAmountPercentage() != null ? info.getAmountPercentage().toString() : "");
        vo.setPrepaymentAmount(info.getAmountPrepayment() != null ? info.getAmountPrepayment().toString() : "");
        vo.setContractStatus(true);
        if (null != info.getExpectedInvoicingAmount()) {
            vo.setExpectedInvoicingAmount(info.getExpectedInvoicingAmount().toString());
        }
        if (null != info.getExpectedInvoicingDate()) {
            vo.setExpectedInvoicingDate(info.getExpectedInvoicingDate());
        }
        if (null != info.getCompanyId()) {
            vo.setCompanyId(info.getCompanyId());
        }

        BigDecimal payAmount = BigDecimal.ZERO;
        for (InvoicingRecordPaymentInfo pay : recordPaymentInfos) {
            if (StringUtils.isNotBlank(pay.getCandidateJson())) {
                JSONArray objArry = JSONUtil.parseArray(pay.getCandidateJson());
                for (int i = 0; i < objArry.size(); i++) {
                    JSONObject obj = objArry.getJSONObject(i);
                    Long talentId = obj.getLong("talentId");
                    if (obj.containsKey("jobId")) {
                        Long jobId = obj.getLong("jobId");
                        if (talentId.equals(vo.getTalentId()) && jobId.equals(vo.getJobId())) {
                            String paymentAmount = obj.getStr("paymentAmount");
                            if (StringUtils.isNotBlank(paymentAmount)) {
                                payAmount = payAmount.add(new BigDecimal(paymentAmount));
                            }
                        }
                    } else {
                        if (talentId.equals(vo.getTalentId())) {
                            String paymentAmount = obj.getStr("paymentAmount");
                            if (StringUtils.isNotBlank(paymentAmount)) {
                                payAmount = payAmount.add(new BigDecimal(paymentAmount));
                            }
                        }
                    }
                }
            }
        }
        vo.setPaymentAmount(payAmount.toString());

        if (listMap.containsKey(info.getId())) {
            List<InvoicingCandidateDetail> detailInfo = listMap.get(info.getId());
            Map<Integer, List<InvoicingCandidateDetail>> mapDetails = detailInfo.stream().collect(Collectors.groupingBy(InvoicingCandidateDetail::getUserRole));
            JSONObject teamLeaderNames = new JSONObject();
            JSONObject profitCenterManager = new JSONObject();
            for (Map.Entry<Integer, List<InvoicingCandidateDetail>> entry : mapDetails.entrySet()) {
                Pair<String, String> v = setDetail(entry.getValue());
                Pair<JSONObject, JSONObject> teamLeaderAndProfitOwner = setTeamLeader(entry.getValue(), userLeaderMap, teamCodeMap, profitOwnerProperties);
                if (entry.getKey() == 1) {
                    vo.setAccountManager(v.getKey());
                    vo.setAccountManagerPercentage(v.getValue());
                    teamLeaderNames.put("accountManager", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("accountManager", teamLeaderAndProfitOwner.getValue());
                } else if (entry.getKey() == 2) {
                    vo.setRecruiter(v.getKey());
                    vo.setRecruiterPercentage(v.getValue());
                    teamLeaderNames.put("recruiter", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("recruiter", teamLeaderAndProfitOwner.getValue());
                } else if (entry.getKey() == 3) {
                    vo.setDeliveryManager(v.getKey());
                    vo.setDeliveryManagerPercentage(v.getValue());
                    teamLeaderNames.put("deliveryManager", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("deliveryManager", teamLeaderAndProfitOwner.getValue());
                } else if (entry.getKey() == 4) {
                    vo.setSourcer(v.getKey());
                    vo.setSourcerPercentage(v.getValue());
                    teamLeaderNames.put("sourcer", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("sourcer", teamLeaderAndProfitOwner.getValue());
                } else if (entry.getKey() == 5) {
                    vo.setOwner(v.getKey());
                    vo.setOwnerPercentage(v.getValue());
                    teamLeaderNames.put("owner", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("owner", teamLeaderAndProfitOwner.getValue());
                } else if (entry.getKey() == 6) {
                    vo.setSalesLeadManager(v.getKey());
                    vo.setSalesLeadManagerPercentage(v.getValue());
                    teamLeaderNames.put("salesLeadManager", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("salesLeadManager", teamLeaderAndProfitOwner.getValue());
                } else if (entry.getKey() == 7) {
                    vo.setBdManager(v.getKey());
                    vo.setBdManagerPercentage(v.getValue());
                    teamLeaderNames.put("bdManager", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("bdManager", teamLeaderAndProfitOwner.getValue());
                } else if (entry.getKey() == 8) {
                    vo.setCooperateAccountManager(v.getKey());
                    vo.setCooperateAccountManagerPercentage(v.getValue());
                    teamLeaderNames.put("cooperateAccountManager", teamLeaderAndProfitOwner.getKey());
                    profitCenterManager.put("cooperateAccountManager", teamLeaderAndProfitOwner.getValue());
                    List<UserCountryVO> voList = new ArrayList<>();
                    entry.getValue().forEach(x -> {
                        if (null != x.getCountry()) {
                            UserCountryVO countryVO = new UserCountryVO();
                            countryVO.setUserName(x.getUserName());
                            countryVO.setCountryId(x.getCountry());
                            voList.add(countryVO);
                        }
                    });
                    if (!voList.isEmpty()) {
                        vo.setCoAmList(voList);
                    }
                }
            }

            vo.setTeamLeaderName(teamLeaderNames);
            vo.setProfitCenterManager(profitCenterManager);
        }

        if (null != contractVOS) {
            if (null != jobSalesLeadVOS && !jobSalesLeadVOS.isEmpty()) {
                Map<Long, Long> jobSalesLeadMap = jobSalesLeadVOS.stream().collect(Collectors.toMap(z -> z.getId().longValue(), x -> x.getSalesLeadId().longValue()));
                Long businessId = jobSalesLeadMap.get(info.getJobId());

                List<InvoiceContractVO> contractVOList = new ArrayList<>();
                if (null != businessId) {
                    contractVOS.forEach(v -> {
                        if (v.getAccountBusinessId().equals(businessId)) {
                            contractVOList.add(v);
                        }
                    });
                }
                List<InvoiceContractVO> voList = contractVOList.stream().sorted(Comparator.comparing(InvoiceContractVO::getId).reversed()).collect(Collectors.toList());
                vo.setContractList(voList);
            }
        }

        return vo;
    }

    private Pair<JSONObject, JSONObject> setTeamLeader(List<InvoicingCandidateDetail> values,
                                                       Map<BigInteger, List<UserLeaderVO>> userLeaderMap,
                                                       Map<BigInteger, String> teamCodeMap,
                                                       List<PermissionTeamLeaderProfitVO> profitOwnerProperties) {
        JSONObject teamLeaderJson = new JSONObject();
        JSONObject profitOwnerJson = new JSONObject();
        List<InvoicingCandidateDetail> detailList = values;
        for (InvoicingCandidateDetail d : detailList) {
            if (null != userLeaderMap && !userLeaderMap.isEmpty()) {
                if (userLeaderMap.containsKey(BigInteger.valueOf(d.getUserId()))) {
                    JSONObject leader = new JSONObject();
                    List<UserLeaderVO> leaderVOS = userLeaderMap.get(BigInteger.valueOf(d.getUserId()));
                    List<String> leaderNames = leaderVOS.stream().map(UserLeaderVO::getLeaderUserName).collect(Collectors.toList());
                    leader.put("leader", StringUtils.join(leaderNames, ","));
                    teamLeaderJson.put(d.getUserName(), leader);
                }
            }

            if (null != teamCodeMap && !teamCodeMap.isEmpty() && null != profitOwnerProperties) {
                if (teamCodeMap.containsKey(BigInteger.valueOf(d.getUserId()))) {
                    String teamCode = teamCodeMap.get(BigInteger.valueOf(d.getUserId()));
                    for (PermissionTeamLeaderProfitVO profitVO : profitOwnerProperties) {
                        if (teamCode.startsWith(profitVO.getCode())) {
                            JSONObject profitOwner = new JSONObject();
                            profitOwner.put("profitOwner", profitVO.getUserName());
                            profitOwnerJson.put(d.getUserName(), profitOwner);
                            break;
                        }
                    }
                }
            }
        }

        return new Pair<>(teamLeaderJson, profitOwnerJson);
    }


    private Pair<String, String> setDetail(List<InvoicingCandidateDetail> values) {
        StringBuilder name = new StringBuilder();
        StringBuilder percentage = new StringBuilder();
        List<InvoicingCandidateDetail> detailList = values;
        for (InvoicingCandidateDetail d : detailList) {
            if (name.isEmpty()) {
                name.append(d.getUserName());
                percentage.append(d.getPercentage());
            } else {
                name.append(",").append(d.getUserName());
                percentage.append(",").append(d.getPercentage());
            }
        }
        return new Pair<>(name.toString(), percentage.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void voidInvoicing(Long id) {

        InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(id, true, null);

        if (applicationInfo.getStatus().equals(0) || !InvoicingUtils.cancelInvoicingStatus.contains(applicationInfo.getInvoicingStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        deleteInvoicingDerivativeInformation(applicationInfo.getId(), "voidInvoicing", false);

        applicationInfo.setStatus(0);
        invoicingApplicationInfoRepository.save(applicationInfo);
        log.info("[invoicing voidInvoicing]: update application info param:{}", JSONUtil.toJsonStr(applicationInfo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void financialApproval(InvoicingFinancialApprovalDTO dto) {

        InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(dto.getInvoicingId(), false, null);

        if (applicationInfo.getStatus().equals(0) || !applicationInfo.getInvoicingStatus().equals(InvoicingStatus.PENDING_APPROVAL)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Boolean completeSystemCalendar = false;
        applicationInfo.setInvoicingStatus(dto.getInvoicingStatus());

        String note = "财务" + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "%s。";
        if (dto.getInvoicingStatus().equals(InvoicingStatus.PENDING_INVOICING)) {

            //set approval note
            applicationInfo.setApprovalNote(dto.getNote());

            //抵扣发票 使用预付金扣除金额
            if (applicationInfo.getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE)
                    && applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION.toDbValue())
                    && applicationInfo.getVoidInvoicing() == null) {

                applicationInfo.setInvoicingStatus(InvoicingStatus.UNCOLLECTED);

                //预付金类型增加回款信息
                final String title = "回款-预付金";
                final String subTitle = DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + " 创建预付金回款记录";
                List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findAllByInvoicingId(Arrays.asList(applicationInfo.getId()));
                if (!paymentInfos.isEmpty()) {
                    BigDecimal amountDue = applicationInfo.getInvoicingAmount().subtract(paymentInfos.get(0).getPaymentAmount());
                    //插入日志
                    addLogInfo(title, subTitle, applicationInfo.getId(), null, getLogJson(amountDue.toString(), paymentInfos.get(0).getPaymentAmount().toString(), DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS), InvoicingPaymentMethodType.PREPAYMENT), InvoicingLogStatus.PAYMENT, "financialInvoicingRecord", null, null, null);
                    if (applicationInfo.getAmountDue().compareTo(new BigDecimal("0.00")) == 0) {
                        applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                        completeSystemCalendar = true;
                    } else {
                        applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                    }
                }

                //不是全额回款
                if (!applicationInfo.getInvoicingStatus().equals(InvoicingStatus.FULLY_COLLECTED)) {
                    List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdAndStatus(applicationInfo.getId(), 1);
                    List<InvoicingCandidateInfoDTO> candidateInfoDTOS = new ArrayList<>();
                    candidateInfoList.forEach(v -> {
                        InvoicingCandidateInfoDTO bean = new InvoicingCandidateInfoDTO();
                        bean.setGpAmount(v.getGpAmount());
                        bean.setTalentId(v.getTalentId());
                        bean.setTalentName(v.getTalentName());
                        Double tax = 1 + applicationInfo.getInvoicingTax() / 100;
                        BigDecimal invoicingAmount = v.getAmountReceived().multiply(BigDecimal.valueOf(tax)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (v.getAmountPrepayment() != null) {
                            bean.setPaymentAmount(invoicingAmount.subtract(v.getAmountPrepayment()));
                        } else {
                            bean.setPaymentAmount(invoicingAmount);
                        }
                        candidateInfoDTOS.add(bean);
                    });
                    BigDecimal payment = savePrepaymentInfo(applicationInfo.getCompanyId(), applicationInfo.getId(), applicationInfo.getAmountDue(),
                            applicationInfo.getAmountDue().doubleValue(), candidateInfoDTOS, new ArrayList<>(),
                            applicationInfo.getInvoicingBusinessType(), true);

                    if (payment != null) {
                        if (payment.compareTo(new BigDecimal("0.00")) == 0) {
                            applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                            applicationInfo.setAmountDue(BigDecimal.ZERO);
                            completeSystemCalendar = true;
                        } else {
                            applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                            applicationInfo.setAmountDue(payment);
                        }
                    }
                }
            } else if (applicationInfo.getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE)
                    && applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION.toDbValue())
                    && applicationInfo.getVoidInvoicing() != null) {
                //废票 抵扣逻辑
                InvoicingFinancialRecordDTO recordDTO = new InvoicingFinancialRecordDTO();
                recordDTO.setElecInvoiceNumber(applicationInfo.getCodeNumber());
                Long voidInvoicingId = voidRecord(applicationInfo, recordDTO, false);
                SecurityContext context = SecurityContextHolder.getContext();
                CompletableFuture.runAsync(() -> {
                    SecurityContextHolder.setContext(context);
                    notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(voidInvoicingId, InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
                });
            }
            addLogInfo("通过审批", String.format(note, "审批通过开票申请"), dto.getInvoicingId(), dto.getNote(), null, InvoicingLogStatus.APPROVAL, "financialApproval", null, null, null);
        } else {
            addLogInfo("审批驳回", String.format(note, "审批驳回"), dto.getInvoicingId(), dto.getNote(), null, InvoicingLogStatus.APPROVAL, "financialApproval", null, null, null);
        }
        invoicingApplicationInfoRepository.save(applicationInfo);
        log.info("[invoicing financialApproval]: update application info param:{}", JSONUtil.toJsonStr(applicationInfo));
        if (completeSystemCalendar) {
            SecurityContext context = SecurityContextHolder.getContext();
            Long invoicingId = dto.getInvoicingId();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(invoicingId, InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void financialApprovalList(InvoicingFinancialApprovalDTO dto) {

        if (dto.getInvoicingIdList() == null || dto.getInvoicingIdList().isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        List<InvoicingApplicationInfo> applicationInfos = invoicingApplicationInfoRepository.findByIdIn(dto.getInvoicingIdList());
        if (applicationInfos.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Set<InvoicingStatus> statusSet = applicationInfos.stream().map(InvoicingApplicationInfo::getInvoicingStatus).collect(Collectors.toSet());
        if (statusSet.size() == 1 && !statusSet.contains(InvoicingStatus.PENDING_APPROVAL)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        if (statusSet.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        for (InvoicingApplicationInfo info : applicationInfos) {
            //发票类型是 抵扣发票 状态直接是待回款
            if (info.getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE) && dto.getInvoicingStatus().equals(InvoicingStatus.PENDING_INVOICING)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_INVOICINGTYPE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            } else {
                info.setInvoicingStatus(dto.getInvoicingStatus());
            }
        }

        invoicingApplicationInfoRepository.saveAll(applicationInfos);
        log.info("[invoicing financialApprovalList]: update application info param:{}", JSONUtil.toJsonStr(applicationInfos));

        List<InvoicingRecordLog> logList = new ArrayList<>();
        String note = "财务" + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "%s。";
        for (InvoicingApplicationInfo info : applicationInfos) {
            if (dto.getInvoicingStatus().equals(InvoicingStatus.PENDING_INVOICING)) {
                logList.add(addLogInfoOperate("通过审批", String.format(note, "审批通过开票申请"), info.getId(), null, null, InvoicingLogStatus.APPROVAL, null, null, null));
            } else {
                logList.add(addLogInfoOperate("审批驳回", String.format(note, "审批驳回"), info.getId(), null, null, InvoicingLogStatus.APPROVAL, null, null, null));
            }
        }
        if (!logList.isEmpty()) {
            invoicingRecordLogRepository.saveAll(logList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void financialInvoicingRecord(InvoicingFinancialRecordDTO dto) {

        InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(dto.getInvoicingId(), false, null);

        if (applicationInfo.getStatus().equals(0) || !InvoicingUtils.recordInvoicingStatus.contains(applicationInfo.getInvoicingStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        InvoicingApplicationInfo existElec = invoicingApplicationInfoRepository.findByElectronicInvoiceNumberAndStatus(dto.getElecInvoiceNumber(), 1);
        if (null != existElec) {
            if (!existElec.getId().equals(applicationInfo.getId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_ELEC_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
        }

        //如果是废票 异常
        if (applicationInfo.getVoidInvoicing() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        ZonedDateTime zonedDateTime = dto.getInvoicingDate().atZone(ZoneId.of("Asia/Shanghai"));

        applicationInfo.setInvoicingDate(zonedDateTime.toInstant());
        applicationInfo.setElectronicInvoiceNumber(dto.getElecInvoiceNumber());
        applicationInfo.setDueWithinDays(dto.getDueWithinDays());
        if (!dto.getInvoiceTax().equals(BigDecimal.ZERO)) {
            applicationInfo.setInvoiceTax(dto.getInvoiceTax());
            applicationInfo.setTaxesNotIncluded(applicationInfo.getInvoicingAmount().subtract(dto.getInvoiceTax()));
        }
        Duration addDays = Duration.ofDays(dto.getDueWithinDays());
        applicationInfo.setPaymentDueDate(zonedDateTime.toInstant().plus(addDays));
        applicationInfo.setInvoiceNode(dto.getNote());
        applicationInfo.setInvoiceUrl(dto.getInvoiceUrl());
        if (applicationInfo.getInvoicingStatus().equals(InvoicingStatus.PENDING_INVOICING)) {
            applicationInfo.setInvoicingStatus(InvoicingStatus.UNCOLLECTED);
        }

        //处理日志
        List<InvoicingRecordLog> logList = invoicingRecordLogRepository.findByVoidReferenceIdAndInvoicingStatus(applicationInfo.getId(), InvoicingLogStatus.INVOICING);

        if (logList.isEmpty()) {
            String note = "财务" + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "%s。";
            addLogInfo("开票", String.format(note, "创建开票信息"), dto.getInvoicingId(), dto.getNote(),
                    getLogJsonByInvoicing(dto.getElecInvoiceNumber(), dto.getDueWithinDays().toString(),
                            zonedDateTime.toInstant(), applicationInfo.getPaymentDueDate()),
                    InvoicingLogStatus.INVOICING, "financialInvoicingRecord", null, null, applicationInfo.getId());
        } else {
            InvoicingRecordLog recordLog = logList.get(0);
            String subTitleStr = "财务" + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "编辑开票信息。" + ";" + recordLog.getSubTitle();
            recordLog.setSubTitle(subTitleStr);
            String json = getLogJsonByInvoicing(dto.getElecInvoiceNumber(), dto.getDueWithinDays().toString(),
                    zonedDateTime.toInstant(), applicationInfo.getPaymentDueDate());
            recordLog.setNoteJson(json);
            recordLog.setNote(dto.getNote());
            invoicingRecordLogRepository.save(recordLog);

            invoicingRecordLogRepository.updateElectronicInvoiceNumberById(applicationInfo.getId(), dto.getElecInvoiceNumber());
        }

        //修改状态
        if (!applicationInfo.getInvoicingStatus().equals(InvoicingStatus.VOIDED)
                && !applicationInfo.getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE)
                && logList.isEmpty()) {

            //预付金类型增加回款信息
            final String title = "回款-预付金";
            final String subTitle = DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + " 创建预付金回款记录";
            List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findAllByInvoicingId(Arrays.asList(applicationInfo.getId()));
            if (!paymentInfos.isEmpty()) {
                BigDecimal amountDue = applicationInfo.getInvoicingAmount().subtract(paymentInfos.get(0).getPaymentAmount());
                //插入日志
                addLogInfo(title, subTitle, applicationInfo.getId(), null, getLogJson(amountDue.toString(), paymentInfos.get(0).getPaymentAmount().toString(), DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS), InvoicingPaymentMethodType.PREPAYMENT), InvoicingLogStatus.PAYMENT, "financialInvoicingRecord", null, null, null);
                if (applicationInfo.getAmountDue().equals(new BigDecimal("0.00"))) {
                    applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                } else {
                    applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                }
            }
        }
        invoicingApplicationInfoRepository.save(applicationInfo);
        log.info("[invoicing financialInvoicingRecord]: update application info param:{}", JSONUtil.toJsonStr(applicationInfo));
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            Instant paymentDueDate = applicationInfo.getPaymentDueDate();
            if (paymentDueDate.isAfter(Instant.now())) {
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(dto.getInvoicingId(), InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            }
        });



        if (applicationInfo.getInvoicingApplicationType() == 1) { //全职开票申请才发送lark
            CompletableFuture.runAsync(() -> {
                try {
                    SecurityContextHolder.setContext(context);
                    Company company = companyService.getCompany(applicationInfo.getCompanyId());
                    List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdAndStatus(applicationInfo.getId(), 1);
                    String candidateName = candidateInfoList.stream()
                            .map(InvoicingCandidateInfo::getTalentName)
                            .collect(Collectors.joining(","));


                    String url = applicationProperties.getDefaultInvoiceUrl() + "/" + applicationInfo.getId();
                    if (StringUtils.isNotBlank(applicationInfo.getInvoiceUrl()) && JSONUtil.isJson(applicationInfo.getInvoiceUrl())) {
                        JSONObject invoiceUrlJson = JSONUtil.parseObj(applicationInfo.getInvoiceUrl());
                        String uuid = invoiceUrlJson.getStr("uuid");
                        String uploadType = invoiceUrlJson.getStr("uploadType");
                        url = applicationProperties.getViewInvoiceUrl() + "?uuid=" + uuid + "&type=" + uploadType;
                    }

                    String userEmail = userService.getUserEmail(applicationInfo.getPermissionUserId());

                    sendLarkMsgViaAnycross(company.getFullBusinessName(), candidateName, applicationInfo.getCodeNumber(), userEmail, url, applicationProperties.getLarkServiceCallbackUrl());
                } catch (Exception e) {
                    log.error("Failed to send Lark message", e);
                }
            });
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void financialInvoicingVoidRecord(InvoicingFinancialRecordDTO dto) {

        InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(dto.getInvoicingId(), false, null);

        if (applicationInfo.getStatus().equals(0) || !InvoicingUtils.voidRecordInvoicingStatus.contains(applicationInfo.getInvoicingStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        //如果不是废票 异常
        if (applicationInfo.getVoidInvoicing() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        InvoicingApplicationInfo existElec = invoicingApplicationInfoRepository.findByElectronicInvoiceNumberAndStatus(dto.getElecInvoiceNumber(), 1);
        if (null != existElec) {
            if (!existElec.getId().equals(applicationInfo.getId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_ELEC_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
        }

        voidRecord(applicationInfo, dto, true);

        //更新预付金金额
        invoicingCandidateInfoRepository.updatePrepaymentByInvoicingId(applicationInfo.getId());
        log.info("[invoicing financialInvoicingVoidRecord]: update candidate prepayment is 0 application id param:{}", applicationInfo.getId());

        Long invalidInvoicingId = applicationInfo.getInvalidInvoicingId();
        SecurityContext context = SecurityContextHolder.getContext();

        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(dto.getInvoicingId(), InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
        });
        if (invalidInvoicingId != null) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(invalidInvoicingId, InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            });
        }

    }

    /**
     * @param applicationInfo
     * @param dto
     * @param flag            正常废票
     */
    private Long voidRecord(InvoicingApplicationInfo applicationInfo, InvoicingFinancialRecordDTO dto, boolean flag) {
        String note = "财务" + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "%s。";


        //查询原始数据
        Optional<InvoicingApplicationInfo> oldInvoicingInfoOpt = invoicingApplicationInfoRepository.findById(applicationInfo.getInvalidInvoicingId());
        if (!oldInvoicingInfoOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        InvoicingApplicationInfo oldInvoicingInfo = oldInvoicingInfoOpt.get();
        oldInvoicingInfo.setInvoicingStatus(InvoicingStatus.VOIDED);
        oldInvoicingInfo.setAmountDue(oldInvoicingInfo.getInvoicingAmount());
        deleteInvoicingDerivativeInformation(oldInvoicingInfo.getId(), "financialInvoicingVoidRecord", false);
        invoicingApplicationInfoRepository.save(oldInvoicingInfo);

        //删除旧日志
        invoicingRecordLogRepository.deleteByInvoicingLogStatusAndVoidReferenceId(InvoicingLogStatus.PAYMENT.toDbValue(), oldInvoicingInfo.getId());

        addLogInfo("取消回款", String.format(note, "取消回款记录"), oldInvoicingInfo.getId(), "发票作废，自动取消回款",
                null,
                InvoicingLogStatus.PAYMENT, "financialInvoicingVoidRecord", null, null, oldInvoicingInfo.getId());

        if (dto.getInvoicingDate() != null) {
            ZonedDateTime zonedDateTime = dto.getInvoicingDate().atZone(ZoneId.of("Asia/Shanghai"));
            applicationInfo.setInvoicingDate(zonedDateTime.toInstant());
            applicationInfo.setElectronicInvoiceNumber(dto.getElecInvoiceNumber());
            applicationInfo.setInvoiceNode(dto.getNote());
        }
        applicationInfo.setInvoicingStatus(InvoicingStatus.INVOICED);

        if (flag) {
            invoicingApplicationInfoRepository.save(applicationInfo);
            log.info("[invoicing financialInvoicingVoidRecord]: update application info param:{}", JSONUtil.toJsonStr(applicationInfo));
        }

        String newCodeNumber = applicationInfo.getElectronicInvoiceNumber();
        String oldCodeNumber = oldInvoicingInfo.getElectronicInvoiceNumber();
        String newStr = "被红冲蓝字数电票号码";
        String oldStr = "红冲数电票号码";
        if (applicationInfo.getInvoicingType().equals(InvoicingType.DEDUCTION_INVOICE)) {
            newCodeNumber = applicationInfo.getCodeNumber();
            oldCodeNumber = oldInvoicingInfo.getCodeNumber();
            newStr = "原始发票开票编码";
            oldStr = "废票开票编码";
        }

        addLogInfo("作废发票", String.format(note, "作废该发票"), oldInvoicingInfo.getId(), oldStr,
                null,
                InvoicingLogStatus.PAYMENT, "financialInvoicingVoidRecord", dto.getElecInvoiceNumber(), applicationInfo.getId(), oldInvoicingInfo.getId());

        //废票审核通过不用添加开票数据
        if (flag) {
            List<InvoicingRecordLog> logList = invoicingRecordLogRepository.findByVoidReferenceIdAndInvoicingStatus(applicationInfo.getId(), InvoicingLogStatus.INVOICING);

            if (logList.isEmpty()) {
                addLogInfo("开票", String.format(note, "创建开票信息"), applicationInfo.getId(), newStr,
                        getLogJsonByInvoicing(newCodeNumber, null, applicationInfo.getInvoicingDate(), null),
                        InvoicingLogStatus.INVOICING, "financialInvoicingVoidRecord", oldCodeNumber, oldInvoicingInfo.getId(), applicationInfo.getId());
            } else {
                InvoicingRecordLog recordLog = logList.get(0);
                String subTitleStr = "财务" + SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "编辑开票信息。" + ";" + recordLog.getSubTitle();
                recordLog.setSubTitle(subTitleStr);
                String json = getLogJsonByInvoicing(newCodeNumber, null, applicationInfo.getInvoicingDate(), null);
                recordLog.setNoteJson(json);
                invoicingRecordLogRepository.save(recordLog);
            }
        }
        cancelPrepayment(oldInvoicingInfo);
        return oldInvoicingInfo.getId();
    }

    /**
     * 自动还原预付金金额
     *
     * @param oldInvoicingInfo 作废对应发票id  不是作废发票id
     */
    private void cancelPrepayment(InvoicingApplicationInfo oldInvoicingInfo) {
        //预付金类型
        if (oldInvoicingInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue())) {
            deleteUsePrepaymentInfo(oldInvoicingInfo.getId(), null, false);
        } else {
            //查询存在预付金
            List<PrepaymentInvoicingRelation> paymentRelationList = prepaymentInvoicingRelationRepository.findByInvoicingIdAndRelationTypeAndStatus(oldInvoicingInfo.getId(), InvoicingRelationType.ADD_RELATION, 1);
            if (null != paymentRelationList && !paymentRelationList.isEmpty()) {
                List<Long> prepaymentIdList = paymentRelationList.stream()
                        .map(PrepaymentInvoicingRelation::getPrepaymentInvoicingId)
                        .collect(Collectors.toList());
                List<Long> relationIdList = paymentRelationList.stream()
                        .map(PrepaymentInvoicingRelation::getInvoicingId)
                        .collect(Collectors.toList());
                List<InvoicingApplicationInfo> applicationInfoList = invoicingApplicationInfoRepository.findByIdIn(prepaymentIdList);
                List<Long> applicationIdList = new ArrayList<>();
                for (InvoicingApplicationInfo v : applicationInfoList) {
                    deleteUsePrepaymentInfo(v.getId(), null, false);
                    applicationIdList.add(v.getId());
                }

                //删除系统预付金
                invoicingApplicationInfoRepository.updateStatusById(applicationIdList);
                log.info("[invoicing financialInvoicingVoidRecord]: delete system application info param:{}", JSONUtil.toJsonStr(applicationIdList));

                //删除关系
                prepaymentInvoicingRelationRepository.updateStatusById(relationIdList);
                log.info("[invoicing financialInvoicingVoidRecord]: delete relation info param:{}", JSONUtil.toJsonStr(relationIdList));
            }
        }
    }

    private BigDecimal deleteUsePrepaymentInfo(Long invoicingId, BigDecimal cancelAmount, boolean flag) {

        BigDecimal total = new BigDecimal("0.00");

        //查询用了预付金的信息
        List<PrepaymentInvoicingRelation> prepaymentInvoicingRelationList = prepaymentInvoicingRelationRepository.findByPrepaymentInvoicingIdAndRelationTypeAndStatus(invoicingId, InvoicingRelationType.USE_RELATION, 1);
        if (null != prepaymentInvoicingRelationList && !prepaymentInvoicingRelationList.isEmpty()) {

            //关联id
            List<Long> relationList = new ArrayList<>();

            List<InvoicingRecordLog> logList = new ArrayList<>();

            List<Long> invoiceIdList = prepaymentInvoicingRelationList.stream().map(PrepaymentInvoicingRelation::getInvoicingId).collect(Collectors.toList());
            //查询到所有用该预付金的 开票信息
            List<InvoicingApplicationInfo> applicationInfoList = invoicingApplicationInfoRepository.findByIdIn(invoiceIdList);
            Map<Long, InvoicingApplicationInfo> applicationInfoMap = applicationInfoList.stream().collect(Collectors.toMap(InvoicingApplicationInfo::getId, person -> person));


            //查询支付记录
            List<InvoicingRecordPaymentInfo> recordPaymentInfos = invoicingRecordPaymentInfoRepository.findAllByInvoicingId(invoiceIdList);
            Map<Long, InvoicingRecordPaymentInfo> recordPaymentInfoMap = recordPaymentInfos.stream().collect(Collectors.toMap(InvoicingRecordPaymentInfo::getId, person -> person));

            //查询支付候选人明细
            List<Long> recordPaymentIds = recordPaymentInfos.stream().map(InvoicingRecordPaymentInfo::getId).collect(Collectors.toList());
            List<InvoicingRecordPaymentDetail> detailList = invoicingRecordPaymentDetailRepository.findByPaymentIdInAndStatus(recordPaymentIds, 1);
            Map<Long, List<InvoicingRecordPaymentDetail>> recordPaymentDetailMap = detailList.stream().collect(Collectors.groupingBy(InvoicingRecordPaymentDetail::getPaymentId));


            //查询候选人信息
            List<InvoicingCandidateInfo> invoicingCandidateInfos = invoicingCandidateInfoRepository.findByInvoiceApplicationIdInAndStatus(invoiceIdList, 1);
            Map<Long, List<InvoicingCandidateInfo>> invoicingCandidateInfoMap = invoicingCandidateInfos.stream().collect(Collectors.groupingBy(InvoicingCandidateInfo::getInvoiceApplicationId));


            BigDecimal cancelAmountSub = cancelAmount;
            for (PrepaymentInvoicingRelation relation : prepaymentInvoicingRelationList) {
                if (relation.getInvoicingAmount().equals(new BigDecimal("0.00"))) {
                    continue;
                }

                String subTitle = SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "取消回款记录";

                total = total.add(relation.getInvoicingAmount());

                BigDecimal invoicingAmount = relation.getInvoicingAmount();
                if (flag) {
                    if (cancelAmountSub.compareTo(new BigDecimal("0.00")) == 0) {
                        break;
                    }
                    BigDecimal z = relation.getInvoicingAmount().subtract(cancelAmountSub);
                    if (z.compareTo(new BigDecimal("0.00")) == 0) {
                        cancelAmountSub = BigDecimal.ZERO;
                        relation.setInvoicingAmount(BigDecimal.ZERO);
                    } else if (z.compareTo(new BigDecimal("0.00")) > 0) {
                        invoicingAmount = cancelAmountSub;
                        cancelAmountSub = BigDecimal.ZERO;
                        relation.setInvoicingAmount(z);
                    } else if (z.compareTo(new BigDecimal("0.00")) == -1) {
                        cancelAmountSub = cancelAmountSub.subtract(relation.getInvoicingAmount());
                        relation.setInvoicingAmount(BigDecimal.ZERO);
                    }
                }

                if (applicationInfoMap.containsKey(relation.getInvoicingId())) {

                    InvoicingRecordPaymentInfo record = recordPaymentInfoMap.get(relation.getPaymentId());

                    List<InvoicingRecordPaymentDetail> paymentDetails = recordPaymentDetailMap.get(record.getId());
                    Map<String, InvoicingRecordPaymentDetail> paymentDetailTalentMap = new HashMap<>();
                    if (null != paymentDetails && !paymentDetails.isEmpty()) {
                        paymentDetailTalentMap = paymentDetails.stream()
                                .collect(
                                        Collectors.toMap(k -> k.getTalentId() + "" + (null == k.getJobId() ? "" : k.getJobId()),
                                                p -> p));
                    }

                    if (null != record.getCandidateJson()) {
                        BigDecimal payment = invoicingAmount;
                        JSONArray objArry = JSONUtil.parseArray(record.getCandidateJson());
                        for (int i = 0; i < objArry.size(); i++) {
                            JSONObject obj = objArry.getJSONObject(i);
                            if (obj.containsKey("paymentAmount")) {
                                String paymentAmountStr = obj.getStr("paymentAmount");
                                Long talentId = obj.getLong("talentId");
                                String jobId = "";
                                if (obj.containsKey("jobId")) {
                                    jobId = obj.getStr("jobId");
                                }
                                InvoicingRecordPaymentDetail detail = null;
                                if (paymentDetailTalentMap.containsKey(talentId + jobId)) {
                                    detail = paymentDetailTalentMap.get(talentId + jobId);
                                    detail.setStatus(0);
                                }
                                BigDecimal paymentAmount = new BigDecimal(paymentAmountStr);
                                if (payment.compareTo(new BigDecimal("0.00")) == 0) {
                                    break;
                                }
                                BigDecimal x = paymentAmount.subtract(payment);
                                if (x.compareTo(new BigDecimal("0.00")) == 0) {
                                    obj.put("paymentAmount", 0);
                                    payment = BigDecimal.ZERO;
                                    if (detail != null) {
                                        detail.setPaymentAmount(BigDecimal.ZERO);
                                    }
                                } else if (x.compareTo(new BigDecimal("0.00")) > 0) {
                                    obj.put("paymentAmount", x);
                                    if (detail != null) {
                                        detail.setPaymentAmount(x);
                                    }
                                    payment = BigDecimal.ZERO;
                                } else if (x.compareTo(new BigDecimal("0.00")) == -1) {
                                    payment = payment.subtract(paymentAmount);
                                    obj.put("paymentAmount", 0);
                                    if (detail != null) {
                                        detail.setPaymentAmount(BigDecimal.ZERO);
                                    }
                                }
                            }
                        }
                        record.setCandidateJson(JSONUtil.toJsonStr(objArry));
                    }

                    //修改支付记录值
                    record.setPaymentAmount(record.getPaymentAmount().subtract(invoicingAmount));

                    InvoicingRecordPaymentInfo recordPaymentInfo = ObjectUtil.cloneByStream(record);
                    recordPaymentInfo.setPaymentAmount(invoicingAmount);

                    record.setStatus(0);

                    //获得系统的预付金
                    recoverApplicationInfo(applicationInfoMap.get(relation.getInvoicingId()), recordPaymentInfo, false);
                    relationList.add(relation.getId());


                    //添加log
                    subTitle = subTitle +
                            "，回款记录为：" +
                            DateUtil.fromInstantToDate(record.getPaymentDate(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD) +
                            " " + record.getPaymentMethod().getDescription() +
                            " " + invoicingAmount;
                    logList.add(addLogInfoOperate("取消回款", subTitle, record.getInvoicingId(), null, null, InvoicingLogStatus.PAYMENT, null, null, null));

                    //扣除候选人预付金金额
                    List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoMap.get(relation.getInvoicingId());
                    BigDecimal payment = invoicingAmount;
                    for (InvoicingCandidateInfo v : candidateInfoList) {
                        if (payment.compareTo(new BigDecimal("0.00")) == 0) {
                            break;
                        }
                        BigDecimal x = v.getAmountPrepayment().subtract(payment);
                        if (x.compareTo(new BigDecimal("0.00")) == 0) {
                            v.setAmountPrepayment(BigDecimal.ZERO);
                            payment = BigDecimal.ZERO;
                        } else if (x.compareTo(new BigDecimal("0.00")) > 0) {
                            v.setAmountPrepayment(x);
                            payment = BigDecimal.ZERO;
                        } else if (x.compareTo(new BigDecimal("0.00")) == -1) {
                            payment = payment.subtract(v.getAmountPrepayment());
                            v.setAmountPrepayment(BigDecimal.ZERO);
                        }
                    }

                }
            }

            if (!recordPaymentInfos.isEmpty()) {
                invoicingRecordPaymentInfoRepository.saveAll(recordPaymentInfos);
                log.info("[invoicing financialInvoicingVoidRecord]: update payment info param:{}", JSONUtil.toJsonStr(recordPaymentInfos));
            }

            if (detailList.isEmpty()) {
                invoicingRecordPaymentDetailRepository.saveAll(detailList);
                log.info("[invoicing financialInvoicingVoidRecord]: update payment talent detail param:{}", JSONUtil.toJsonStr(detailList));
            }


            if (!applicationInfoList.isEmpty()) {
                //更新状态
                invoicingApplicationInfoRepository.saveAllAndFlush(applicationInfoList);
                log.info("[invoicing financialInvoicingVoidRecord]: update invoicing application info info param:{}", JSONUtil.toJsonStr(applicationInfoList));
            }

            if (!invoicingCandidateInfos.isEmpty()) {
                invoicingCandidateInfoRepository.saveAll(invoicingCandidateInfos);
                log.info("[invoicing financialInvoicingVoidRecord]: update invoicing candidate info info param:{}", JSONUtil.toJsonStr(applicationInfoList));
            }

            if (!logList.isEmpty()) {
                invoicingRecordLogRepository.saveAll(logList);
                log.info("[invoicing financialInvoicingVoidRecord]: add record log info param:{}", JSONUtil.toJsonStr(logList));
            }

            if (!flag) {
                if (!relationList.isEmpty()) {
                    prepaymentInvoicingRelationRepository.updateStatusById(relationList);
                    log.info("[invoicing financialInvoicingVoidRecord]: delete relation info param:{}", JSONUtil.toJsonStr(relationList));
                }
            } else {
                if (!relationList.isEmpty()) {
                    prepaymentInvoicingRelationRepository.saveAll(prepaymentInvoicingRelationList);
                    log.info("[invoicing financialInvoicingVoidRecord]: update relation info param:{}", JSONUtil.toJsonStr(relationList));
                }
            }
        }
        return total;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void financialRecordPayment(InvoicingFinancialRecordPaymentDTO dto) {

        InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(dto.getInvoicingId(), false, null);

        if (applicationInfo.getStatus().equals(0) || !InvoicingUtils.paymentInvoicingStatus.contains(applicationInfo.getInvoicingStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        if (applicationInfo.getVoidInvoicing() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_TYPE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Pair<String, BigDecimal> result = modifyApplicationStatusAndAmountDue(applicationInfo, dto);

        InvoicingRecordPaymentInfo recordPaymentInfo = new InvoicingRecordPaymentInfo();

        ZonedDateTime zonedDateTime = dto.getPaymentDate().atZone(ZoneId.of("Asia/Shanghai"));

        recordPaymentInfo.setPaymentDate(zonedDateTime.toInstant());
        recordPaymentInfo.setPaymentAmount(dto.getPaymentAmount());
        recordPaymentInfo.setInvoicingId(dto.getInvoicingId());
        recordPaymentInfo.setNote(dto.getNote());
        recordPaymentInfo.setPaymentMethod(dto.getPaymentMethod());
        recordPaymentInfo.setStatus(1);
        JSONArray candidateArray = new JSONArray();
        List<InvoicingRecordPaymentDetail> detailList = new ArrayList<>();
        if (null != dto.getCandidateJson() && !dto.getCandidateJson().isEmpty()) {
            dto.getCandidateJson().forEach(v -> {
                JSONObject bean = new JSONObject();
                bean.put("gpAmount", v.getGpAmount());
                bean.put("paymentAmount", v.getPaymentAmount());
                bean.put("talentId", v.getTalentId());
                bean.put("talentName", v.getTalentName());
                bean.put("jobId",v.getJobId());
                candidateArray.add(bean);

                InvoicingRecordPaymentDetail detail = new InvoicingRecordPaymentDetail();
                detail.setTalentId(v.getTalentId());
                detail.setTalentName(v.getTalentName());
                detail.setGpAmount(v.getGpAmount());
                detail.setPaymentAmount(v.getPaymentAmount());
                detail.setPaymentDate(recordPaymentInfo.getPaymentDate());
                detail.setStatus(1);
                detail.setJobId(v.getJobId());
                detailList.add(detail);
            });
        }
        if (!candidateArray.isEmpty()) {
            recordPaymentInfo.setCandidateJson(JSONUtil.toJsonStr(candidateArray));
        }
        invoicingRecordPaymentInfoRepository.save(recordPaymentInfo);
        log.info("[invoicing financialRecordPayment]: save payment info info param:{}", JSONUtil.toJsonStr(recordPaymentInfo));

        if (!detailList.isEmpty()) {
            detailList.forEach(v -> {
                v.setPaymentId(recordPaymentInfo.getId());
            });
            invoicingRecordPaymentDetailRepository.saveAll(detailList);
            log.info("[invoicing financialRecordPayment]: save payment talent detail param:{}", JSONUtil.toJsonStr(detailList));
        }

        invoicingApplicationInfoRepository.save(applicationInfo);
        log.info("[invoicing financialRecordPayment]: save invoicing application info info param:{}", JSONUtil.toJsonStr(applicationInfo));

        final String subTitle = SecurityUtils.getUserName() + "于" + DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "创建回款记录";
        String note = StringUtils.isNotBlank(dto.getNote()) ? dto.getNote() : "";
        if (!result.getValue().equals(BigDecimal.ZERO) && !result.getValue().equals(new BigDecimal("0.00"))) {
            note = note + " 回款金额大于应付余额，回款中的￥" + result.getValue() + "转为预付金金额。";
        }

        String title = "";
        String amountDue = "0";
        if (result.getKey().indexOf("&") != -1) {
            String[] str = result.getKey().split("&");
            title = str[0] + "（应付余额：￥" + str[1] + "）";
            amountDue = str[1];
        } else {
            title = result.getKey();
        }
        addLogInfo(title, subTitle, dto.getInvoicingId(), note, getLogJson(amountDue, dto.getPaymentAmount().toString(), DateUtil.fromInstantToDate(recordPaymentInfo.getPaymentDate(), DateUtil.CN_BJ_TIMEZONE), dto.getPaymentMethod()), InvoicingLogStatus.PAYMENT, "financialRecordPayment", null, null, null);

        addSystemPrepaymentInfo(applicationInfo, result.getValue(), recordPaymentInfo.getId());
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            if (InvoicingStatus.FULLY_COLLECTED.equals(applicationInfo.getInvoicingStatus())) {
                SecurityContextHolder.setContext(context);
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(dto.getInvoicingId(), InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            }
        });
    }

    private void sendLarkMsgViaAnycross(String clientName, String candidateName, String invoiceNumber, String receiverEmail, String invoiceUrl, String larkLink) {
        String content = buildLarkJsonStr(clientName, candidateName, invoiceNumber, receiverEmail, invoiceUrl);
        sendLarkMessageViaAnycross(content, larkLink);
    }

    private String buildLarkJsonStr(String clientName, String candidateName, String invoiceNumber, String receiverEmail, String invoiceUrl) {

        // 3. 构建 JSON 对象
        JSONObject root = new JSONObject();
        root.put("candidateName", candidateName);
        root.put("clientName", clientName);
        root.put("invoiceNumber", invoiceNumber);
        root.put("invoiceUrl", invoiceUrl);

        // 收件人邮箱
        root.put("receiverEmail", receiverEmail);

        // 返回格式化后的 JSON 字符串
        return root.toStringPretty();
    }

    private void sendLarkMessageViaAnycross(String content, String url) {
        try {
            log.info("sendLarkMessageViaAnycross, url = {}, json body ={}",url, content);
            HttpResponse response = httpService.post(url, content);
            if (Objects.equals(response.getCode(), 200)) {
                log.info("sendLarkMessageViaAnycross is success, url = {}, result = {}", url, response.getBody());
            } else {
                log.error("sendLarkMessageViaAnycross is fail url = {}, result = {}", url, response.getBody());
            }
        } catch (Exception e) {
            log.error("sendLarkMessageViaAnycross exception url = {}, result = {}", url, ExceptionUtil.getAllExceptionMsg(e));
        }
    }

    @Resource
    private CalendarService calendarService;

    private void notifyCompleteSystemCalendar(Long talentRecruitmentProcessId, CalendarRelationEnum calendarRelation, List<CalendarTypeEnum> type) {
        if (talentRecruitmentProcessId == null) {
            return;
        }
        CompleteSystemCalendarDTO dto = new CompleteSystemCalendarDTO();
        dto.setRelationType(calendarRelation);
        dto.setType(type);
        dto.setUniqueReferenceId(talentRecruitmentProcessId);
        calendarService.completeSystemCalendar(dto);
    }

    private Pair<String, BigDecimal> modifyApplicationStatusAndAmountDue(InvoicingApplicationInfo applicationInfo, InvoicingFinancialRecordPaymentDTO dto) {
        BigDecimal over = BigDecimal.ZERO;
        String title = "回款-全部回款";
        //预付金类型 查询数据总额
        if (applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue())) {
            List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdAndStatus(applicationInfo.getId(), 1);
            BigDecimal totalBalance = paymentInfos.stream()
                    .map(p -> p.getPaymentAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //待回款金额
            BigDecimal subtract = applicationInfo.getInvoicingAmount().subtract(totalBalance);

            if (totalBalance.equals(BigDecimal.ZERO) && dto.getPaymentAmount().compareTo(applicationInfo.getInvoicingAmount()) > 0) {
                over = dto.getPaymentAmount().subtract(applicationInfo.getInvoicingAmount());
                applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);

                applicationInfo.setAmountDue(applicationInfo.getInvoicingAmount());

            } else if (dto.getPaymentAmount().compareTo(subtract) >= 0) {
                over = dto.getPaymentAmount().subtract(subtract);
                applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                applicationInfo.setAmountDue(applicationInfo.getAmountDue().add(subtract));

            } else if (subtract.compareTo(dto.getPaymentAmount()) > 0) {
                title = "回款-部分回款&" + subtract.subtract(dto.getPaymentAmount());
                if (!applicationInfo.getInvoicingStatus().equals(InvoicingStatus.OVERDUE)) {
                    applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                }
                //预付金 amount due 加值
                applicationInfo.setAmountDue(applicationInfo.getAmountDue().add(dto.getPaymentAmount()));
            }

        } else {
            //全职类型
            if (dto.getPaymentAmount().compareTo(applicationInfo.getAmountDue()) >= 0) {
                over = dto.getPaymentAmount().subtract(applicationInfo.getAmountDue());
                applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                applicationInfo.setAmountDue(BigDecimal.ZERO);
            } else if (applicationInfo.getAmountDue().compareTo(dto.getPaymentAmount()) > 0) {
                title = "回款-部分回款&" + applicationInfo.getAmountDue().subtract(dto.getPaymentAmount());
                if (!applicationInfo.getInvoicingStatus().equals(InvoicingStatus.OVERDUE)) {
                    applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                }
                applicationInfo.setAmountDue(applicationInfo.getAmountDue().subtract((dto.getPaymentAmount())));
            }
        }

        return new Pair<>(title, over);
    }


    /**
     * @param applicationInfo
     * @param over
     * @param paymentId       回款id
     */
    private void addSystemPrepaymentInfo(InvoicingApplicationInfo applicationInfo, BigDecimal over, Long paymentId) {
        if (!over.equals(BigDecimal.ZERO) && !over.equals(new BigDecimal("0.00"))) {
            //Long prepaymentId;

            /*List<InvoicingApplicationInfo> preApplicationList = invoicingApplicationInfoRepository.findByCompanyIdAndPrepaymentType(applicationInfo.getCompanyId(), 1, InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue(), applicationInfo.getTenantId(), 1);
            if (preApplicationList.isEmpty()) {*/
            InvoicingApplicationInfo info = new InvoicingApplicationInfo();
            BeanUtils.copyProperties(applicationInfo, info);
            info.setId(null);
            info.setCodeNumber(null);
            info.setAmountDue(over);
            info.setInvoicingAmount(BigDecimal.ZERO);
            info.setGpAmount(BigDecimal.ZERO);
            info.setUninvoicedAmount(BigDecimal.ZERO);
            info.setInvoicingApplicationType(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue());
            info.setPrepaymentType(1);
            info.setInvoiceTax(BigDecimal.ZERO);
            info.setTaxesNotIncluded(BigDecimal.ZERO);
            info.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
            info.setElectronicInvoiceNumber(null);
            info.setInvoicingDate(null);
            info.setDueWithinDays(null);
            info.setPaymentDueDate(null);
            info.setNote(null);
            info.setStatus(1);
            invoicingApplicationInfoRepository.save(info);
            log.info("[invoicing financialRecordPayment]: save system prepayment invoicing application info param:{}", JSONUtil.toJsonStr(info));
            // prepaymentId = info.getId();
            /*} else {
                InvoicingApplicationInfo info = preApplicationList.get(0);
                info.setAmountDue(info.getAmountDue().add(over));
                invoicingApplicationInfoRepository.save(info);
                log.info("[invoicing financialRecordPayment]: update system prepayment invoicing application info param:{}", JSONUtil.toJsonStr(info));
                prepaymentId = info.getId();
            }*/

            PrepaymentInvoicingRelation relation = new PrepaymentInvoicingRelation();
            relation.setInvoicingId(applicationInfo.getId());
            relation.setPaymentId(paymentId);
            relation.setPrepaymentInvoicingId(info.getId());
            relation.setInvoicingAmount(over);
            relation.setStatus(1);
            relation.setRelationType(InvoicingRelationType.ADD_RELATION);
            prepaymentInvoicingRelationRepository.save(relation);
            log.info("[invoicing financialRecordPayment]: insert prepayment invoicing relation info param:{}", JSONUtil.toJsonStr(relation));

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyFinancialRecordPayment(InvoicingFinancialRecordPaymentDTO dto) {

        InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(dto.getInvoicingId(), false, null);

        if (applicationInfo.getStatus().equals(0) || !InvoicingUtils.paymentModifyInvoicingStatus.contains(applicationInfo.getInvoicingStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        if (applicationInfo.getVoidInvoicing() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_TYPE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Optional<InvoicingRecordPaymentInfo> recordPaymentInfoOpt = invoicingRecordPaymentInfoRepository.findById(dto.getId());
        if (!recordPaymentInfoOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        InvoicingRecordPaymentInfo recordPaymentInfo = recordPaymentInfoOpt.get();
        //预付金支付 不能修改
        if (recordPaymentInfo.getPrepaymentId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_PAYMENT_ISUSE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Pair<String, BigDecimal> result = modifyApplicationStatusAndAmountDue(applicationInfo, dto);

        ZonedDateTime zonedDateTime = dto.getPaymentDate().atZone(ZoneId.of("Asia/Shanghai"));

        recordPaymentInfo.setPaymentDate(zonedDateTime.toInstant());
        recordPaymentInfo.setPaymentAmount(dto.getPaymentAmount());
        recordPaymentInfo.setInvoicingId(dto.getInvoicingId());
        recordPaymentInfo.setNote(dto.getNote());
        recordPaymentInfo.setPaymentMethod(dto.getPaymentMethod());
        JSONArray candidateArray = new JSONArray();
        List<InvoicingRecordPaymentDetail> detailList = new ArrayList<>();
        if (!dto.getCandidateJson().isEmpty()) {
            dto.getCandidateJson().forEach(v -> {
                JSONObject bean = new JSONObject();
                bean.put("gpAmount", v.getGpAmount());
                bean.put("paymentAmount", v.getPaymentAmount());
                bean.put("talentId", v.getTalentId());
                bean.put("talentName", v.getTalentName());
                bean.put("jobId",v.getJobId());
                candidateArray.add(bean);

                InvoicingRecordPaymentDetail detail = new InvoicingRecordPaymentDetail();
                detail.setTalentId(v.getTalentId());
                detail.setTalentName(v.getTalentName());
                detail.setGpAmount(v.getGpAmount());
                detail.setPaymentAmount(v.getPaymentAmount());
                detail.setPaymentId(recordPaymentInfo.getId());
                detail.setStatus(1);
                detail.setJobId(v.getJobId());
                detailList.add(detail);
            });
        }
        recordPaymentInfo.setCandidateJson(JSONUtil.toJsonStr(candidateArray));
        invoicingRecordPaymentInfoRepository.save(recordPaymentInfo);
        log.info("[invoicing financialRecordPayment]: save payment info info param:{}", JSONUtil.toJsonStr(recordPaymentInfo));

        invoicingRecordPaymentDetailRepository.updateStatusByPaymentId(recordPaymentInfo.getId());
        if (!detailList.isEmpty()) {
            invoicingRecordPaymentDetailRepository.saveAll(detailList);
        }

        invoicingApplicationInfoRepository.save(applicationInfo);
        log.info("[invoicing financialRecordPayment]: save invoicing application info info param:{}", JSONUtil.toJsonStr(applicationInfo));

        if (!result.getValue().equals(BigDecimal.ZERO)) {
            addSystemPrepaymentInfo(applicationInfo, result.getValue(), dto.getId());
        }
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            if (InvoicingStatus.FULLY_COLLECTED.equals(applicationInfo.getInvoicingStatus())) {
                SecurityContextHolder.setContext(context);
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(dto.getInvoicingId(), InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            }
        });

    }

    public boolean isCurrentDateGreaterThan(Instant inputInstant) {

        ZoneId zoneId = ZoneId.of(DateUtil.CN_BJ_TIMEZONE);
        ZonedDateTime zonedDateTime = inputInstant.atZone(zoneId);

        LocalDate currentDate = LocalDate.now(zoneId); // 获取当前日期

        return currentDate.isAfter(zonedDateTime.toLocalDate()); // 比较日期
    }

    private void modifyStatus(InvoicingApplicationInfo applicationInfo, InvoicingRecordPaymentInfo recordPaymentInfo) {
        //查询回款的开票信息
        if (applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue())) {
            List<InvoicingRecordPaymentInfo> infos = invoicingRecordPaymentInfoRepository.findByInvoicingIdAndStatus(applicationInfo.getId(), 1);
            BigDecimal paymentAmountSum = infos.stream()
                    .map(p -> p.getPaymentAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal amountDue = paymentAmountSum.subtract(recordPaymentInfo.getPaymentAmount());
            applicationInfo.setAmountDue(applicationInfo.getAmountDue().subtract(recordPaymentInfo.getPaymentAmount()));

            if (!InvoicingUtils.cancelPayment.contains(applicationInfo.getInvoicingStatus())) {
                if (applicationInfo.getPaymentDueDate() != null) {
                    if (isCurrentDateGreaterThan(applicationInfo.getPaymentDueDate())) {
                        applicationInfo.setInvoicingStatus(InvoicingStatus.OVERDUE);
                    } else if (amountDue.compareTo(new BigDecimal("0.00")) <= 0) {
                        applicationInfo.setInvoicingStatus(InvoicingStatus.UNCOLLECTED);
                    } else {
                        applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                    }
                } else {
                    if (amountDue.compareTo(new BigDecimal("0.00")) <= 0) {
                        applicationInfo.setInvoicingStatus(InvoicingStatus.UNCOLLECTED);
                    } else {
                        applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                    }
                }
            }
        } else {
            //取消回款 要增加的金额
            BigDecimal amount = BigDecimal.ZERO;
            //候选人数据
            List<InvoicingCandidateInfo> candidateList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdAndStatus(applicationInfo.getId(), 1);
            Map<String, BigDecimal> payMap = new HashMap<>();
            if (StringUtils.isNotBlank(recordPaymentInfo.getCandidateJson())) {
                boolean existJobId = false;
                JSONArray objArry = JSONUtil.parseArray(recordPaymentInfo.getCandidateJson());
                for (int i = 0; i < objArry.size(); i++) {
                    JSONObject obj = objArry.getJSONObject(i);
                    Long talentId = obj.getLong("talentId");
                    String paymentAmount = obj.getStr("paymentAmount");
                    String jobId = "";
                    if (obj.containsKey("jobId")) {
                        jobId = obj.getStr("jobId");
                        existJobId = true;
                    }
                    if (StringUtils.isNotBlank(paymentAmount)) {
                        if (payMap.containsKey(talentId + jobId)) {
                            payMap.put(talentId + jobId, payMap.get(talentId + jobId).add(obj.getBigDecimal("paymentAmount")));
                        } else {
                            payMap.put(talentId + jobId, obj.getBigDecimal("paymentAmount"));
                        }
                    }
                }

                //循环获取每个候选人应该回款的金额 如果是未结清关闭了 不增加金额
                for (InvoicingCandidateInfo v : candidateList) {
                    if (null != v.getAmountReceivedTax()) {
                        if (existJobId) {
                            amount = amount.add(payMap.get(v.getTalentId().toString() + v.getJobId().toString()));
                        } else {
                            amount = amount.add(payMap.get(v.getTalentId().toString()));
                        }
                    }
                }
                applicationInfo.setAmountDue(applicationInfo.getAmountDue().add(amount));
            } else {
                amount = recordPaymentInfo.getPaymentAmount();
                applicationInfo.setAmountDue(applicationInfo.getAmountDue().add(recordPaymentInfo.getPaymentAmount()));
            }

            //查询已经回款金额
            List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdAndStatus(applicationInfo.getId(), 1);
            BigDecimal paymentAmount = paymentInfos.stream()
                    .filter(v -> v.getId() != recordPaymentInfo.getId())
                    .map(p -> p.getPaymentAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal invoiceAmount = BigDecimal.ZERO;
            //需要付款总额
            for (InvoicingCandidateInfo v : candidateList) {
                if (null != v.getAmountReceivedTax()) {
                    invoiceAmount = invoiceAmount.add(v.getAmountReceivedTax());
                }
            }

            if (!InvoicingUtils.cancelPayment.contains(applicationInfo.getInvoicingStatus())
                    && amount.compareTo(new BigDecimal("0.00")) != 0) {
                setInvoicingStatus(applicationInfo, invoiceAmount, paymentAmount);
            } else if (!InvoicingUtils.cancelPayment.contains(applicationInfo.getInvoicingStatus())
                    && amount.compareTo(new BigDecimal("0.00")) == 0
                    && !applicationInfo.getInvoicingStatus().equals(InvoicingStatus.FULLY_COLLECTED)) {
                setInvoicingStatus(applicationInfo, invoiceAmount, paymentAmount);
            }
        }
    }

    private void setInvoicingStatus(InvoicingApplicationInfo applicationInfo, BigDecimal invoiceAmount, BigDecimal paymentAmount) {
        if (applicationInfo.getPaymentDueDate() != null) {
            if (isCurrentDateGreaterThan(applicationInfo.getPaymentDueDate())) {
                applicationInfo.setInvoicingStatus(InvoicingStatus.OVERDUE);
            } else if (applicationInfo.getAmountDue().compareTo(invoiceAmount) >= 0 && paymentAmount.compareTo(new BigDecimal("0.00")) == 0) {
                applicationInfo.setInvoicingStatus(InvoicingStatus.UNCOLLECTED);
            } else {
                applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
            }
        } else {
            if (applicationInfo.getAmountDue().compareTo(invoiceAmount) >= 0 && paymentAmount.compareTo(new BigDecimal("0.00")) == 0) {
                applicationInfo.setInvoicingStatus(InvoicingStatus.UNCOLLECTED);
            } else {
                applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
            }
        }
    }

    /**
     * @param applicationInfo
     * @param recordPaymentInfo
     * @param flag              true 撤销付款记录
     * @return
     */
    private List<InvoicingApplicationInfo> recoverApplicationInfo(InvoicingApplicationInfo applicationInfo, InvoicingRecordPaymentInfo recordPaymentInfo, Boolean flag) {

        modifyStatus(applicationInfo, recordPaymentInfo);

        if (flag) {
            if (applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue())) {
                BigDecimal total = deleteUsePrepaymentInfo(applicationInfo.getId(), recordPaymentInfo.getPaymentAmount(), true);
                if (!total.equals(new BigDecimal("0.00"))) {
                    applicationInfo.setAmountDue(applicationInfo.getAmountDue().add(recordPaymentInfo.getPaymentAmount()));
                }
            } else {
                List<PrepaymentInvoicingRelation> prepaymentInvoicingRelationList = prepaymentInvoicingRelationRepository.findByPaymentIdAndRelationTypeAndStatus(recordPaymentInfo.getId(), InvoicingRelationType.USE_RELATION, 1);
                if (null != prepaymentInvoicingRelationList && !prepaymentInvoicingRelationList.isEmpty()) {
                    //预付金id,关系对象
                    Map<Long, PrepaymentInvoicingRelation> prepaymentInvoicingRelationMap = prepaymentInvoicingRelationList.stream().collect(Collectors.toMap(PrepaymentInvoicingRelation::getPrepaymentInvoicingId, a -> a));
                    List<Long> appIds = prepaymentInvoicingRelationList.stream().map(PrepaymentInvoicingRelation::getPrepaymentInvoicingId).collect(Collectors.toList());

                    //查询预付金数据
                    List<InvoicingApplicationInfo> preApplicationInfoList = invoicingApplicationInfoRepository.findByIdIn(appIds);
                    for (InvoicingApplicationInfo info : preApplicationInfoList) {
                        PrepaymentInvoicingRelation relation = prepaymentInvoicingRelationMap.get(info.getId());
                        info.setAmountDue(info.getAmountDue().add(relation.getInvoicingAmount()));
                    }

                    invoicingCandidateInfoRepository.updatePrepaymentByInvoicingId(applicationInfo.getId());
                    log.info("[invoicing financialInvoicingVoidRecord]: update invoicing candidate info info param:{}", applicationInfo.getId());

                    return preApplicationInfoList;
                }
            }
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void financialBulkRecordPayment(InvoicingFinancialRecordPaymentDTO dto) {
        if (null == dto.getInvoicingIdList() || dto.getInvoicingIdList().isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        List<InvoicingApplicationInfo> invoicingApplicationInfos = invoicingApplicationInfoRepository.findByIdIn(dto.getInvoicingIdList());
        if (invoicingApplicationInfos.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Set<Integer> statusList = invoicingApplicationInfos.stream().map(InvoicingApplicationInfo::getStatus).collect(Collectors.toSet());
        if (statusList.contains(0)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Set<InvoicingStatus> invoicingStatusSet = invoicingApplicationInfos.stream().map(InvoicingApplicationInfo::getInvoicingStatus).collect(Collectors.toSet());
        invoicingStatusSet.forEach(v -> {
            if (!InvoicingUtils.paymentInvoicingStatus.contains(v)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
        });

        Set<Integer> applicationType = invoicingApplicationInfos.stream().filter(v -> v.getVoidInvoicing() != null).map(InvoicingApplicationInfo::getInvoicingApplicationType).collect(Collectors.toSet());
        if (!applicationType.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_TYPE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        List<InvoicingRecordPaymentInfo> recordPaymentInfos = new ArrayList<>();
        List<InvoicingRecordLog> logList = new ArrayList<>();
        final String subTitle = DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) + "创建回款记录";

        List<Long> invoicingIdList = invoicingApplicationInfos.stream().map(InvoicingApplicationInfo::getId).collect(Collectors.toList());
        List<InvoicingRecordPaymentInfo> paymentInfoList = invoicingRecordPaymentInfoRepository.findByInvoicingIdInAndStatus(invoicingIdList, 1);
        Map<Long, List<InvoicingRecordPaymentInfo>> paymentMap = paymentInfoList.stream().collect(Collectors.groupingBy(InvoicingRecordPaymentInfo::getInvoicingId));

        List<InvoicingCandidateInfo> candidateInfoAllList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdInAndStatus(invoicingIdList, 1);
        Map<Long, List<InvoicingCandidateInfo>> candidateMap = candidateInfoAllList.stream().collect(Collectors.groupingBy(InvoicingCandidateInfo::getInvoiceApplicationId));

        Map<Long, List<InvoicingRecordPaymentDetail>> detailMap = new HashMap<>();

        for (InvoicingApplicationInfo info : invoicingApplicationInfos) {

            Map<String, BigDecimal> talentPayment = new HashMap<>();
            BigDecimal payment = BigDecimal.ZERO;
            if (info.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue())) {
                List<InvoicingRecordPaymentInfo> paymentInfos = paymentMap.get(info.getId());
                BigDecimal totalBalance = BigDecimal.ZERO;
                if (null != paymentInfos && !paymentInfos.isEmpty()) {
                    totalBalance = paymentInfos.stream()
                            .map(p -> p.getPaymentAmount())
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                payment = info.getInvoicingAmount().subtract(totalBalance);

                info.setAmountDue(info.getAmountDue().add(payment));
            } else {
                List<InvoicingRecordPaymentInfo> paymentInfos = paymentMap.get(info.getId());
                if (null != paymentInfos && !paymentInfos.isEmpty()) {
                    paymentInfos.forEach(record -> {
                        JSONArray objArry = JSONUtil.parseArray(record.getCandidateJson());
                        for (int i = 0; i < objArry.size(); i++) {
                            JSONObject obj = objArry.getJSONObject(i);
                            if (obj.containsKey("paymentAmount")) {
                                BigDecimal paymentAmountStr = obj.getBigDecimal("paymentAmount");
                                Long talentId = obj.getLong("talentId");
                                String jobId = "";
                                if (obj.containsKey("jobId")) {
                                    jobId = obj.getStr("jobId");
                                }
                                if (talentPayment.containsKey(talentId + jobId)) {
                                    BigDecimal existAmount = talentPayment.get(talentId + jobId);
                                    existAmount = existAmount.add(paymentAmountStr);
                                    talentPayment.put(talentId + jobId, existAmount);
                                } else {
                                    talentPayment.put(talentId + jobId, paymentAmountStr);
                                }
                            }
                        }
                    });
                }
                payment = info.getAmountDue();
                info.setAmountDue(BigDecimal.ZERO);
            }

            info.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);

            InvoicingRecordPaymentInfo recordPaymentInfo = new InvoicingRecordPaymentInfo();
            ZonedDateTime zonedDateTime = dto.getPaymentDate().atZone(ZoneId.of("Asia/Shanghai"));
            recordPaymentInfo.setPaymentDate(zonedDateTime.toInstant());
            recordPaymentInfo.setPaymentAmount(payment);
            recordPaymentInfo.setInvoicingId(info.getId());
            recordPaymentInfo.setNote(dto.getNote());
            recordPaymentInfo.setPaymentMethod(dto.getPaymentMethod());
            recordPaymentInfo.setStatus(1);
            if (info.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION.toDbValue())) {
                JSONArray candidateArray = new JSONArray();
                List<InvoicingRecordPaymentDetail> detailList = new ArrayList<>();
                List<InvoicingCandidateInfo> candidateInfoList = candidateMap.get(info.getId());
                if (null != candidateInfoList && !candidateInfoList.isEmpty()) {
                    for (InvoicingCandidateInfo v : candidateInfoList) {
                        if (null == v.getAmountReceivedTax()) {
                            continue;
                        }
                        JSONObject bean = new JSONObject();
                        bean.put("gpAmount", v.getAmountReceivedTax());
                        if (talentPayment.containsKey(v.getTalentId().toString()) || talentPayment.containsKey(v.getTalentId().toString() + v.getJobId().toString())) {
                            BigDecimal paymentAmount = new BigDecimal("0.00");
                            if (talentPayment.containsKey(v.getTalentId().toString())) {
                                paymentAmount = talentPayment.get(v.getTalentId().toString());
                            } else {
                                paymentAmount = talentPayment.get(v.getTalentId().toString() + v.getJobId());
                            }
                            bean.put("paymentAmount", v.getAmountReceivedTax().subtract(paymentAmount));
                        } else {
                            bean.put("paymentAmount", v.getAmountReceivedTax());
                        }
                        bean.put("talentId", v.getTalentId());
                        bean.put("talentName", v.getTalentName());
                        bean.put("jobId", v.getJobId());
                        candidateArray.add(bean);

                        InvoicingRecordPaymentDetail detail = new InvoicingRecordPaymentDetail();
                        detail.setTalentId(v.getTalentId());
                        detail.setTalentName(v.getTalentName());
                        detail.setGpAmount(v.getGpAmount());
                        detail.setPaymentAmount(bean.getBigDecimal("paymentAmount"));
                        detail.setPaymentDate(recordPaymentInfo.getPaymentDate());
                        detail.setStatus(1);
                        detail.setJobId(v.getJobId());
                        detailList.add(detail);
                    }
                }
                recordPaymentInfo.setCandidateJson(JSONUtil.toJsonStr(candidateArray));
                detailMap.put(info.getId(), detailList);
            }
            recordPaymentInfos.add(recordPaymentInfo);
            logList.add(addLogInfoOperate("回款-全部回款", subTitle, info.getId(), dto.getNote(), getLogJson("0", payment.toString(), DateUtil.fromInstantToDate(recordPaymentInfo.getPaymentDate(), DateUtil.CN_BJ_TIMEZONE), dto.getPaymentMethod()), InvoicingLogStatus.PAYMENT, null, null, null));
        }


        invoicingRecordPaymentInfoRepository.saveAll(recordPaymentInfos);
        log.info("[invoicing financialRecordPayment]: save payment info info param:{}", JSONUtil.toJsonStr(recordPaymentInfos));

        if (!detailMap.isEmpty()) {
            recordPaymentInfos.forEach(v -> {
                if (detailMap.containsKey(v.getInvoicingId())) {
                    List<InvoicingRecordPaymentDetail> detailList = detailMap.get(v.getInvoicingId());
                    if (!detailList.isEmpty()) {
                        detailList.forEach(x -> {
                            x.setPaymentId(v.getId());
                        });
                    }

                    invoicingRecordPaymentDetailRepository.saveAll(detailList);
                    log.info("[invoicing financialRecordPayment]: save payment talent detail param:{}", JSONUtil.toJsonStr(recordPaymentInfos));
                }
            });
        }

        invoicingApplicationInfoRepository.saveAll(invoicingApplicationInfos);
        log.info("[invoicing financialRecordPayment]: update invoicing application info param:{}", JSONUtil.toJsonStr(invoicingApplicationInfos));

        invoicingRecordLogRepository.saveAll(logList);
        log.info("[invoicing financialRecordPayment]: add record log info param:{}", JSONUtil.toJsonStr(logList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void financialUnrecordPayment(InvoicingFinancialRecordPaymentDTO dto) {

        if (dto.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        final String key = "invoicing:unRecord:payment:" + dto.getId();
        if (Objects.equals(commonRedisService.setNxAndExpire(key, "1", 120L), 1L)) {

            Optional<InvoicingRecordPaymentInfo> recordPaymentInfoOpt = invoicingRecordPaymentInfoRepository.findById(dto.getId());
            if (!recordPaymentInfoOpt.isPresent()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }

            InvoicingRecordPaymentInfo recordPaymentInfo = recordPaymentInfoOpt.get();

            InvoicingApplicationInfo applicationInfo = checkStatusAndPermission(recordPaymentInfo.getInvoicingId(), false, null);

            if (applicationInfo.getStatus().equals(0) || !InvoicingUtils.paymentModifyInvoicingStatus.contains(applicationInfo.getInvoicingStatus())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }

            //获得系统的预付金
            List<InvoicingApplicationInfo> preApplicationInfo = recoverApplicationInfo(applicationInfo, recordPaymentInfo, true);

            if (null != preApplicationInfo) {
                invoicingApplicationInfoRepository.saveAllAndFlush(preApplicationInfo);
                log.info("[invoicing financialUnrecordPayment]: update prepayment invoicing application info param:{}", JSONUtil.toJsonStr(preApplicationInfo));

                //删除关联数据
                prepaymentInvoicingRelationRepository.updateStatusByInvoicingIdAndPaymentId(recordPaymentInfo.getInvoicingId(), Arrays.asList(recordPaymentInfo.getId()));
                log.info("[invoicing financialUnrecordPayment]: delete payment relation info param:{}", JSONUtil.toJsonStr(recordPaymentInfo));
            }

            invoicingRecordPaymentInfoRepository.updateStatusById(recordPaymentInfo.getId());
            log.info("[invoicing financialUnrecordPayment]: delete payment info param:{}", JSONUtil.toJsonStr(recordPaymentInfo));

            invoicingRecordPaymentDetailRepository.updateStatusByPaymentId(recordPaymentInfo.getId());
            log.info("[invoicing financialUnrecordPayment]: delete payment detail param:{}", JSONUtil.toJsonStr(recordPaymentInfo));


            //添加log
            final String subTitle = SecurityUtils.getUserName() + "于" +
                    DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) +
                    "取消回款记录，回款记录为：" +
                    DateUtil.fromInstantToDate(recordPaymentInfo.getPaymentDate(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD) +
                    " " + recordPaymentInfo.getPaymentMethod().getDescription() +
                    " " + recordPaymentInfo.getPaymentAmount();
            addLogInfo("取消回款", subTitle, applicationInfo.getId(), null, null, InvoicingLogStatus.PAYMENT, "financialUnrecordPayment", null, null, null);

            //查询创建预付金信息
            List<PrepaymentInvoicingRelation> prepaymentInvoicingRelationList = prepaymentInvoicingRelationRepository.findByPaymentIdAndRelationTypeAndStatus(recordPaymentInfo.getId(), InvoicingRelationType.ADD_RELATION, 1);
            if (null != prepaymentInvoicingRelationList && !prepaymentInvoicingRelationList.isEmpty()) {
                PrepaymentInvoicingRelation relation = prepaymentInvoicingRelationList.get(0);
                Optional<InvoicingApplicationInfo> prepaymentInfoOpt = invoicingApplicationInfoRepository.findById(relation.getPrepaymentInvoicingId());
                if (!prepaymentInfoOpt.isPresent()) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
                }
                InvoicingApplicationInfo prepaymentInfo = prepaymentInfoOpt.get();
                deleteUsePrepaymentInfo(prepaymentInfo.getId(), null, false);


                if (applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.PREPAYMENT_INVOICING_APPLICATION.toDbValue())) {
                    applicationInfo.setAmountDue(applicationInfo.getAmountDue().add(relation.getInvoicingAmount()));

                } else {
                    applicationInfo.setAmountDue(applicationInfo.getAmountDue().subtract(relation.getInvoicingAmount()));
                    if (!InvoicingUtils.cancelPayment.contains(applicationInfo.getInvoicingStatus())) {
                        if (applicationInfo.getAmountDue().compareTo(applicationInfo.getInvoicingAmount()) == 0) {
                            applicationInfo.setInvoicingStatus(InvoicingStatus.UNCOLLECTED);
                        } else {
                            if (applicationInfo.getAmountDue().compareTo(new BigDecimal("0.00")) != 0) {
                                applicationInfo.setInvoicingStatus(InvoicingStatus.PARTIALLY_COLLECTED);
                            }
                        }
                    }
                }

                //删除系统预付金
                invoicingApplicationInfoRepository.updateStatusById(Arrays.asList(prepaymentInfo.getId()));
                log.info("[invoicing financialUnrecordPayment]: delete system application info param:{}", JSONUtil.toJsonStr(prepaymentInfo));

                //删除关系
                prepaymentInvoicingRelationRepository.updateStatusById(Arrays.asList(relation.getId()));
                log.info("[invoicing financialUnrecordPayment]: delete relation info param:{}", JSONUtil.toJsonStr(relation));
            }

            //判断有没有未结清结束
            List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdAndStatus(applicationInfo.getId(), 1);
            if (!candidateInfoList.isEmpty()) {
                List<Long> candidateIds = candidateInfoList.stream()
                        .filter(x -> x.getAmountReceivedTax() == null)
                        .map(InvoicingCandidateInfo::getId).collect(Collectors.toList());
                if (candidateInfoList.size() == candidateIds.size()) {
                    applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                }
            }

            //更新状态
            invoicingApplicationInfoRepository.save(applicationInfo);
            log.info("[invoicing financialUnrecordPayment]: save invoicing application info info param:{}", JSONUtil.toJsonStr(applicationInfo));

            commonRedisService.delete(key);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void outstandingClose(InvoicingOutstandingCloseDTO dto) {
        if (dto.getInvoicingId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        Optional<InvoicingApplicationInfo> applicationInfoOpt = invoicingApplicationInfoRepository.findById(dto.getInvoicingId());
        if (!applicationInfoOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        final String key = "invoicing:outstandingClose:" + dto.getInvoicingId();
        if (Objects.equals(commonRedisService.setNxAndExpire(key, "1", 120L), 1L)) {
            InvoicingApplicationInfo applicationInfo = applicationInfoOpt.get();

            if (!SecurityUtils.isAdmin()) {
                if (!applicationInfo.getPermissionUserId().equals(SecurityUtils.getUserId())) {
                    throw new CustomParameterizedException("Only the creator is allowed to perform operations");
                }
            }

            if (!applicationInfo.getInvoicingApplicationType().equals(InvoicingApplicationType.INVOICING_APPLICATION.toDbValue())) {
                throw new CustomParameterizedException("The operation is not allowed for the billing type");
            }

            if (!InvoicingUtils.outstandingCloseInvoicingStatus.contains(applicationInfo.getInvoicingStatus())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICING_APPLICATION_STATUS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }

            List<InvoicingRecordPaymentInfo> recordPaymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdAndStatus(applicationInfo.getId(), 1);
            Map<String, BigDecimal> payMap = new HashMap<>();
            for (InvoicingRecordPaymentInfo pay : recordPaymentInfos) {
                if (StringUtils.isNotBlank(pay.getCandidateJson())) {
                    JSONArray objArry = JSONUtil.parseArray(pay.getCandidateJson());
                    for (int i = 0; i < objArry.size(); i++) {
                        JSONObject obj = objArry.getJSONObject(i);
                        Long talentId = obj.getLong("talentId");
                        String paymentAmount = obj.getStr("paymentAmount");
                        String jobId = "";
                        if (obj.containsKey("jobId")) {
                            jobId = obj.getStr("jobId");
                        }
                        if (StringUtils.isNotBlank(paymentAmount)) {
                            if (payMap.containsKey(talentId + jobId)) {
                                payMap.put(talentId + jobId, payMap.get(talentId + jobId).add(obj.getBigDecimal("paymentAmount")));
                            } else {
                                payMap.put(talentId + jobId, obj.getBigDecimal("paymentAmount"));
                            }
                        }
                    }
                }
            }

            List<InvoicingOutstandingClose> closeList = invoicingOutstandingCloseRepository.findByInvoicingId(applicationInfo.getId());
            BigDecimal closeAmount = closeList.stream()
                    .map(p -> p.getCloseAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            JSONArray objs = new JSONArray();

            List<BigDecimal> unPaymentAmountList = new ArrayList<>();

            //保存对象数据
            List<InvoicingOutstandingClose> outstandingClosesList = new ArrayList<>();

            //获取候选人信息
            List<String> talentIds = dto.getCandidateList().stream().map(x -> x.getTalentId() + "-" + x.getStartId()).collect(Collectors.toList());
            List<Long> ids = dto.getCandidateList().stream().map(InvoicingCandidateInfoDTO::getId).collect(Collectors.toList());
            List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoRepository.findByIdInAndStatus(ids, 1);
            candidateInfoList.forEach(v -> {
                if (v.getAmountReceivedTax() == null) {
                    throw new CustomParameterizedException("The outstanding closure has been executed, please do not repeat the operation.");
                }
                String talentIdStr = v.getTalentId() + "-" + v.getStartId();
                if (talentIds.contains(talentIdStr)) {
                    InvoicingOutstandingClose closeBean = new InvoicingOutstandingClose();
                    closeBean.setCandidateId(v.getId());
                    closeBean.setTalentId(v.getTalentId());
                    closeBean.setInvoicingId(applicationInfo.getId());
                    if (payMap.containsKey(v.getTalentId().toString()) || payMap.containsKey(v.getTalentId().toString() + v.getJobId().toString())) {
                        BigDecimal paymentAmount = new BigDecimal("0.00");
                        if (payMap.containsKey(v.getTalentId().toString())) {
                            paymentAmount = payMap.get(v.getTalentId().toString());
                        } else {
                            paymentAmount = payMap.get(v.getTalentId().toString() + v.getJobId());
                        }
                        BigDecimal value = v.getAmountReceivedTax().subtract(paymentAmount);
                        JSONObject object = new JSONObject();
                        object.put("talentName", v.getTalentName());
                        object.put("paymentAmount", value.toString());
                        objs.add(object);
                        unPaymentAmountList.add(value);
                        closeBean.setCloseAmount(value);
                    } else {
                        JSONObject object = new JSONObject();
                        object.put("talentName", v.getTalentName());
                        object.put("paymentAmount", v.getAmountReceivedTax().toString());
                        objs.add(object);
                        unPaymentAmountList.add(v.getAmountReceivedTax());
                        closeBean.setCloseAmount(v.getAmountReceivedTax());
                    }

                    v.setAmountReceivedTax(null);
                    v.setExpectedInvoicingAmount(null);
                    v.setExpectedInvoicingDate(null);
                    outstandingClosesList.add(closeBean);
                }
            });

            invoicingCandidateInfoRepository.saveAllAndFlush(candidateInfoList);
            log.info("[invoicing outstandingClose]: save invoicing candidate info info param:{}", JSONUtil.toJsonStr(candidateInfoList));

            String title = "回款-未结清关闭-全部回款（应付余额：0）";

            //修改开票状态
            List<InvoicingCandidateInfo> candidateList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdAndStatus(dto.getInvoicingId(), 1);
            BigDecimal amountReceivedTax = candidateList.stream()
                    .filter(x -> x.getAmountReceivedTax() != null)
                    .map(p -> p.getAmountReceivedTax())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (amountReceivedTax.compareTo(new BigDecimal("0.00")) == 0) {
                applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                applicationInfo.setAmountDue(BigDecimal.ZERO);
                invoicingApplicationInfoRepository.save(applicationInfo);
                log.info("[invoicing outstandingClose]: save invoicing application info info param:{}", JSONUtil.toJsonStr(applicationInfo));
            } else {
                BigDecimal payAmount = recordPaymentInfos.stream()
                        .map(InvoicingRecordPaymentInfo::getPaymentAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal unPayment = unPaymentAmountList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal amount = applicationInfo.getInvoicingAmount().subtract(payAmount).subtract(unPayment).subtract(closeAmount);
                title = "回款-未结清关闭（应付余额：￥" + amount + "）";
                if (amount.compareTo(new BigDecimal("0.00")) == 0) {
                    applicationInfo.setInvoicingStatus(InvoicingStatus.FULLY_COLLECTED);
                }
                applicationInfo.setAmountDue(amount);
                invoicingApplicationInfoRepository.save(applicationInfo);
                log.info("[invoicing outstandingClose]: save invoicing application info info param:{}", JSONUtil.toJsonStr(applicationInfo));
            }

            //add close record
            if (!outstandingClosesList.isEmpty()) {
                invoicingOutstandingCloseRepository.saveAll(outstandingClosesList);
                log.info("[invoicing outstandingClose]: save close  info info param:{}", JSONUtil.toJsonStr(outstandingClosesList));
            }

            //添加log
            final String subTitle = SecurityUtils.getUserName() + "于" +
                    DateUtil.fromInstantToDate(Instant.now(), DateUtil.CN_BJ_TIMEZONE, DateUtil.YYYY_MM_DD_HH_MM_SS) +
                    "进行未结清关闭";

            addLogInfo(title, subTitle, applicationInfo.getId(), null, JSONUtil.toJsonStr(objs), InvoicingLogStatus.PAYMENT, "outstandingClose", null, null, null);

            commonRedisService.delete(key);

            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                if (InvoicingStatus.FULLY_COLLECTED.equals(applicationInfo.getInvoicingStatus())) {
                    SecurityContextHolder.setContext(context);
                    notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(dto.getInvoicingId(), InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
                }
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInvoicingStatus() {
        List<InvoicingApplicationInfo> applicationInfoList = invoicingApplicationInfoRepository.findByInvoicingApplicationTypeInAndInvoicingStatusInAndStatus(Arrays.asList(1, 2), Arrays.asList(InvoicingStatus.INVOICED.toDbValue(), InvoicingStatus.UNCOLLECTED.toDbValue(), InvoicingStatus.PARTIALLY_COLLECTED.toDbValue()), 1);
        if (!applicationInfoList.isEmpty()) {
            List<Long> idList = applicationInfoList.stream().map(InvoicingApplicationInfo::getId).collect(Collectors.toList());
            invoicingApplicationInfoRepository.updateInvoicingStatusById(idList, InvoicingStatus.OVERDUE.toDbValue());
            log.info("[invoicing updateInvoicingStatus]: update invoicing status param:{}", JSONUtil.toJsonStr(idList));
        }
    }

    /**
     * 只执行一次 线上只有60条数据，没有考虑性能
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initTalentPayment() {
        List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findByStatus(1);
        List<InvoicingRecordPaymentDetail> detailList = new ArrayList<>();
        if (!paymentInfos.isEmpty()) {
            for (InvoicingRecordPaymentInfo record : paymentInfos) {
                if (record.getPaymentAmount().compareTo(new BigDecimal("0.00")) > 0 && StringUtils.isNotBlank(record.getCandidateJson())) {
                    JSONArray objArry = JSONUtil.parseArray(record.getCandidateJson());
                    for (int i = 0; i < objArry.size(); i++) {
                        JSONObject obj = objArry.getJSONObject(i);
                        InvoicingRecordPaymentDetail detail = new InvoicingRecordPaymentDetail();
                        detail.setTalentId(obj.getLong("talentId"));
                        detail.setTalentName(obj.getStr("talentName"));
                        detail.setGpAmount(obj.getBigDecimal("gpAmount"));
                        detail.setPaymentAmount(obj.getBigDecimal("paymentAmount"));
                        detail.setPaymentDate(record.getPaymentDate());
                        detail.setStatus(1);
                        detail.setPaymentId(record.getId());
                        detailList.add(detail);
                    }
                }
            }
        }

        if (!detailList.isEmpty()) {
            invoicingRecordPaymentDetailRepository.saveAll(detailList);
        }
    }

    /**
     * 只执行一次 线上只有60条数据，没有考虑性能
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initCandidateTax() {
        List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoRepository.findByStatus(1);
        if (!candidateInfoList.isEmpty()) {
            for (InvoicingCandidateInfo record : candidateInfoList) {
                if (null == record.getAmountReceivedTax()) {
                    Optional<InvoicingApplicationInfo> applicationInfo = invoicingApplicationInfoRepository.findById(record.getInvoiceApplicationId());
                    if (applicationInfo.isPresent()) {
                        InvoicingApplicationInfo vo = applicationInfo.get();
                        Double tax = 1.0;
                        if (TaxPayerType.CLIENT.equals(vo.getTaxPayerType())) {
                            tax = 1 + vo.getInvoicingTax() / 100;
                        }

                        BigDecimal amountReceivedTax = record.getAmountReceived().multiply(BigDecimal.valueOf(tax)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        record.setAmountReceivedTax(amountReceivedTax);
                    }
                }
            }
            invoicingCandidateInfoRepository.saveAll(candidateInfoList);
        }
    }

    @Override
    @Transactional
    public JSONArray deleteByStarts(Collection<Long> startIds) {
        JSONArray result = new JSONArray();
        List<InvoicingCandidateInfo> infoList = invoicingCandidateInfoRepository.findAllByStartIdIn(startIds);
        if (CollUtil.isEmpty(infoList)) {
            return result;
        }

        Map<Long, List<InvoicingCandidateInfo>> invoicingCandidateinfoMap = infoList.stream().collect(Collectors.groupingBy(InvoicingCandidateInfo::getInvoiceApplicationId));
        Set<Long> invoicingIds = invoicingCandidateinfoMap.keySet();
        List<InvoicingApplicationInfo> invoicingList = invoicingApplicationInfoRepository.findAllById(invoicingIds);
        invoicingList.forEach(i -> {
            JSONObject item = DtoToJsonUtil.toJsonWithColumnNames(i);
            item.put("WARN!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", "中国区发票未执行删除");
            result.add(item);
        });
        return result;
    }

    @Override
    @Scheduled(cron = "0 0 10 ? * MON", zone = "Asia/Shanghai")
    public void sendUninvoicedLarkNotification(){
        List<StartInfoForInvoicingVO> res = invoicingNativeRepository.getStartInfoForInvoicing();

        if (CollectionUtils.isEmpty(res)) {
            return;
        }

        Map<Long, List<StartInfoForInvoicingVO>> group = groupByAmId(res);

        Map<Long, List<StartInfoForInvoicingVO>> partnerGroup = new HashMap<>();

        for (Long userId: group.keySet()) {
            Long profitTeamLeader = userService.findProfitTeamLeaderIdByUserId(userId);
            List<StartInfoForInvoicingVO> vo = group.get(userId);

            String amEmail = userService.getUserEmail(userId);
            sendLarkMsgViaAnycross2(vo, amEmail, applicationProperties.getCreateFteChinaInvoiceUrl(), applicationProperties.getLarkServiceUninvoiceUrl());

            if (profitTeamLeader != null) {
//                String email = userService.getUserEmail(profitTeamLeader);
//                sendLarkMsgViaAnycross2(vo, email, applicationProperties.getCreateFteChinaInvoiceUrl(), applicationProperties.getLarkServiceUninvoiceUrl());
                partnerGroup.computeIfAbsent(profitTeamLeader, k -> new ArrayList<>()).addAll(vo);
            }
        }

        for (Long partnerUserId: partnerGroup.keySet()) {
            List<StartInfoForInvoicingVO> vo = partnerGroup.get(partnerUserId);
            String email = userService.getUserEmail(partnerUserId);
            sendLarkMsgViaAnycross2(vo, email, applicationProperties.getCreateFteChinaInvoiceUrl(), applicationProperties.getLarkServiceUninvoiceUrl());
        }

    }

    public Map<Long, List<StartInfoForInvoicingVO>> groupByAmId(List<StartInfoForInvoicingVO> vos) {
        Map<Long, List<StartInfoForInvoicingVO>> groupedMap = new HashMap<>();
        if (CollectionUtils.isEmpty(vos)) {
            return groupedMap;
        }

        for (StartInfoForInvoicingVO vo : vos) {
            if (CollectionUtils.isEmpty(vo.getAccountManagers())) {
                continue;
            }

            for (FullNameUserDTO accountManager : vo.getAccountManagers()) {
                Long amId = accountManager.getId();
                if (amId == null) {
                    continue;
                }
                groupedMap.computeIfAbsent(amId, k -> new ArrayList<>()).add(vo);
            }
        }

        return groupedMap;
    }

    private void sendLarkMsgViaAnycross2(List<StartInfoForInvoicingVO> vo, String email, String url, String larkLink) {
        String content = buildUninvoiceLarkJsonStr(vo, email, url);
        sendLarkMessageViaAnycross(content, larkLink);
    }

    private String buildUninvoiceLarkJsonStr(List<StartInfoForInvoicingVO> vo, String email, String url) {

        if (vo.size() == 1) {
            StartInfoForInvoicingVO v = vo.get(0);
            // 3. 构建 JSON 对象
            JSONObject root = new JSONObject();
            root.put("candidateName", v.getTalentName() + "(originalReceiverEmail:" + email + ")");
            root.put("candidateId", v.getTalentId());
            root.put("clientName", v.getClientName());
            root.put("clientId", v.getClientId());
            root.put("onboardDate", formatOnboardDate(v.getOnboardDate()));

            // 收件人邮箱
            root.put("remindType", "SINGLE");
            root.put("email", email);
            root.put("url", url);

            // 返回格式化后的 JSON 字符串
            return root.toStringPretty();
        } else {
            // 3. 构建 JSON 对象
            JSONObject root = new JSONObject();
            root.put("count", vo.size());

            // 收件人邮箱
            root.put("remindType", "MULTIPLE");
            root.put("email", email);
            root.put("url", url);

            // 返回格式化后的 JSON 字符串
            return root.toStringPretty();
        }
    }

    public String formatOnboardDate(Instant onboardDate) {
        if (onboardDate == null) {
            return null;
        }
        ZonedDateTime beijingTime = onboardDate.atZone(ZoneId.of("Asia/Shanghai"));
        return beijingTime.toLocalDate().toString();  // yyyy-MM-dd
    }

    @Override
    @Scheduled(cron = "0 0 11 ? * MON", zone = "Asia/Shanghai")
    public void sendOverdueInvoiceLarkNotification(){
        List<InvoicingApplicationInfoForOverdueVO> res = invoicingNativeRepository.getOverdueInvoicingApplications();

        if (CollectionUtils.isEmpty(res)) {
            return;
        }


        Map<Long, List<InvoicingApplicationInfoForOverdueVO>> group = res.stream()
                .filter(vo -> vo.getUserId() != null)  // 过滤掉没有 userId 的
                .collect(Collectors.groupingBy(InvoicingApplicationInfoForOverdueVO::getUserId));


        Map<Long, List<InvoicingApplicationInfoForOverdueVO>> partnerGroup = new HashMap<>();
        for (Long userId: group.keySet()) {
            Long profitTeamLeader = userService.findProfitTeamLeaderIdByUserId(userId);
            List<InvoicingApplicationInfoForOverdueVO> vo = group.get(userId);

            String amEmail = userService.getUserEmail(userId);
            sendLarkMsgViaAnycross3(vo, amEmail, applicationProperties.getFteChinaInvoiceListUrl(), applicationProperties.getLarkServiceOverdueInvoiceUrl());

            if (profitTeamLeader != null) {
//                String email = userService.getUserEmail(profitTeamLeader);
//                sendLarkMsgViaAnycross3(vo, email, applicationProperties.getFteChinaInvoiceListUrl(), applicationProperties.getLarkServiceOverdueInvoiceUrl());
                partnerGroup.computeIfAbsent(profitTeamLeader, k -> new ArrayList<>()).addAll(vo);
            }
        }

        for (Long partnerUserId: partnerGroup.keySet()) {
            List<InvoicingApplicationInfoForOverdueVO> vo = partnerGroup.get(partnerUserId);
            String email = userService.getUserEmail(partnerUserId);
            sendLarkMsgViaAnycross3(vo, email, applicationProperties.getFteChinaInvoiceListUrl(), applicationProperties.getLarkServiceOverdueInvoiceUrl());
        }

    }

    private void sendLarkMsgViaAnycross3(List<InvoicingApplicationInfoForOverdueVO> vo, String email, String url, String larkLink) {
        String content = buildOverdueInvoiceLarkJsonStr(vo, email, url);
        sendLarkMessageViaAnycross(content, larkLink);
    }

    private String buildOverdueInvoiceLarkJsonStr(List<InvoicingApplicationInfoForOverdueVO> vo, String email, String url) {

        if (vo.size() == 1) {
            InvoicingApplicationInfoForOverdueVO v = vo.get(0);
            // 3. 构建 JSON 对象
            JSONObject root = new JSONObject();
            root.put("candidateName", v.getTalentName() + "(originalReceiverEmail:" + email + ")");
            root.put("candidateId", v.getTalentId());
            root.put("clientName", v.getCompanyName());
            root.put("clientId", v.getCompanyId());
            root.put("overdueDay", v.getOverdueDay());
            root.put("dueAmount", v.getAmountDue());
            root.put("invoiceCodeNumber", v.getCodeNumber());

            // 收件人邮箱
            root.put("remindType", "SINGLE");
            root.put("email", email);
            root.put("url", url);

            // 返回格式化后的 JSON 字符串
            return root.toStringPretty();
        } else {
            // 3. 构建 JSON 对象
            JSONObject root = new JSONObject();
            root.put("count", vo.size());

            // 收件人邮箱
            root.put("remindType", "MULTIPLE");
            root.put("email", email);
            root.put("url", url);

            // 返回格式化后的 JSON 字符串
            return root.toStringPretty();
        }
    }

    /**
     * 补全支付记录中的jobId
     */
    @Override
    @Transactional
    public void completionPaymentJob() {
        List<InvoicingApplicationInfo> infos = invoicingApplicationInfoRepository.findByTenantId(14L);
        List<Long> applicationIds = infos.stream().map(InvoicingApplicationInfo::getId).toList();
        List<InvoicingRecordPaymentInfo> paymentInfos = invoicingRecordPaymentInfoRepository.findByInvoicingIdInAndStatus(applicationIds,1);

        List<Long> paymentIds = paymentInfos.stream().map(InvoicingRecordPaymentInfo::getId).toList();
        List<InvoicingRecordPaymentDetail> details = invoicingRecordPaymentDetailRepository.findByPaymentIdInAndStatus(paymentIds, 1);
        Map<Long,List<InvoicingRecordPaymentDetail>> detailMap = details.stream().collect(Collectors.groupingBy(InvoicingRecordPaymentDetail::getPaymentId));

        List<InvoicingCandidateInfo> candidateInfoList = invoicingCandidateInfoRepository.findByInvoiceApplicationIdInAndStatus(applicationIds,1);
        Map<Long,List<InvoicingCandidateInfo>> candidateInfoMap = candidateInfoList.stream().collect(Collectors.groupingBy(InvoicingCandidateInfo::getInvoiceApplicationId));
        for (InvoicingRecordPaymentInfo paymentInfo : paymentInfos) {
            List<InvoicingCandidateInfo> candidateInfos = candidateInfoMap.get(paymentInfo.getInvoicingId());
            if (CollUtil.isEmpty(candidateInfos)) {
                continue;
            }
            Map<Long, Long> talentJobMap = candidateInfos.stream().collect(Collectors.toMap(x -> x.getTalentId(), x -> x.getJobId()));

            if (detailMap.containsKey(paymentInfo.getId())) {
                List<InvoicingRecordPaymentDetail> detailList = detailMap.get(paymentInfo.getId());
                detailList.forEach(x -> {
                    if (null != x.getJobId()) {
                        return;
                    }
                    if (talentJobMap.containsKey(x.getTalentId())) {
                        x.setJobId(talentJobMap.get(x.getTalentId()));
                    }
                });
            }

            if (StringUtils.isNotBlank(paymentInfo.getCandidateJson())) {
                JSONArray objArry = JSONUtil.parseArray(paymentInfo.getCandidateJson());
                for (int i = 0; i < objArry.size(); i++) {
                    JSONObject obj = objArry.getJSONObject(i);
                    if (obj.containsKey("jobId")) {
                        continue;
                    }
                    Long talentId = obj.getLong("talentId");
                    if (talentJobMap.containsKey(talentId)) {
                        obj.put("jobId", talentJobMap.get(talentId));
                    }
                }
                paymentInfo.setCandidateJson(JSONUtil.toJsonStr(objArry));
            }
        }

        invoicingRecordPaymentInfoRepository.saveAll(paymentInfos);
        invoicingRecordPaymentDetailRepository.saveAll(details);
    }
}
