package com.altomni.apn.agency.dto;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.talent.CreationTalentType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentInfoInputWithJobIdForApplicationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    // job info
    @ApiModelProperty(value = "The job id that this talent wants to apply. This is for application using")
    private Long jobId;

    //talent info
    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    private Long tenantId;

    @ApiModelProperty(value = "first name. required in US.")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.")
    private String lastName;

    @ApiModelProperty(value = "nick first name.")
    private String nickFirstName;

    @ApiModelProperty(value = "nick last name.")
    private String nickLastName;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
            "the full name can not be null.")
    private String fullName;

    @ApiModelProperty(value = "Talent gender enum name.")
    private String gender;
    //种族/族裔
    private List<String> ethnicity;
    //偏好的人称代词
    private String preferredPronoun;
    //是否为残障人士
    private String disability;
    //退伍军人身份
    private String veteran;
    //是否为 LGBTQ+ 群体
    private String memberOfLGBTQ;

    @ApiModelProperty(value = "only use in jobDiva MyProfile, zipCode")
    private String zipCode;

    @ApiModelProperty(value = "Talent birthday(yyyy-MM-dd).")
    private String birthdate;

    @ApiModelProperty(value = "Talent birthday(MM-dd).|| 暂时兼容yyyy-MM-dd")
    private String birthday;

    @ApiModelProperty(value = "Talent job motivation enum id.")
    private Integer motivationId;

    @ApiModelProperty(value = "url link to photo")
    private String photoUrl;

    @ApiModelProperty(value = "talent creation types", allowableValues = "UPLOAD_WITH_RESUME, CREATE_WITHOUT_RESUME, BULK_UPLOAD_RESUMES")
    private CreationTalentType creationTalentType;

    @ApiModelProperty(value = "id for the company location")
    private Long companyLocationId;

    @ApiModelProperty(value = "Talent current location. Save to talent basic information table.")
    private LocationDTO currentLocation;

//    @ApiModelProperty(value = "Talent preferred locations to work. May include the current location.")
//    private List<LocationDTO> preferredLocations;
//
//    private List<String> preferredTitle;

    private List<TalentPreference> preferences;

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> skills;

    /**
     * Additional Info
     */
    @ApiModelProperty(value = "Talent expected salary currency")
    private String currency;

    private Double payTimes;

    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "Talent current salary range")
    private RangeDTO salaryRange;

//    @ApiModelProperty(value = "salary rate unit")
//    private Object preferredPayType;
//
//    @ApiModelProperty(value = "preferred salary range")
//    private Object preferredSalaryRange;
//
//    @ApiModelProperty(value = "Talent preferred salary currency")
//    private Object preferredCurrency;
//
//    private Object preferredPayTimes;

    @ApiModelProperty(value = "sourcing channel")
    private ResumeSourceType source;

    @ApiModelProperty(value = "salary range only use in common pool")
    private RangeDTO annualSalaryInUSD;

//    @ApiModelProperty(value = "preferred salary range only use in common pool")
//    private RangeDTO preferredAnnualSalaryInUSD;

    @ApiModelProperty(value = "work authorization enum name list")
    private List<String> workAuthorization;

    @ApiModelProperty(value = "job functions enum id list, load data in biz_dict service")
    private List<String> jobFunctions;

    @ApiModelProperty(value = "languages enum name list, load data in biz_dict service")
    private List<String> languages;

    @ApiModelProperty(value = "industries enum id list. talent specified in same type")
    private List<String> industries;

    @ApiModelProperty(value = "list of talent contacts. Show up when fetch a single talent profile. Read Only. Create/update using the TalentContact entity.")
    private List<TalentContactDTO> contacts;

    @ApiModelProperty(value = "list of talent experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentExperienceDTO> experiences;

    @ApiModelProperty(value = "list of talent educations. Show up when fetch a single talent profile. Read Only. Create/update using the TalentEducationDTO entity.")
    private List<TalentEducationDTO> educations;

    @ApiModelProperty(value = "list of talent project experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentProjectDTO> projects;

    @ApiModelProperty(value = "The currently specified parse resume uuid.")
    private String parseResumeUuid;

    @ApiModelProperty(value = "list of talent resumes. Show up when fetch a single talent profile. Read Only. Create/update using the TalentResume entity.")
    private List<TalentResumeDTO> resumes;

    @ApiModelProperty(value = "The person who have ownership for this talent will get commission when this talent on board")
    private List<TalentOwnershipDTO> ownerships;

    @ApiModelProperty(value = "list of talent certificate")
    private List<TalentCertificateDTO> certificates;

    @ApiModelProperty(value = "Talent folder id")
    private Long folderId;

    //自我评价
    private String selfEvaluation;

    //apn pro更新使用
    private List<TalentNoteDTO> notes;
    //apn pro是否根据传入信息生成简历
    private boolean generateResume = false;

    /**
     * This should only happen for English
     * Chinese should just set full name, ignore the first & last name
     */
    public void setFullName() {
        if (this.firstName == null && this.lastName == null) {
            throw new CustomParameterizedException("Either full name or first name and last name are required");
        }
        if(this.firstName == null) {
            this.fullName = this.lastName;
        } else if(this.lastName == null) {
            this.fullName = this.firstName;
        } else {
            this.fullName = CommonUtils.formatFullName(firstName, lastName);
        }
    }

    public String toSimpleString() {
        return String.format(
                "TalentInfo(id=%s, jobId=%s, tenantId=%s, firstName=%s, lastName=%s, contacts=[%s], resumes=[%s], parseResumeUuid=%s)",
                format(id),
                format(jobId),
                format(tenantId),
                format(firstName),
                format(lastName),
                format(contacts),
                format(resumes),
                format(parseResumeUuid)
        );
    }

    private String format(Object obj) {
        if (obj == null) {
            return "N/A";
        }

        // 如果是 List 类型，遍历调用 toString()
        if (obj instanceof List<?>) {
            List<?> list = (List<?>) obj;
            if (list.isEmpty()) {
                return "N/A";
            }
            return list.stream()
                    .map(Object::toString)  // 直接调用 toString()
                    .collect(Collectors.joining(" | "));
        }

        // 其他情况（包括 String, Long, DTO 等）直接 toString()
        return obj.toString();
    }
}
