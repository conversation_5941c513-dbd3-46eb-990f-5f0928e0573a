package com.altomni.apn.jobdiva.service.assignment.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessNodeVO;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.job.domain.enumeration.start.StartStatus;
import com.altomni.apn.job.service.dto.start.StartDTO;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.assignment.*;
import com.altomni.apn.jobdiva.domain.invoice.TContractorInvoice;
import com.altomni.apn.jobdiva.domain.timesheet.*;
import com.altomni.apn.jobdiva.repository.assignment.*;
import com.altomni.apn.jobdiva.repository.invoice.ContractorInvoiceRepository;
import com.altomni.apn.jobdiva.repository.timesheet.*;
import com.altomni.apn.jobdiva.service.application.ApplicationService;
import com.altomni.apn.jobdiva.service.assignment.AssignmentActivityService;
import com.altomni.apn.jobdiva.service.assignment.AssignmentService;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.company.CompanyService;
import com.altomni.apn.jobdiva.service.dto.assignment.*;
import com.altomni.apn.jobdiva.service.dto.timesheet.TimeSheetInfoDTO;
import com.altomni.apn.jobdiva.service.finance.FinanceService;
import com.altomni.apn.jobdiva.service.invoice.ContractorInvoiceService;
import com.altomni.apn.jobdiva.service.job.JobService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.vo.assignment.*;
import com.altomni.apn.jobdiva.util.OtDtUtil;
import com.altomni.apn.jobdiva.util.RateUnitUtil;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.company.constants.Constants.SYSTEM_ACCOUNT;

/**
 * assignment impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service("assignmentService")
public class AssignmentServiceImpl implements AssignmentService {

    @Resource
    private EntityManager entityManager;

    @Resource
    private PayInfoRepository payInfoRepository;

    @Resource
    private PayRateRepository payRateRepository;

    @Resource
    private BillInfoRepository billInfoRepository;

    @Resource
    private TimeSheetRepository timeSheetRepository;

    @Resource
    private TimeSheetManagerRepository managerRepository;

    @Resource
    private AssignmentLocationRepository locationRepository;

    @Resource
    private TalentAssignmentRepository assignmentRepository;

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @Resource
    private CalculateAndStateRepository calculateAndStateRepository;

    @Resource
    private AssignmentContributionRepository contributionRepository;

    @Resource
    private ApproveRecordRepository approveRecordRepository;

    @Resource
    private TimeSheetBreakTimeRepository breakTimeRepository;

    @Resource
    private TimeSheetCommentsRepository commentsRepository;

    @Resource
    private TimeSheetHolidayRecordRepository timeSheetHolidayRecordRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    FinanceService financeService;

    @Resource
    ApplicationService applicationService;

    @Resource
    private TalentService talentService;

    @Resource
    private CompanyService companyService;

    @Resource
    private JobService jobService;

    @Resource
    private AssignmentActivityService assignmentActivityService;

    @Resource
    private ContractorInvoiceRepository contractorInvoiceRepository;

    @Resource
    private AssignmentSyncToHrService assignmentSyncToHrService;

    @Resource
    private TalentAssignmentRepository talentAssignmentRepository;

    @Resource
    private TimeSheetWeekEndingRecordRepository timeSheetWeekEndingRecordRepository;

    @Resource
    private TimeSheetBreakTimeRepository timeSheetBreakTimeRepository;

    @Resource
    private ContractorInvoiceService contractorInvoiceService;

    @Resource
    private AssignmentServiceImpl self;

    private static final Map<TimeSheetStatus, Integer> STATUS_PRIORITY_MAP = new HashMap<>();

    static {
        STATUS_PRIORITY_MAP.put(TimeSheetStatus.REJECTED, 4);
        STATUS_PRIORITY_MAP.put(TimeSheetStatus.DRAFT, 3);
        STATUS_PRIORITY_MAP.put(TimeSheetStatus.MISSING, 2);
        STATUS_PRIORITY_MAP.put(TimeSheetStatus.APPLIED_APPROVE, 1);
        STATUS_PRIORITY_MAP.put(TimeSheetStatus.APPROVED, 0);
        STATUS_PRIORITY_MAP.put(TimeSheetStatus.NO_RECORD, -1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(AssignmentDetailInfoDTO dto) {
        checkStartStatus(dto.getTalentRecruitmentProcessId());
        return doSave(dto, null);
    }

    private void checkStartStatus(Long talentRecruitmentProcessId) {
        TalentRecruitmentProcessNodeVO vo = applicationService.getNodeByTalentRecruitmentProcessNodeIdAndNodeType(talentRecruitmentProcessId, NodeType.ON_BOARD).getBody();
        if (vo != null && vo.getNodeStatus().equals(NodeStatus.ELIMINATED)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVE_UPDATE_RECRUITMENT_PROCESS_INACTIVE.getKey(), CommonUtils.USER_LANGUAGE_SET.concat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
    }

    public Long doSave(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo) {
        Long id = saveAssignment(dto);
        dto.setId(id);
        saveBillInfo(dto);
        saveContribution(dto);
        savePayInfo(dto);
        savePayRate(dto);
        saveTimeSheet(dto);
        saveLocation(dto);
        if (dto.getStatus() == AssignmentStatusType.APPROVED) {
            TimeSheetUtil.setTimeSheetStatus(TimeSheetUserType.TALENT, dto.getTalentId(), SecurityUtils.getTenantId(), id, dto.getTimeSheet().getFrequency());
        }
        //标记同步hr
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            talentService.updateTalentNeedSyncToHr(dto.getTalentId());
            companyService.updateCompanyNeedSyncToHr(dto.getCompanyId());
            jobService.updateJobNeedSyncToHr(dto.getJobId());
        });
        assignmentSyncToHrService.buildAssignmentListSyncToHrMq(List.of(dto.getId()));
        return id;
    }

    private void checkInvoiceExist(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo) {

        List<WeekDataVO> newWeekDataVOList = TimeSheetUtil.getWeekEnding(dto.getStartDate(), dto.getEndDate(), dto.getTimeSheet().getWeekEnding().toDbValue(), vo.getIsWeekEnd(), dto.getTimeSheet().getFrequency());

        List<LocalDate> newWeekEndingDate = newWeekDataVOList.stream().map(WeekDataVO::getWeekEnd).distinct().collect(Collectors.toList());
        List<String> newWeekEndingDateStr = newWeekEndingDate.stream().map(v -> v.toString()).distinct().collect(Collectors.toList());

        List<TimeSheetRecord> timeSheetRecords = timeSheetRecordRepository.findAllByAssignmentId(dto.getId());
        List<LocalDate> weekEndingDate = timeSheetRecords.stream().map(TimeSheetRecord::getWeekEnd).distinct().collect(Collectors.toList());
        if (null != weekEndingDate && !weekEndingDate.isEmpty()) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            List<TContractorInvoice> invoiceCounts = contractorInvoiceRepository.searchNoVoidInvoiceCountByAssignmentIdAndWeekEndingDate(dto.getId(), weekEndingDate);
            if (null != invoiceCounts && !invoiceCounts.isEmpty()) {
                List<String> existWeekending = invoiceCounts.stream().map(v -> formatter.format(v.getWeekEndingDate())).distinct().collect(Collectors.toList());
                List<String> existParam = new ArrayList<>();
                for (String weekend : existWeekending) {
                    if (!newWeekEndingDateStr.contains(weekend)) {
                        existParam.add(weekend);
                    }
                }
                if (!existParam.isEmpty()) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DATE_CHECK_INCLUDE_INVOICE.getKey(),
                            CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),
                            Collections.singletonList(existParam),
                            jobdivaApiPromptProperties.getJobdivaService()));
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(AssignmentDetailInfoDTO dto) {
        //checkStartStatus(dto.getStartId());
        if (dto.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DETAIL_UPDATE_ID_NULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (dto.getStartDate().compareTo(dto.getEndDate()) > 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_START_DATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        AssignmentGeneralInfoVO vo = assignmentRepository.findGeneralInfoById(dto.getId());
        if (vo == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DETAIL_UPDATE_ID_NOT_CORRECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        checkInvoiceExist(dto,vo);
        checkAssignmentAlreadyStarted(dto, vo);
        AssignmentBillInfo billInfo = billInfoRepository.findByAssignmentId(dto.getId());
        //Timesheet Entry Format
        if (ObjectUtil.isNotNull(dto.getTimeSheet()) && ObjectUtil.isNotNull(dto.getTimeSheet().getTimeSheetType()) && !vo.getIsClockIn()) {
            timeSheetRecordRepository.updateTimeSheetTypeByAssignmentIdAndTalentId(dto.getTalentId(), dto.getId(), dto.getTimeSheet().getTimeSheetType().toDbValue());
        }
        List<Integer> statusList = CollUtil.newArrayList(TimeSheetStatus.DRAFT.toDbValue(), TimeSheetStatus.APPROVED.toDbValue(), TimeSheetStatus.REJECTED.toDbValue(), TimeSheetStatus.APPLIED_APPROVE.toDbValue());
        if (dto.getStatus() == AssignmentStatusType.APPROVED) {
            AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(vo.getId());
            startDateAndEndDateChangHandler(dto, vo, assignmentTimeSheet);
            //if OverTimeType changed, clear timeSheet data and change TimeSheetStatus to MISSING/NO_RECORD
            if (ObjectUtil.isNotNull(billInfo) && !dto.getBillInfo().getOvertimeType().equals(billInfo.getOvertimeType())) {
                changeOverTimeType(dto, vo);
            }
            //有打卡记录时,根据 frequency 去修改weekend
            if (dto.getTimeSheet().getFrequency() != assignmentTimeSheet.getFrequency()) {
                dto.setFrequencyChanged(true);
                changeTimeSheetRecordByFrequency(dto.getId(), dto.getStartDate(), dto.getEndDate(), dto.getTimeSheet().getWeekEnding().toDbValue(), dto.getTimeSheet().getFrequency());
            }
            //没有打卡记录时
            if (!vo.getIsClockIn()) {
                //判断是否没有报销记录
                Integer count = expenseRecordRepository.countByAssignmentId(vo.getId());
                if (count <= 0) {
                    // weekending 发生变化 清理 生成的记录重新生成
                    if (dto.getTimeSheet().getWeekEnding() != assignmentTimeSheet.getWeekEnding() || dto.getTimeSheet().getFrequency() != assignmentTimeSheet.getFrequency()) {
                        timeSheetRecordRepository.deleteByAssignmentId(dto.getId());
                    }
                }
            }
        } else {
            //非approved的数据需要删除掉, 需要先判断是否有打卡和报销记录,防止直接通过接口修改assignment状态导致数据被删除
            if (timeSheetRecordRepository.countByStatusInAndTalentIdAndAssignmentId(statusList, dto.getTalentId(), dto.getId()) > 0
                    || expenseRecordRepository.countByAssignmentId(vo.getId()) > 0) {
                //存在打卡记录或者报销记录,则抛出异常
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_UPDATE_ALREADY_STARTED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            timeSheetRecordRepository.deleteByAssignmentId(dto.getId());
        }
        doSave(dto, vo);
        return dto.getId();
    }

//    @Override
//    public void historyDataProcess() {
//        Long userId = SecurityUtils.getUserId();
//        SecurityContext context = SecurityContextHolder.getContext();
//        SecurityContextHolder.setContext(context);
//        CompletableFuture.runAsync(() -> {
//            SecurityContextHolder.setContext(context);
//            log.info("[APN @{}] time sheet record handler history data weekend is start ", userId);
//            int page = 0;
//            int size = 1000;
//            long total = assignmentRepository.count();
//            AtomicInteger current = new AtomicInteger(0);
//            while (true) {
//                int offset = page * size;
//                log.info("[APN @{}] time sheet record handler history data weekend page = {}, total = {}, current = {}", userId, page, total, current.get());
//                List<Long> assignmentIdList = assignmentRepository.findAllOrderByIdDesc(offset, size);
//                if (CollUtil.isEmpty(assignmentIdList)) {
//                    log.info("[APN @{}] time sheet record is finish ", userId);
//                    break;
//                }
//                assignmentIdList.parallelStream().forEach(assignmentId -> {
//                    try {
//                        SecurityContextHolder.setContext(context);
//                        log.info("[APN @{}] current = {}, assignmentId = {}", userId, current.incrementAndGet(), assignmentId);
//                        AssignmentVO assignmentVO = assignmentRepository.findAssigmentInfoByAssignmentId(assignmentId);
//                        if (assignmentVO != null) {
//                            List<LocalDate> localDateList = new ArrayList<>();
//                            localDateList.add(assignmentVO.getStartDate());
//                            localDateList.add(assignmentVO.getEndDate());
//                            // 查询出所有合同拥有的数据做补全的基础
//                            List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByAssignmentId(assignmentId);
//                            List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByAssignmentId(assignmentId);
//                            Map<LocalDate, List<ExpenseRecord>> expenseRecordMap = expenseRecordList.stream().collect(Collectors.groupingBy(ExpenseRecord::getWorkDate));
//                            Map<LocalDate, TimeSheetRecord> timeSheetRecordMap = timeSheetRecordList.stream().collect(Collectors.toMap(TimeSheetRecord::getWorkDate, a -> a, (a1, a2) -> a1));
//                            if (CollUtil.isNotEmpty(timeSheetRecordList)) {
//                                localDateList.addAll(timeSheetRecordList.stream().map(TimeSheetRecord::getWorkDate).toList());
//                            }
//                            if (CollUtil.isNotEmpty(expenseRecordList)) {
//                                localDateList.addAll(expenseRecordList.stream().map(ExpenseRecord::getWorkDate).toList());
//                            }
//                            localDateList.sort(Collections.reverseOrder());
//                            LocalDate endDate = localDateList.get(0);
//                            LocalDate startDate = localDateList.get(localDateList.size() - 1);
//                            //历史数据不错迁移, 只做填充, 补全新增的字段 weekStart, weekEnd 等
//                            List<WeekDataVO> weekDataVOList = TimeSheetUtil.getWeekEnding(startDate, endDate, assignmentVO.getWeekEnding(), assignmentVO.getIsWeekEnd(), TimeSheetFrequencyType.WEEKLY);
//                            weekDataVOList.forEach(weekDataVO -> {
//                                //补全 timesheet
//                                timeSheetRecordUpdateHistoryByWeekEnd(weekDataVO, assignmentVO, timeSheetRecordMap);
//                                //补全 expense
//                                timeSheetExpenseUpdateByWeekEnd(weekDataVO, assignmentVO, expenseRecordMap);
//                            });
//                        }
//                    } catch (Exception e) {
//                        log.error("update time sheet week end is error = {}", ExceptionUtils.getStackTrace(e));
//                    }
//                });
//                page++;
//            }
//            log.info("[APN @{}] time sheet record handler history data weekend is success ", userId);
//        });
//    }

    /**
     * 修改timesheet 打卡信息 by frequency
     * @param assignmentId
     * @param startDate
     * @param endDate
     * @param weekending
     * @param newFrequency
     */
    private void changeTimeSheetRecordByFrequency(Long assignmentId, LocalDate startDate, LocalDate endDate, Integer weekending, TimeSheetFrequencyType newFrequency) {
        //内存中有对数据做修改,如果不清理则会出现数据错乱的问题
        entityManager.clear();
        //获取新的 week data 列表
        AssignmentVO assignmentVO = assignmentRepository.findAssigmentInfoByAssignmentId(assignmentId);
        List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByAssignmentId(assignmentId);
        if (CollUtil.isEmpty(timeSheetRecordList)) {
            return;
        }
        Map<LocalDate, TimeSheetRecord> timeSheetRecordMap = timeSheetRecordList.stream().collect(Collectors.toMap(TimeSheetRecord::getWorkDate, a -> a, (a1, a2) -> a1));
        List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByAssignmentId(assignmentId);
        Integer expenseMax = expenseRecordList.stream().max(Comparator.comparingInt(ExpenseRecord::getExpenseIndex)).orElse(new ExpenseRecord()).getExpenseIndex();
        Map<String, List<ExpenseRecord>> expenseRecordIndexMap = expenseRecordList.stream().collect(Collectors.groupingBy(a -> a.getWorkDate() + "-" + a.getExpenseIndex()));
        //获取所有的时间数据
        List<LocalDate> localDateList = new ArrayList<>();
        localDateList.add(startDate);
        localDateList.add(endDate);
        if (CollUtil.isNotEmpty(timeSheetRecordList)) {
            localDateList.addAll(timeSheetRecordList.stream().map(TimeSheetRecord::getWorkDate).toList());
        }
        if (CollUtil.isNotEmpty(expenseRecordList)) {
            localDateList.addAll(expenseRecordList.stream().map(ExpenseRecord::getWorkDate).toList());
        }
        //排序后获取真正的开始和结束时间
        localDateList.sort(Collections.reverseOrder());
        TimeSheetRecord tempRecord = timeSheetRecordList.get(0);
        //获取最大范围的合同记录
        List<WeekDataVO> weekDataVOList = TimeSheetUtil.getWeekEnding(localDateList.get(localDateList.size() - 1), localDateList.get(0), weekending, 1, newFrequency);
        log.info("[APN] time sheet record weekend assignmentId = {}, talentId = {}", tempRecord.getAssignmentId(), tempRecord.getTalentId());
        Map<Long, ApproveRecord> approveMap = new HashMap<>();
        Map<Long, ApproveRecord> approveExpenseMap = new HashMap<>();
        Map<LocalDate, TimeSheetComments> commentMap = new HashMap<>();
        Map<String, TimeSheetComments> commentExpenseMap = new HashMap<>();
        weekDataVOList.forEach(weekDataVO -> {
            //存在历史数据,需要修改,重新写入 week_start, week_end
            timeSheetRecordUpdateByWeekEnd(weekDataVO, assignmentVO, timeSheetRecordMap, approveMap, commentMap);
            timeSheetExpenseUpdateByWeekEnd(weekDataVO, assignmentVO, expenseRecordIndexMap, approveExpenseMap, commentExpenseMap, expenseMax);
        });
        //修改完成后需要处理创建人和修改人
        List<WeekDataVO> assignmentWeekDataVoList = TimeSheetUtil.getWeekEnding(startDate, endDate, weekending, 1, newFrequency);
        WeekDataVO lastWeekDate = assignmentWeekDataVoList.get(assignmentWeekDataVoList.size() - 1);
        //查看是否有超出合同期外的打卡数据,如果有则不能删除
        List<TimeSheetRecord> lastTimeSheetList = timeSheetRecordRepository.findAllByEndDate(lastWeekDate.getWeekendingDate().plusDays(1), assignmentVO.getTalentId(), assignmentId);
        List<ExpenseRecord> lastExpenseList = expenseRecordRepository.findAllByEndDate(lastWeekDate.getWeekendingDate().plusDays(1), assignmentVO.getTalentId(), assignmentId);
        if (CollUtil.isEmpty(lastTimeSheetList) && CollUtil.isEmpty(lastExpenseList)) {
            //数据是jobdiva 迁移过来的有超过范围的打卡记录
            List<TimeSheetRecord> timeSheetRecords = timeSheetRecordRepository.findAllByDate(lastWeekDate.getWorkDate().plusDays(1), lastWeekDate.getWeekendingDate(), assignmentVO.getTalentId(), assignmentId);
            if (CollUtil.isNotEmpty(timeSheetRecords)) {
                timeSheetRecordRepository.deleteByDate(lastWeekDate.getWorkDate().plusDays(1), lastWeekDate.getWeekendingDate(), assignmentVO.getTalentId(), assignmentId);
                commentsRepository.deleteComment(lastWeekDate.getWeekendingDate(), CommentsType.TIME_SHEET.toDbValue(), assignmentVO.getTalentId(), assignmentId);
                approveRecordRepository.deleteAllByRecordIdInAndType(timeSheetRecords.stream().map(TimeSheetRecord::getId).toList(), CommentsType.TIME_SHEET.toDbValue());
            }
            List<ExpenseRecord> expenseRecords = expenseRecordRepository.findAllByDate(lastWeekDate.getWorkDate().plusDays(1), lastWeekDate.getWeekendingDate(), assignmentVO.getTalentId(), assignmentId);
            if (CollUtil.isNotEmpty(expenseRecords)) {
                expenseRecordRepository.deleteByDate(lastWeekDate.getWorkDate().plusDays(1), lastWeekDate.getWeekendingDate(), assignmentVO.getTalentId(), assignmentId);
                commentsRepository.deleteCommentEndDate(lastWeekDate.getWeekendingDate(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getTalentId(), assignmentId);
                approveRecordRepository.deleteAllByRecordIdInAndType(expenseRecords.stream().map(ExpenseRecord::getId).toList(), CommentsType.EXPENSE.toDbValue());
            }
        }
        timeSheetRecordRepository.updateCreateByAndLastModifiedByByAssignmentId(assignmentId, SYSTEM_ACCOUNT);
    }

    private void timeSheetExpenseUpdateByWeekEnd(WeekDataVO weekDataVO, AssignmentVO assignmentVO, Map<String, List<ExpenseRecord>> expenseRecordIndexMap, Map<Long, ApproveRecord> approveExpenseMap,
                                                 Map<String, TimeSheetComments> commentExpenseMap, Integer maxExpenseIndex) {
        //历史存在数据,则需要修改,没有这不处理,不需要预生成数据
        for (int currentIndex = 0; currentIndex <= maxExpenseIndex; currentIndex++) {
            if (expenseRecordIndexMap.containsKey(weekDataVO.getWorkDate() + "-" + currentIndex)) {
                Integer expenseIndex = currentIndex;
                List<ExpenseRecord> recordList = expenseRecordIndexMap.get(weekDataVO.getWorkDate() + "-" + currentIndex);
                ExpenseRecord oldExpenseRecord = recordList.get(0);
                if (oldExpenseRecord.getWeekEnd() != null
                        && oldExpenseRecord.getWeekEnd().isEqual(weekDataVO.getWeekEnd())
                        && oldExpenseRecord.getWeekStart().isEqual(weekDataVO.getWeekStart())) {
                    return;
                }
                LocalDate oldWeekend = oldExpenseRecord.getWeekEnd();
                LocalDate oldWeekStart = oldExpenseRecord.getWeekStart();
                List<Long> oldIdList = recordList.stream().map(ExpenseRecord::getId).toList();
                recordList = recordList.stream().map(expenseRecord -> {
                    ExpenseRecord newExpenseRecord = new ExpenseRecord();
                    BeanUtil.copyProperties(expenseRecord, newExpenseRecord, "id");
                    newExpenseRecord.setWeekStart(weekDataVO.getWeekStart());
                    newExpenseRecord.setWeekEnd(weekDataVO.getWeekEnd());
                    newExpenseRecord.setWeekEndingDate(weekDataVO.getWeekendingDate());
                    return newExpenseRecord;
                }).toList();
                if (ObjectUtil.isNotNull(oldWeekend) && !oldWeekend.isEqual(weekDataVO.getWeekEnd())) {
                    //oldWeekEnd 不等于 新的 weekend 表示新增的切割数据,需要先删除在新增, 使用trigger 来完成
                    expenseRecordRepository.deleteByIdList(oldIdList);
                    if (weekDataVO.getWorkDate().isEqual(weekDataVO.getWeekEnd())) {
                        if(checkWeekCost(weekDataVO, assignmentVO, expenseIndex)) {
                            return;
                        }
                        List<ExpenseRecord> expenseRecords = expenseRecordIndexMap.get(weekDataVO.getWeekendingDate() + "-" + currentIndex);
                        Long oldApprovedId = oldIdList.stream().min(Long::compareTo).orElse(0L);
                        if (CollUtil.isNotEmpty(expenseRecords)) {
                            //这里是新增的切割时间weekend, 存在weekendingDate 的数据的时候, 需要复制过来
                            ExpenseRecord expenseRecord = expenseRecords.get(0);
                            oldWeekend = expenseRecord.getWeekEnd();
                            oldApprovedId = expenseRecords.stream().map(ExpenseRecord::getId).min(Long::compareTo).orElse(0L);
                            recordList.forEach(expense -> {
                                expense.setStatus(expenseRecord.getStatus());
                                expense.setSubmittedDate(expenseRecord.getSubmittedDate());
                            });
                        }
                        expenseRecordRepository.saveAll(recordList);
                        //comment 处理
                        TimeSheetComments timeSheetComments = commentsRepository.findAllByDateAndType(oldWeekend, assignmentVO.getTalentId(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getId(), expenseIndex);
                        if (timeSheetComments != null) {
                            commentsRepository.deleteComment(weekDataVO.getWeekEnd(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getTalentId(), assignmentVO.getId(), expenseIndex);
                            TimeSheetComments newComments = new TimeSheetComments();
                            BeanUtil.copyProperties(timeSheetComments, newComments, "id", "workDate");
                            newComments.setWorkDate(weekDataVO.getWeekEnd());
                            commentsRepository.save(newComments);
                        }
                        //approve 处理
                        ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(oldApprovedId, CommentsType.EXPENSE);
                        if (ObjectUtil.isNotEmpty(approveRecord)) {
                            ApproveRecord newApproveRecord = new ApproveRecord();
                            BeanUtil.copyProperties(approveRecord, newApproveRecord, "id", "weekEnd", "recordId");
                            newApproveRecord.setRecordId(recordList.stream().mapToLong(ExpenseRecord::getId).min().orElse(0));
                            newApproveRecord.setWeekEnd(weekDataVO.getWeekEnd());
                            approveRecordRepository.save(newApproveRecord);
                            approveRecordRepository.updateCreateAndModifiedById(newApproveRecord.getId(), approveRecord.getCreatedBy(), approveRecord.getLastModifiedBy(), approveRecord.getCreatedDate(), approveRecord.getLastModifiedDate());
                        }
                        //聚合表数据补全提交时间和审批数据
                        expenseRecordRepository.updateLastModifiedDateById(recordList.stream().map(ExpenseRecord::getId).toList(), Instant.now());
                    } else {
                        List<ExpenseRecord> expenseRecords = expenseRecordIndexMap.get(weekDataVO.getWeekEnd() + "-" + currentIndex);
                        //这个地方表示 oldWeekEnd 和 newWeekEnd 数据不一致,为删除WeekEnd 同时涉及到数据合并
                        expenseRecordRepository.deleteByIdList(oldIdList);
                        if (weekDataVO.getWorkDate().isEqual(oldWeekend)) {
                            TimeSheetComments comments = commentsRepository.findAllByDateAndType(oldWeekend, assignmentVO.getTalentId(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getId(), expenseIndex);
                            if (comments != null) {
                                commentExpenseMap.put(comments.getWorkDate().toString() + expenseIndex, comments);
                            }
                            commentsRepository.deleteComment(oldWeekend, CommentsType.EXPENSE.toDbValue(), oldExpenseRecord.getTalentId(), oldExpenseRecord.getAssignmentId(), expenseIndex);
                            ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(oldIdList.stream().min(Long::compareTo).orElse(0L), CommentsType.EXPENSE);
                            if (approveRecord != null) {
                                approveExpenseMap.put(approveRecord.getRecordId(), approveRecord);
                            }
                            approveRecordRepository.deleteAllByRecordIdInAndType(oldIdList, CommentsType.EXPENSE.toDbValue());
                        }
                        if (CollUtil.isNotEmpty(expenseRecords)) {
                            ExpenseRecord weekending = expenseRecords.get(0);
                            ExpenseRecord expenseRecord = recordList.get(0);
                            TimeSheetStatus status = getHigherPriorityStatus(expenseRecord.getStatus(), weekending.getStatus());
                            Instant submittedDate = expenseRecord.getSubmittedDate();
                            if (expenseRecord.getStatus() == weekending.getStatus()) {
                                if (List.of(TimeSheetStatus.APPLIED_APPROVE, TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(status)) {
                                    submittedDate = expenseRecord.getSubmittedDate().isAfter(weekending.getSubmittedDate())? expenseRecord.getSubmittedDate(): weekending.getSubmittedDate();
                                }
                            } else if (status == weekending.getStatus()) {
                                submittedDate = weekending.getSubmittedDate();
                            }
                            Instant finalSubmittedDate = submittedDate;
                            recordList.forEach(expense -> {
                                expense.setStatus(status);
                                expense.setSubmittedDate(finalSubmittedDate);
                            });
                        }
                        expenseRecordRepository.saveAll(recordList);
                    }
                } else {
                    //也可能发生变化, weekStart 变化, weekend 没有变化
                    expenseRecordRepository.deleteByIdList(oldIdList);
                    Long approveRecordId = oldIdList.stream().min(Long::compareTo).orElse(0L);
                    if (!oldWeekStart.isEqual(weekDataVO.getWeekStart())) {
                        //weekStart 发送变化需要特殊处理
                        List<ExpenseRecord> firstExpenseRecordList = expenseRecordIndexMap.get(oldWeekStart.minusDays(1) + "-" + currentIndex);
                        if (CollUtil.isNotEmpty(firstExpenseRecordList)) {
                            ExpenseRecord firstExpenseRecord = firstExpenseRecordList.get(0);
                            if (firstExpenseRecord.getWeekEndingDate().isEqual(oldExpenseRecord.getWeekEndingDate())) {
                                //这个地方需要延续2个week 的expense type
                                //寻找到firstList 里面的最大lineIndex
                                int maxLineIndex = firstExpenseRecordList.stream().mapToInt(ExpenseRecord::getLineIndex).max().orElse(0);
                                TimeSheetStatus status = getHigherPriorityStatus(firstExpenseRecord.getStatus(), oldExpenseRecord.getStatus());
                                Instant submittedDate = oldExpenseRecord.getSubmittedDate();
                                if (firstExpenseRecord.getStatus() == oldExpenseRecord.getStatus()) {
                                    //提交时间和审批时间都取较大的
                                    ExpenseRecord currentExpenseRecord = oldExpenseRecord.getCreatedDate().isAfter(firstExpenseRecord.getCreatedDate())? oldExpenseRecord: firstExpenseRecord;
                                    oldWeekend = currentExpenseRecord.getWorkDate();
                                    if (List.of(TimeSheetStatus.APPLIED_APPROVE, TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(status)) {
                                        //选择用那个的 comment
                                        submittedDate = currentExpenseRecord.getSubmittedDate();
                                    }
                                    //审批时间
                                    if (List.of(TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(status)) {
                                        ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(approveRecordId, CommentsType.EXPENSE);
                                        ApproveRecord oldApproveRecord = approveExpenseMap.get(firstExpenseRecordList.stream().mapToLong(ExpenseRecord::getId).min().orElse(0));
                                        if (approveRecord != null && oldApproveRecord != null) {
                                            approveRecordId = approveRecord.getCreatedDate().isAfter(oldApproveRecord.getCreatedDate())? approveRecord.getRecordId(): oldApproveRecord.getId();
                                        }
                                    }
                                } else if (status == firstExpenseRecord.getStatus()) {
                                    submittedDate = firstExpenseRecord.getSubmittedDate();
                                    oldWeekend = firstExpenseRecord.getWeekEnd();
                                    approveRecordId = firstExpenseRecord.getId();
                                }
                                Instant finalSubmittedDate = submittedDate;
                                recordList.forEach(expense -> {
                                    expense.setStatus(status);
                                    expense.setSubmittedDate(finalSubmittedDate);
                                    expense.setLineIndex(expense.getLineIndex() + maxLineIndex + 1);
                                });
                            }
                        }
                    }
                    expenseRecordRepository.saveAll(recordList);
                    if (weekDataVO.getWorkDate().isEqual(weekDataVO.getWeekEnd())) {
                        //特殊处理一下, workDate == weekEnd 的时候会出现数据为空的情况,这种数据直接删除
                        if(checkWeekCost(weekDataVO, assignmentVO, expenseIndex)) {
                            return;
                        }
                        //comment 处理
                        LocalDate currentLocalDate = weekDataVO.getWeekStart();
                        StringBuilder comments = new StringBuilder();
                        TimeSheetComments copySource = null;
                        while (!currentLocalDate.isAfter(weekDataVO.getWeekEnd())) {
                            TimeSheetComments timeSheetComments = commentExpenseMap.get(currentLocalDate.toString() + expenseIndex);
                            if (ObjectUtil.isEmpty(timeSheetComments)) {
                                timeSheetComments = commentsRepository.findAllByDateAndType(currentLocalDate, assignmentVO.getTalentId(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getId(), expenseIndex);
                            }
                            if (ObjectUtil.isNotEmpty(timeSheetComments)) {
                                if (!comments.toString().contains(timeSheetComments.getComments())) {
                                    comments.append(";\n").append(timeSheetComments.getComments());
                                }
                                copySource = timeSheetComments;
                            }
                            currentLocalDate = currentLocalDate.plusDays(1);
                        }
                        if (copySource != null && copySource.getWorkDate() != null) {
                            commentsRepository.deleteComment(weekDataVO.getWeekEnd(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getTalentId(), assignmentVO.getId(), expenseIndex);
                            TimeSheetComments newComments = new TimeSheetComments();
                            BeanUtil.copyProperties(copySource, newComments, "id", "workDate");
                            newComments.setWorkDate(weekDataVO.getWeekEnd());
                            newComments.setComments(comments.toString());
                            commentsRepository.save(newComments);
                        }
                        //approve 处理
                        ApproveRecord approveRecord = approveExpenseMap.get(approveRecordId);
                        if (ObjectUtil.isEmpty(approveRecord)) {
                            approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(oldIdList.stream().min(Long::compare).orElse(0L), CommentsType.EXPENSE);
                        }
                        if (ObjectUtil.isNotEmpty(approveRecord)) {
                            ApproveRecord newApproveRecord = new ApproveRecord();
                            BeanUtil.copyProperties(approveRecord, newApproveRecord, "id", "weekEnd", "recordId");
                            newApproveRecord.setRecordId(recordList.stream().mapToLong(ExpenseRecord::getId).min().orElse(0L));
                            newApproveRecord.setWeekEnd(weekDataVO.getWeekEnd());
                            approveRecordRepository.save(newApproveRecord);
                            approveRecordRepository.updateCreateAndModifiedById(newApproveRecord.getId(), approveRecord.getCreatedBy(), approveRecord.getLastModifiedBy(), approveRecord.getCreatedDate(), approveRecord.getLastModifiedDate());
                        }
                        //聚合表数据补全提交时间和审批数据
                        expenseRecordRepository.updateLastModifiedDateById(recordList.stream().map(ExpenseRecord::getId).toList(), Instant.now());
                    }
                }
            } else {
                //如果是切割后的数据,需要计算切割的周期内有数据,如果有,需要补全最后一天weekend用于聚合
                if (weekDataVO.getWorkDate().isEqual(weekDataVO.getWeekEnd())) {
                    LocalDate currentDate = weekDataVO.getWeekStart();
                    int i = 0;
                    //不需要相等, 因为weekend 的数据不存在
                    List<ExpenseRecord> list = new ArrayList<>();
                    while ((currentDate = currentDate.plusDays(i++)).isBefore(weekDataVO.getWeekEnd())) {
                        if (expenseRecordIndexMap.containsKey(currentDate + "-" + currentIndex)) {
                            list = expenseRecordIndexMap.get(currentDate + "-" + currentIndex);
                            break;
                        }
                    }
                    if (CollUtil.isNotEmpty(list)) {
                        Integer expenseIndex = currentIndex;
                        List<ExpenseRecord> expenseRecordList = list;
                        //周期内存在数据
                        ExpenseRecord oldExpenseRecord = expenseRecordList.get(0);
                        if (List.of(TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(oldExpenseRecord.getStatus())) {
                            //判断是切割还是合并
                            List<ExpenseRecord> weekEndingExpenseList = expenseRecordIndexMap.getOrDefault(oldExpenseRecord.getWeekEndingDate() + "-" + currentIndex, null);
                            if (CollUtil.isEmpty(weekEndingExpenseList)) {
                                //weekend 不变表示合并数据
                                expenseRecordList = expenseRecordIndexMap.getOrDefault(oldExpenseRecord.getWeekEnd() + "-" + currentIndex, expenseRecordList);
                            } else {
                                expenseRecordList = weekEndingExpenseList;
                            }
                        }
                        oldExpenseRecord = expenseRecordList.get(0);
                        ExpenseRecord weekendExpenseRecord = new ExpenseRecord();
                        BeanUtil.copyProperties(oldExpenseRecord, weekendExpenseRecord, "id", "workDate", "cost", "s3Key", "weekDay", "lineIndex");
                        weekendExpenseRecord.setLineIndex(0);
                        weekendExpenseRecord.setWorkDate(weekDataVO.getWorkDate());
                        weekendExpenseRecord.setWeekDay(weekDataVO.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                        weekendExpenseRecord.setWeekStart(weekDataVO.getWeekStart());
                        weekendExpenseRecord.setWeekEnd(weekDataVO.getWeekEnd());
                        weekendExpenseRecord.setWeekEndingDate(weekDataVO.getWeekendingDate());
                        expenseRecordRepository.save(weekendExpenseRecord);
                        TimeSheetComments timeSheetComments = commentExpenseMap.get(oldExpenseRecord.getWeekEnd().toString() + expenseIndex);
                        if (ObjectUtil.isEmpty(timeSheetComments)) {
                            timeSheetComments = commentsRepository.findAllByDateAndType(oldExpenseRecord.getWeekEnd(), assignmentVO.getTalentId(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getId(), expenseIndex);
                        }
                        if (timeSheetComments != null) {
                            commentsRepository.deleteComment(weekDataVO.getWeekEnd(), CommentsType.EXPENSE.toDbValue(), assignmentVO.getTalentId(), assignmentVO.getId(), expenseIndex);
                            TimeSheetComments newComments = new TimeSheetComments();
                            BeanUtil.copyProperties(timeSheetComments, newComments, "id", "workDate");
                            newComments.setWorkDate(weekDataVO.getWeekEnd());
                            commentsRepository.save(newComments);
                        }
                        //approve 处理
                        ApproveRecord approveRecord = approveExpenseMap.get(expenseRecordList.stream().map(ExpenseRecord::getId).min(Long::compare).orElse(0L));
                        if (ObjectUtil.isEmpty(approveRecord)) {
                            approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(oldExpenseRecord.getId(), CommentsType.EXPENSE);
                        }
                        if (ObjectUtil.isNotEmpty(approveRecord)) {
                            ApproveRecord newApproveRecord = new ApproveRecord();
                            BeanUtil.copyProperties(approveRecord, newApproveRecord, "id", "weekEnd", "recordId");
                            newApproveRecord.setRecordId(weekendExpenseRecord.getId());
                            newApproveRecord.setWeekEnd(weekDataVO.getWeekEnd());
                            approveRecordRepository.save(newApproveRecord);
                            approveRecordRepository.updateCreateAndModifiedById(newApproveRecord.getId(), approveRecord.getCreatedBy(), approveRecord.getLastModifiedBy(), approveRecord.getCreatedDate(), approveRecord.getLastModifiedDate());
                        }
                        //聚合表数据补全提交时间和审批数据
                        expenseRecordRepository.updateLastModifiedDateById(List.of(weekendExpenseRecord.getId()), Instant.now());
                    }
                }
            }
        }
    }

    private boolean checkWeekCost(WeekDataVO weekDataVO, AssignmentVO assignmentVO, Integer expenseIndex) {
        List<ExpenseRecord> expenseRecords = expenseRecordRepository.findAllByDateAndIndex(weekDataVO.getWeekStart(), weekDataVO.getWeekEnd(), assignmentVO.getTalentId(), assignmentVO.getId(), expenseIndex);
        if (CollUtil.isNotEmpty(expenseRecords)) {
            double sum = expenseRecords.stream()
                    .filter(expenseRecord -> ObjectUtil.isNotEmpty(expenseRecord.getCost()))
                    .mapToDouble(ExpenseRecord::getCost)
                    .sum();
            if (sum <= 0) {
                //没有金额的情况下需要删除数据
                expenseRecordRepository.deleteByDate(weekDataVO.getWeekStart(), weekDataVO.getWeekEnd(), assignmentVO.getTalentId(), assignmentVO.getId());
                return true;
            }
        }
        return false;
    }

    private void timeSheetRecordUpdateByWeekEnd(WeekDataVO weekDataVO, AssignmentVO assignmentVO, Map<LocalDate, TimeSheetRecord> timeSheetRecordMap, Map<Long, ApproveRecord> approveRecordMap, Map<LocalDate, TimeSheetComments> commentMap) {
        TimeSheetRecord timeSheetRecord;
        if (timeSheetRecordMap.containsKey(weekDataVO.getWorkDate())) {
            //已经有timesheet 数据
            log.info("[APN] time sheet record weekend update, assignmentId = {}, weekData = {}", assignmentVO.getId(), weekDataVO);
            timeSheetRecord = timeSheetRecordMap.get(weekDataVO.getWorkDate());
            //不需要发生变化
            if (timeSheetRecord.getWeekEnd() != null
                    && timeSheetRecord.getWeekEnd().isEqual(weekDataVO.getWeekEnd())
                    && timeSheetRecord.getWeekStart().isEqual(weekDataVO.getWeekStart())) {
                return;
            }
            //保存历史记录
            LocalDate oldWeekEnd = timeSheetRecord.getWeekEnd();
            LocalDate oldWeekStart = timeSheetRecord.getWeekStart();
            TimeSheetRecord copy = new TimeSheetRecord();
            long oldId = timeSheetRecord.getId();
            BeanUtil.copyProperties(timeSheetRecord, copy, "id");
            timeSheetRecord = copy;
            timeSheetRecord.setWeekStart(weekDataVO.getWeekStart());
            timeSheetRecord.setWeekEnd(weekDataVO.getWeekEnd());
            timeSheetRecord.setWeekEndingDate(weekDataVO.getWeekendingDate());
            if (ObjectUtil.isNotNull(oldWeekEnd) && !oldWeekEnd.isEqual(weekDataVO.getWeekEnd())) {
                //当新老 weekend 发生变化
                if (weekDataVO.getWorkDate().isEqual(weekDataVO.getWeekEnd())) {
                    //出现了新增的切割节点
                    timeSheetRecordRepository.deleteById(oldId);
                    TimeSheetRecord newTimeSheetRecord = new TimeSheetRecord();
                    TimeSheetRecord weekEndingRecord = timeSheetRecordMap.get(weekDataVO.getWeekendingDate());
                    if (ObjectUtil.isNotEmpty(weekEndingRecord)) {
                        timeSheetRecord.setStatus(weekEndingRecord.getStatus());
                        timeSheetRecord.setSubmittedDate(weekEndingRecord.getSubmittedDate());
                        //需要weekending 的weekend 来获取comment
                        oldWeekEnd = weekEndingRecord.getWeekEnd();
                    }
                    BeanUtil.copyProperties(timeSheetRecord, newTimeSheetRecord, "id");
                    timeSheetRecord = newTimeSheetRecord;
                    timeSheetRecordRepository.save(timeSheetRecord);
                    TimeSheetUtil.setWeekId(List.of(timeSheetRecord));
                    setCommentsAndApproveRecord(oldWeekEnd, assignmentVO, weekDataVO, timeSheetRecord, weekEndingRecord.getId(), approveRecordMap, commentMap);
                } else {
                    //今天不是weekend,但是今天是oldWeekend
                    timeSheetRecordRepository.deleteById(oldId);
                    if (weekDataVO.getWorkDate().isEqual(oldWeekEnd)) {
                        TimeSheetComments comments = commentsRepository.findAllByDateAndType(oldWeekEnd, assignmentVO.getTalentId(), CommentsType.TIME_SHEET.toDbValue(), assignmentVO.getId());
                        if (comments != null) {
                            commentMap.put(comments.getWorkDate(), comments);
                        }
                        commentsRepository.deleteComment(oldWeekEnd, CommentsType.TIME_SHEET.toDbValue(), assignmentVO.getTalentId(), assignmentVO.getId());
                        ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(oldId, CommentsType.TIME_SHEET);
                        if (approveRecord != null) {
                            approveRecordMap.put(approveRecord.getRecordId(), approveRecord);
                        }
                        approveRecordRepository.deleteAllByRecordIdInAndType(List.of(oldId), CommentsType.TIME_SHEET.toDbValue());
                    }
                    TimeSheetRecord weekEndingRecord = timeSheetRecordMap.get(weekDataVO.getWeekEnd());
                    if (ObjectUtil.isNotEmpty(weekEndingRecord)) {
                        TimeSheetStatus status = getHigherPriorityStatus(timeSheetRecord.getStatus(), weekEndingRecord.getStatus());
                        timeSheetRecord.setStatus(status);
                        if (timeSheetRecord.getStatus() == weekEndingRecord.getStatus()) {
                            //取时间靠后的数据为标准这里是删除数据,只比较提交时间
                            if (List.of(TimeSheetStatus.APPLIED_APPROVE, TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(status)) {
                                timeSheetRecord.setSubmittedDate(timeSheetRecord.getSubmittedDate().isAfter(weekEndingRecord.getSubmittedDate()) ? timeSheetRecord.getSubmittedDate() : weekEndingRecord.getSubmittedDate());
                            }
                        } else if (status == weekEndingRecord.getStatus()) {
                            //是什么状态以什么时间为准
                            timeSheetRecord.setSubmittedDate(weekEndingRecord.getSubmittedDate());
                        }
                    }
                    timeSheetRecordRepository.save(timeSheetRecord);
                    TimeSheetUtil.setWeekId(List.of(timeSheetRecord));
                }
            } else {
                //更新,触发重新聚合
                timeSheetRecordRepository.deleteById(oldId);
                //可能 weekStart 发生了变化, 但是 weekEnd 没有变化
                if (!oldWeekStart.isEqual(timeSheetRecord.getWeekStart())) {
                    TimeSheetRecord firstTimeSheetRecord = timeSheetRecordMap.get(oldWeekStart.minusDays(1));
                    if (ObjectUtil.isNotEmpty(firstTimeSheetRecord) && firstTimeSheetRecord.getWeekEndingDate().isEqual(timeSheetRecord.getWeekEndingDate())) {
                        TimeSheetStatus status = getHigherPriorityStatus(timeSheetRecord.getStatus(), firstTimeSheetRecord.getStatus());
                        if (timeSheetRecord.getStatus() == firstTimeSheetRecord.getStatus()) {
                            //提交时间和审批时间都取较大的
                            TimeSheetRecord currentTimeSheetRecord = timeSheetRecord.getCreatedDate().isAfter(firstTimeSheetRecord.getCreatedDate())? timeSheetRecord: firstTimeSheetRecord;
                            //选择用那个的 comment
                            oldWeekEnd = currentTimeSheetRecord.getWorkDate();
                            if (List.of(TimeSheetStatus.APPLIED_APPROVE, TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(status)) {
                                timeSheetRecord.setSubmittedDate(currentTimeSheetRecord.getSubmittedDate());
                            }
                            //审批时间
                            if (List.of(TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(status)) {
                                ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(oldId, CommentsType.TIME_SHEET);
                                ApproveRecord oldApproveRecord = approveRecordMap.get(firstTimeSheetRecord.getId());
                                if (approveRecord != null && oldApproveRecord != null) {
                                    oldId = approveRecord.getCreatedDate().isAfter(oldApproveRecord.getCreatedDate())? approveRecord.getRecordId(): oldApproveRecord.getRecordId();
                                }
                            }
                        } else if (status == firstTimeSheetRecord.getStatus()) {
                            timeSheetRecord.setSubmittedDate(firstTimeSheetRecord.getSubmittedDate());
                            oldId = firstTimeSheetRecord.getId();
                            oldWeekEnd = firstTimeSheetRecord.getWeekEnd();
                        }
                        timeSheetRecord.setStatus(status);
                        //holiday 数据合并
                        timeSheetHolidayRecordRepository.updateHolidayWeekendByWeekendingDate(assignmentVO.getId(), timeSheetRecord.getWeekEndingDate(), timeSheetRecord.getWeekEndingDate().minusDays(6));
                    }
                }
                timeSheetRecordRepository.save(timeSheetRecord);
                TimeSheetUtil.setWeekId(List.of(timeSheetRecord));
                if (timeSheetRecord.getWorkDate().isEqual(timeSheetRecord.getWeekEnd())) {
                    setCommentsAndApproveRecord(oldWeekEnd, assignmentVO, weekDataVO, timeSheetRecord, oldId, approveRecordMap, commentMap);
                }
            }
        } else {
            //历史没有数据在的情况,查看当天是不是 weekend,如果是这需要新增record
            if (weekDataVO.getWeekEnd().equals(weekDataVO.getWorkDate())) {
                //没有数据的时候,需要查询一下old 的weekend 是否有数据
                int i = 0;
                while (i < 7) {
                    timeSheetRecord = timeSheetRecordMap.get(weekDataVO.getWorkDate().plusDays(i++));
                    if (ObjectUtil.isEmpty(timeSheetRecord)) {
                        continue;
                    }
                    LocalDate oldWeekStart = timeSheetRecord.getWeekStart();
                    if (oldWeekStart.isEqual(weekDataVO.getWorkDate()) || oldWeekStart.isBefore(weekDataVO.getWorkDate())) {
                        if (!timeSheetRecord.getWeekEnd().isEqual(timeSheetRecord.getWorkDate())) {
                            continue;
                        }
                        TimeSheetRecord newTimeSheetRecord = new TimeSheetRecord();
                        BeanUtil.copyProperties(timeSheetRecord, newTimeSheetRecord, "id", "workHours", "regularHours", "overTime", "doubleTime", "totalHours");
                        newTimeSheetRecord.setWorkDate(weekDataVO.getWorkDate());
                        newTimeSheetRecord.setWeekEndingDate(weekDataVO.getWeekendingDate());
                        newTimeSheetRecord.setWeekStart(weekDataVO.getWeekStart());
                        newTimeSheetRecord.setWeekEnd(weekDataVO.getWeekEnd());
                        newTimeSheetRecord.setWeekDay(weekDataVO.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                        timeSheetRecordRepository.save(newTimeSheetRecord);
                        TimeSheetUtil.setWeekId(List.of(newTimeSheetRecord));
                        //comment 处理
                        TimeSheetComments timeSheetComments = commentsRepository.findAllByDateAndType(weekDataVO.getWeekendingDate(), assignmentVO.getTalentId(), CommentsType.TIME_SHEET.toDbValue(), assignmentVO.getId());
                        if (timeSheetComments != null) {
                            TimeSheetComments newComments = new TimeSheetComments();
                            BeanUtil.copyProperties(timeSheetComments, newComments, "id", "workDate");
                            newComments.setWorkDate(weekDataVO.getWeekEnd());
                            commentsRepository.save(newComments);
                        }
                        //approve 处理
                        if (CollUtil.newArrayList(TimeSheetStatus.REJECTED, TimeSheetStatus.APPROVED).contains(newTimeSheetRecord.getStatus())) {
                            ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByWeekEndingDateAndAssignmentIdAndRecordTypeOrderByIdDesc(assignmentVO.getId(), CommentsType.TIME_SHEET.toDbValue(), weekDataVO.getWeekendingDate());
                            ApproveRecord newApproveRecord = new ApproveRecord();
                            BeanUtil.copyProperties(approveRecord, newApproveRecord, "id", "weekEnd", "recordId");
                            newApproveRecord.setRecordId(newTimeSheetRecord.getId());
                            newApproveRecord.setWeekEnd(weekDataVO.getWeekEnd());
                            approveRecordRepository.save(newApproveRecord);
                            approveRecordRepository.updateCreateAndModifiedById(newApproveRecord.getId(), approveRecord.getCreatedBy(), approveRecord.getLastModifiedBy(), approveRecord.getCreatedDate(), approveRecord.getLastModifiedDate());
                        }
                        timeSheetRecordRepository.updateLastModifiedDateById(CollUtil.newArrayList(newTimeSheetRecord.getId()), Instant.now());
                        return;
                    }
                }
//                log.info("[APN] time sheet record weekend add, assignmentId = {}, weekData = {}", assignmentVO.getId(), weekDataVO);
//                timeSheetRecord = new TimeSheetRecord();
//                timeSheetRecord.setWorkDate(weekDataVO.getWorkDate());
//                timeSheetRecord.setWeekEndingDate(weekDataVO.getWeekendingDate());
//                timeSheetRecord.setWeekStart(weekDataVO.getWeekStart());
//                timeSheetRecord.setWeekEnd(weekDataVO.getWeekEnd());
//                timeSheetRecord.setTalentId(assignmentVO.getTalentId());
//                timeSheetRecord.setTenantId(assignmentVO.getTenantId());
//                timeSheetRecord.setAssignmentId(assignmentVO.getId());
//                timeSheetRecord.setTimeSheetType(assignmentVO.getSheetType());
//                if (weekDataVO.getWorkDate().isAfter(LocalDate.now())) {
//                    timeSheetRecord.setStatus(TimeSheetStatus.NO_RECORD);
//                } else {
//                    timeSheetRecord.setStatus(TimeSheetStatus.MISSING);
//                }
//                timeSheetRecordRepository.save(timeSheetRecord);
//                TimeSheetUtil.setWeekId(List.of(timeSheetRecord));
            }
        }
    }

    private void setCommentsAndApproveRecord(LocalDate oldWeekEnd, AssignmentVO assignmentVO, WeekDataVO weekDataVO, TimeSheetRecord timeSheetRecord, Long oldId, Map<Long, ApproveRecord> approveRecordMap, Map<LocalDate, TimeSheetComments> commentMap) {
        //comment 处理
        if (oldWeekEnd != weekDataVO.getWeekEnd()) {
            TimeSheetComments timeSheetComments = commentMap.get(oldWeekEnd);
            if (ObjectUtil.isEmpty(timeSheetComments)) {
                timeSheetComments = commentsRepository.findAllByDateAndType(oldWeekEnd, assignmentVO.getTalentId(), CommentsType.TIME_SHEET.toDbValue(), assignmentVO.getId());
            }
            if (timeSheetComments != null) {
                commentsRepository.deleteComment(weekDataVO.getWeekEnd(), CommentsType.TIME_SHEET.toDbValue(), assignmentVO.getTalentId(), assignmentVO.getId());
                TimeSheetComments newComments = new TimeSheetComments();
                BeanUtil.copyProperties(timeSheetComments, newComments, "id", "workDate");
                newComments.setWorkDate(weekDataVO.getWeekEnd());
                commentsRepository.save(newComments);
            }
        }
        //approve 处理
        ApproveRecord approveRecord = approveRecordMap.get(oldId);
        if (ObjectUtil.isEmpty(approveRecord)) {
            approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(oldId, CommentsType.TIME_SHEET);
        }
        if (ObjectUtil.isNotEmpty(approveRecord)) {
            ApproveRecord newApproveRecord = new ApproveRecord();
            BeanUtil.copyProperties(approveRecord, newApproveRecord, "id", "weekEnd", "recordId");
            newApproveRecord.setRecordId(timeSheetRecord.getId());
            newApproveRecord.setWeekEnd(weekDataVO.getWeekEnd());
            approveRecordRepository.save(newApproveRecord);
            approveRecordRepository.updateCreateAndModifiedById(newApproveRecord.getId(), approveRecord.getCreatedBy(), approveRecord.getLastModifiedBy(), approveRecord.getCreatedDate(), approveRecord.getLastModifiedDate());
        }
        //聚合表数据补全提交时间和审批数据
        timeSheetRecordRepository.updateLastModifiedDateById(CollUtil.newArrayList(timeSheetRecord.getId()), Instant.now());
    }

    public static TimeSheetStatus getHigherPriorityStatus(TimeSheetStatus status, TimeSheetStatus status1) {
        return STATUS_PRIORITY_MAP.get(status) >= STATUS_PRIORITY_MAP.get(status1) ? status : status1;
    }

    /**
     * 手动变自动, 或者自动变手动
     *
     * @param dto
     * @param vo
     */
    private void changeOverTimeType(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo) {
        //查询出所有属于  Pending, draft, reject 的数据重新计算
        List<TimeSheetRecord> recordList = timeSheetRecordRepository.findAllByStatusInAndTalentIdAndAssignmentId(Arrays.asList(TimeSheetStatus.APPLIED_APPROVE, TimeSheetStatus.DRAFT, TimeSheetStatus.REJECTED), dto.getTalentId(), dto.getId());
        if (CollUtil.isEmpty(recordList)) {
            return;
        }
        //获取所有 timesheet record id
        List<Long> idList = recordList.stream().map(TimeSheetRecord::getId).collect(Collectors.toList());
        List<LocalDate> workList = recordList.stream().map(TimeSheetRecord::getWorkDate).distinct().collect(Collectors.toList());
        recordList = recordList.stream()
                .sorted(Comparator.comparing(TimeSheetRecord::getWorkDate))
                .collect(Collectors.toList());
        if (recordList.size() > SqlUtil.PARTITION_COUNT_999) {
            // 如果数据量超过999,会出现修改无效,查询数据为0等问题,需要根据999 做切割
            // 第一修改状态为draft
            CollUtil.split(idList, SqlUtil.PARTITION_COUNT_999).forEach(partitionIdList -> timeSheetRecordRepository.updateTimeSheetStatusByIdList(TimeSheetStatus.DRAFT.toDbValue(), partitionIdList));
            // 第二删除comments 记录
            CollUtil.split(workList, SqlUtil.PARTITION_COUNT_999).forEach(workDateList -> commentsRepository.deleteAllByTalentIdAndAssignmentIdAndWorkDateIsInAndCommentsType(dto.getTalentId(), dto.getId(), workDateList, CommentsType.TIME_SHEET));
            // 第三删除审批记录
            CollUtil.split(idList, SqlUtil.PARTITION_COUNT_999).forEach(list -> approveRecordRepository.deleteAllByRecordIdInAndType(list, CommentsType.TIME_SHEET.toDbValue()));
        } else {
            approveRecordRepository.deleteAllByRecordIdInAndType(idList, CommentsType.TIME_SHEET.toDbValue());
            commentsRepository.deleteAllByTalentIdAndAssignmentIdAndWorkDateIsInAndCommentsType(dto.getTalentId(), dto.getId(), workList, CommentsType.TIME_SHEET);
            timeSheetRecordRepository.updateTimeSheetStatusByIdList(TimeSheetStatus.DRAFT.toDbValue(), idList);
        }
        //如果是变成自动,需要重新计算
        if (Objects.equals(dto.getBillInfo().getOvertimeType(), OverTimeType.AUTO)) {
            AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(dto.getId());
            //workDate 升序
            recordList.forEach(timeSheetRecord -> {
                if (!timeSheetRecord.getWorkDate().isEqual(timeSheetRecord.getWeekEnd())) {
                    //一个切割周才计算,否则跳过
                    return;
                }
                //重新计算,需要完整周
                LocalDate startDate = TimeSheetUtil.findDateInWeek(timeSheetRecord.getWorkDate(), assignmentTimeSheet.getWeekEnding().toDbValue(), 2);
                doHandlerOtDtByDraft(startDate, timeSheetRecord.getWeekEndingDate(), vo, timeSheetRecord, assignmentTimeSheet.getCalculateType());
            });
        }
    }

    @Override
    public AssignmentGeneralInfoVO currentAssignment(Long startId) {
        List<TalentAssigment> assignments = assignmentRepository.findAllByStartIdOrderByStartDate(startId);
        if (CollUtil.isEmpty(assignments)) {
            return new AssignmentGeneralInfoVO();
        }
        AssignmentGeneralInfoVO nearByAss = findNearByAssignment(assignments, startId);
        nearByAss.setAssignmentCount(assignments.size());
        return nearByAss;
    }

    @Override
    public AssignmentDetailInfoDTO findLatestAssignment(Long startId) {
        TalentAssigment assigment = assignmentRepository.findLatestAssigmentByStartId(startId);
        if (assigment == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_FIND_DETAIL_BY_START_ID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        return detail(assigment.getId());
    }

    @Override
    public AssignmentDetailInfoDTO detail(Long assignmentId) {
        AssignmentGeneralInfoVO assigment = assignmentRepository.findGeneralInfoById(assignmentId);
        if (assigment == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_IS_NOT_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        AssignmentDetailInfoDTO ass = new AssignmentDetailInfoDTO();
        ServiceUtils.myCopyProperties(assigment, ass);
        //set expenses flag
        Integer countExpenses = expenseRecordRepository.countByAssignmentId(assignmentId);
        if (countExpenses > 0) {
            ass.setIsSubmitExpenses(true);
        } else {
            ass.setIsSubmitExpenses(false);
        }
        BillInfoDTO billInfoDTO = getBillInfo(assignmentId);
        ass.setBillInfo(billInfoDTO);
        List<AssignmentContribution> userInfo = getContribution(assignmentId);
        ass.setUserInfo(userInfo);
        PayInfoDTO payInfoDTO = getPayInfo(assignmentId);
        ass.setPayInfo(payInfoDTO);
        TimeSheetInfoDTO timeSheetInfoDTO = getTimeSheet(assignmentId);
        ass.setTimeSheet(timeSheetInfoDTO);
        WorkInfoDTO workInfoDTO = getLocation(assignmentId);
        ass.setWorkLocation(workInfoDTO);
        if (ass.getType() == AssignmentType.EXTENSION) {
            ass.setOrder(getExtensionAssignmentOrder(ass.getId(), ass.getStartId()));
        } else {
            ass.setOrder(0);
        }
        return ass;
    }

    @Override
    public List<AssignmentGeneralInfoVO> list(Long startId) {
        return assignmentRepository.findGeneralInfoByStartId(startId);
    }

    @Override
    public AssignmentCancelEliminateInfoVO assignmentList(Long talentRecruitmentProcessId,Long talentId) {
        AssignmentCancelEliminateInfoVO vo = new AssignmentCancelEliminateInfoVO();
        StartDTO startDTO = financeService.getStartByTalentIdAndTalentRecruitmentProcessId(talentId,talentRecruitmentProcessId,StartStatus.ELIMINATED).getBody();
        if(startDTO !=null){
            List<AssignmentEliminateVO> eliminateVOList = assignmentRepository.findCancelEliminateInfoByStartId(startDTO.getId());
            vo.setEliminateInfoVOList(eliminateVOList);
        }

        List<AssignmentActiveVO> activeVOList = assignmentRepository.findStartDateAndEndDateByTalentId(talentId);
        vo.setActiveDateList(activeVOList);

        return vo;
    }



    public static boolean hasOverlaps(List<AssignmentDetailInfoDTO> ranges) {
        for (int i = 0; i < ranges.size(); i++) {
            for (int j = i + 1; j < ranges.size(); j++) {
                if (ranges.get(i).overlaps(ranges.get(j))) {
                    return true; // 发现重叠
                }
            }
        }
        return false; // 没有发现重叠
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(AssignmentBatchInfoDTO dto) {

        //校验前端传过来的日期是否覆盖
        if (null !=dto.getUpdateInfoList() && !dto.getUpdateInfoList().isEmpty()) {
            if (hasOverlaps(dto.getUpdateInfoList())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_CHECK_RATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            for (AssignmentDetailInfoDTO info : dto.getUpdateInfoList()) {
                AssignmentDetailInfoDTO detail = BeanUtil.toBean(info, AssignmentDetailInfoDTO.class);
                detail.setId(null);
                validateStartDateAndEndDate(detail);
            }
        }



        //修改start 和 node状态
        applicationService.onboardCancelEliminate(dto.getTalentRecruitmentProcessId());

        //执行删除
        if (null !=dto.getDeleteInfoList() && !dto.getDeleteInfoList().isEmpty()) {
            for (Long id : dto.getDeleteInfoList()) {
                delete(id);
            }
        }

        if (null != dto.getUpdateInfoList() && !dto.getUpdateInfoList().isEmpty()) {
            for (AssignmentDetailInfoDTO info : dto.getUpdateInfoList()) {
                save(info);
            }
        }
    }

    @Override
    public List<PayInfoVO> findPayList(Long startId) {
        List<AssignmentGeneralInfoVO> assignments = assignmentRepository.findGeneralInfoByStartId(startId);
        List<PayInfoVO> result = new LinkedList<>();
        for (AssignmentGeneralInfoVO assigment : assignments) {
            PayInfoVO vo = new PayInfoVO();
            ServiceUtils.myCopyProperties(assigment, vo);
            vo.setPayInfo(getPayInfo(assigment.getId()));
            result.add(vo);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        AssignmentGeneralInfoVO vo = assignmentRepository.findGeneralInfoById(id);
        Integer count = expenseRecordRepository.countByAssignmentId(vo.getId());
        if (vo.getIsClockIn() || count > 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DELETE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        assignmentRepository.deleteById(id);
        deleteInfo(id);
        timeSheetRecordRepository.deleteByAssignmentId(id);
        expenseRecordRepository.deleteByAssignmentId(id);
        breakTimeRepository.deleteByAssignmentId(id);
        commentsRepository.deleteByAssignmentId(id);
        assignmentSyncToHrService.deleteAssignment(List.of(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByStartId(Long startId) {

        if (null == startId) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DELETE_STARTID_IS_NULL.getKey(), CommonUtils.USER_LANGUAGE_SET.concat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        List<TalentAssigment> assigmentList = assignmentRepository.findLatestAssigmentByStartIdList(startId);
        if (!assigmentList.isEmpty()) {
            //throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_FIND_DETAIL_BY_START_ID.getKey(), CommonUtils.USER_LANGUAGE_SET.concat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            for (TalentAssigment assigment : assigmentList) {
                timeSheetRecordRepository.deleteByAssignmentId(assigment.getId());
                log.info("[onboard eliminate] delete timesheet record assigment id :{}", assigment.getId());
                expenseRecordRepository.deleteByAssignmentId(assigment.getId());
                log.info("[onboard eliminate] delete expense record assigment id :{}", assigment.getId());
                breakTimeRepository.deleteByAssignmentId(assigment.getId());
                log.info("[onboard eliminate] delete break time assigment id :{}", assigment.getId());
                commentsRepository.deleteByAssignmentId(assigment.getId());
                log.info("[onboard eliminate] delete comments assigment id :{}", assigment.getId());
                approveRecordRepository.deleteByAssignmentId(assigment.getId());
                log.info("[onboard eliminate] delete approve record assigment id :{}", assigment.getId());

                //更新assignment status is close
                assignmentRepository.updateStatusByAssignmentId(assigment.getId(), AssignmentStatusType.CLOSE.toDbValue());
                log.info("[onboard eliminate] update assigment status is close id :{}", assigment.getId());
            }
            assignmentSyncToHrService.buildAssignmentListSyncToHrMq(assigmentList.stream().map(TalentAssigment::getId).collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTimesheetByStartId(Long startId) {
        if (null == startId) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DELETE_STARTID_IS_NULL.getKey(), CommonUtils.USER_LANGUAGE_SET.concat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        List<TalentAssigment> assigmentList = assignmentRepository.findCloseAssigmentByStartIdAndStatusList(startId);
        if (!assigmentList.isEmpty()) {
            //throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_FIND_DETAIL_BY_START_ID.getKey(), CommonUtils.USER_LANGUAGE_SET.concat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            for (TalentAssigment assigment : assigmentList) {
                //更新assignment status is close
                assigment.setStatus(AssignmentStatusType.APPROVED);
                assignmentRepository.saveAndFlush(assigment);
                log.info("[onboard cancel eliminate] update assigment status is close id :{}", assigment.getId());
                AssignmentVO assignmentVO = assignmentRepository.findAssigmentInfoByAssignmentId(assigment.getId());
                TimeSheetUtil.setTimeSheetStatus(TimeSheetUserType.TALENT, assigment.getTalentId(), SecurityUtils.getTenantId(), assigment.getId(), assignmentVO.getFrequency());
            }
            assignmentSyncToHrService.buildAssignmentListSyncToHrMq(assigmentList.stream().map(TalentAssigment::getId).toList());
        }
    }


    @Override
    public Integer findLatestAssignmentOrder(Long startId) {
        List<TalentAssigment> assignments = assignmentRepository.findByType(startId, AssignmentType.EXTENSION.toDbValue());
        return assignments.size();
    }

    @Override
    public AssignmentPayRateInfo getBillUnit(Long assignmentId) {
        return payRateRepository.findBillUnit(assignmentId);
    }

    @Override
    public AssignmentPayRateInfo getPayUnit(Long assignmentId) {
        return payRateRepository.findPayUnit(assignmentId);
    }


    @Override
    public Page<CurrentContractorVO> findCurrentContractorPage(CurrentContractorDTO dto, Pageable pageable) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<CurrentContractorVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return self.findCurrentContractorList(dto, pageable);
        });
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return countCurrentContractor(dto);
        });

        //组合处理过后的future
        CompletableFuture<List<CurrentContractorVO>> updatedDataFuture = dataFuture.thenCompose(contractors -> {
            List<Long> jobIds = contractors.stream()
                    .map(CurrentContractorVO::getJobId)
                    .toList();
            return CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                Set<Long> privateJobIds = new HashSet<>(assignmentRepository.findPrivateJobIds(jobIds));
                contractors.forEach(vo -> vo.setIsPrivateJob(privateJobIds.contains(vo.getJobId())));
                return contractors;
            });
        });
        // 等待所有 CompletableFuture 完成并处理异常
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(updatedDataFuture, countFuture)
                .exceptionally(t -> {
                    log.error("Error occurred when fetching start data: ", t);
                    throw new ExternalServiceInterfaceException("Error occurred when fetching start data: ");
                });
        allFutures.join();
        return new PageImpl<>(updatedDataFuture.join(), Pageable.unpaged(), countFuture.join());
    }


    @Override
    public void currentContractorListExcel(CurrentContractorDTO dto, HttpServletResponse response) {
        PageRequest pageRequest = PageRequest.of(0, 10000);
        List<CurrentContractorVO> currentContractorVOList = self.findCurrentContractorList(dto, pageRequest);

        response.setHeader("Content-Disposition", "attachment;filename=current_contractor_" + dto.getNowDate() + ".xls");
        addFileDownLoadHeader(response);

        try (OutputStream outputStream = response.getOutputStream()) {
            ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, "current_contractor")
                    .head(CurrentContractorVO.class)
                    .needHead(true)
                    .build();
            currentContractorVOList.stream().filter(v -> v.getConfidentialTalentViewAble() !=null && !v.getConfidentialTalentViewAble())
                    .forEach(ExcelUtil::maskConfidentialTalentData);
            excelWriter.write(currentContractorVOList, writeSheet);
            excelWriter.finish();
            outputStream.flush();
        } catch (IOException e) {
            log.error("Error writing Excel file", e);
        }
    }

    @Override
    public Page<AssignmentActivityVO> getJobActivities(List<Long> assigmentIds, Pageable pageable) throws IOException {
        return assignmentActivityService.getJobActivities(assigmentIds, pageable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONArray deleteAssignmentByJobId(Long jobId) {
        JSONArray assignmentArray = new JSONArray();
        List<TalentAssigment> talentAssigmentList = talentAssignmentRepository.findAllByJobId(jobId);
        List<Long> assignmentIds = talentAssigmentList.stream().map(TalentAssigment::getId).toList();
        List<AssignmentPayRateInfo> assignmentPayRateInfoList = payRateRepository.findAllByAssignmentIdIn(assignmentIds);
        List<AssignmentBillInfo> assignmentBillInfoList = billInfoRepository.findByAssignmentIdIn(assignmentIds);
        List<TimeSheetManager> timeSheetManagerList = managerRepository.findAllByAssignmentIdIn(assignmentIds);
        List<AssignmentContribution> assignmentContributionList = contributionRepository.findAllByAssignmentIdIn(assignmentIds);
        List<AssignmentPayInfo> assignmentPayInfoList = payInfoRepository.findAllByAssignmentIdIn(assignmentIds);
        List<AssignmentTimeSheet> assignmentTimeSheetList = timeSheetRepository.findAllByAssignmentIdIn(assignmentIds);
        List<AssignmentLocation> assignmentLocationList = locationRepository.findAllByAssignmentIdIn(assignmentIds);
        List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByAssignmentIdIn(assignmentIds);
        List<TimeSheetWeekEndingRecord> timeSheetWeekEndingRecordList = timeSheetWeekEndingRecordRepository.findAllByAssignmentIdIn(assignmentIds);
        List<TimeSheetBreakTimeRecord> timeSheetBreakTimeRecordList = timeSheetBreakTimeRepository.findAllByAssignmentIdIn(assignmentIds);
        List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByAssignmentIdIn(assignmentIds);
        List<ApproveRecord> approveRecordList = approveRecordRepository.findAllByAssignmentIdIn(assignmentIds);
        List<TimeSheetComments> timeSheetCommentsList = commentsRepository.findAllByAssignmentIdIn(assignmentIds);
        Map<Long, List<AssignmentPayRateInfo>> assignmentPayRateInfoMap = assignmentPayRateInfoList.stream().collect(Collectors.groupingBy(AssignmentPayRateInfo::getAssignmentId));
        Map<Long, List<AssignmentBillInfo>> assignmentBillInfoMap = assignmentBillInfoList.stream().collect(Collectors.groupingBy(AssignmentBillInfo::getAssignmentId));
        Map<Long, List<TimeSheetManager>> timeSheetManagerMap = timeSheetManagerList.stream().collect(Collectors.groupingBy(TimeSheetManager::getAssignmentId));
        Map<Long, List<AssignmentContribution>> assignmentContributionMap = assignmentContributionList.stream().collect(Collectors.groupingBy(AssignmentContribution::getAssignmentId));
        Map<Long, List<AssignmentPayInfo>> assignmentPayInfoMap = assignmentPayInfoList.stream().collect(Collectors.groupingBy(AssignmentPayInfo::getAssignmentId));
        Map<Long, List<AssignmentTimeSheet>> assignmentTimeSheetMap = assignmentTimeSheetList.stream().collect(Collectors.groupingBy(AssignmentTimeSheet::getAssignmentId));
        Map<Long, List<AssignmentLocation>> assignmentLocationMap = assignmentLocationList.stream().collect(Collectors.groupingBy(AssignmentLocation::getAssignmentId));
        Map<Long, List<TimeSheetRecord>> timeSheetRecordMap = timeSheetRecordList.stream().collect(Collectors.groupingBy(TimeSheetRecord::getAssignmentId));
        Map<Long, List<TimeSheetWeekEndingRecord>> timeSheetWeekEndingRecordMap = timeSheetWeekEndingRecordList.stream().collect(Collectors.groupingBy(TimeSheetWeekEndingRecord::getAssignmentId));
        Map<Long, List<TimeSheetBreakTimeRecord>> timeSheetBreakTimeRecordMap = timeSheetBreakTimeRecordList.stream().collect(Collectors.groupingBy(TimeSheetBreakTimeRecord::getAssignmentId));
        Map<Long, List<ExpenseRecord>> expenseRecordMap = expenseRecordList.stream().collect(Collectors.groupingBy(ExpenseRecord::getAssignmentId));
        Map<Long, List<ApproveRecord>> approveRecordMap = approveRecordList.stream().collect(Collectors.groupingBy(ApproveRecord::getAssignmentId));
        Map<Long, List<TimeSheetComments>> timeSheetCommentsMap = timeSheetCommentsList.stream().collect(Collectors.groupingBy(TimeSheetComments::getAssignmentId));
        talentAssigmentList.forEach(o -> {
            JSONObject item = DtoToJsonUtil.toJsonWithColumnNames(o);
            item.put("assignment_pay_rate", DtoToJsonUtil.toJsonArrayWithColumnNames(assignmentPayRateInfoMap.get(o.getId())));
            item.put("assignment_bill_info", DtoToJsonUtil.toJsonArrayWithColumnNames(assignmentBillInfoMap.get(o.getId())));
            item.put("timesheet_manager", DtoToJsonUtil.toJsonArrayWithColumnNames(timeSheetManagerMap.get(o.getId())));
            item.put("assignment_contribution", DtoToJsonUtil.toJsonArrayWithColumnNames(assignmentContributionMap.get(o.getId())));
            item.put("assignment_pay_info", DtoToJsonUtil.toJsonArrayWithColumnNames(assignmentPayInfoMap.get(o.getId())));
            item.put("assignment_timesheet", DtoToJsonUtil.toJsonArrayWithColumnNames(assignmentTimeSheetMap.get(o.getId())));
            item.put("assignment_location", DtoToJsonUtil.toJsonArrayWithColumnNames(assignmentLocationMap.get(o.getId())));
            item.put("time_sheet_record", DtoToJsonUtil.toJsonArrayWithColumnNames(timeSheetRecordMap.get(o.getId())));
            item.put("time_sheet_week_ending_record", DtoToJsonUtil.toJsonArrayWithColumnNames(timeSheetWeekEndingRecordMap.get(o.getId())));
            item.put("timesheet_breaktime_record", DtoToJsonUtil.toJsonArrayWithColumnNames(timeSheetBreakTimeRecordMap.get(o.getId())));
            item.put("timesheet_expense_record", DtoToJsonUtil.toJsonArrayWithColumnNames(expenseRecordMap.get(o.getId())));
            item.put("timesheet_approve_record", DtoToJsonUtil.toJsonArrayWithColumnNames(approveRecordMap.get(o.getId())));
            item.put("timesheet_comments", DtoToJsonUtil.toJsonArrayWithColumnNames(timeSheetCommentsMap.get(o.getId())));
            assignmentArray.add(item);
        });

        talentAssignmentRepository.deleteAllByIdInBatch(assignmentIds);
        payRateRepository.deleteAllByIdInBatch(assignmentPayRateInfoList.stream().map(AssignmentPayRateInfo::getId).toList());
        billInfoRepository.deleteAllByIdInBatch(assignmentBillInfoList.stream().map(AssignmentBillInfo::getId).toList());
        managerRepository.deleteAllByIdInBatch(timeSheetManagerList.stream().map(TimeSheetManager::getId).toList());
        contributionRepository.deleteAllByIdInBatch(assignmentContributionList.stream().map(AssignmentContribution::getId).toList());
        payInfoRepository.deleteAllByIdInBatch(assignmentPayInfoList.stream().map(AssignmentPayInfo::getId).toList());
        timeSheetRepository.deleteAllByIdInBatch(assignmentTimeSheetList.stream().map(AssignmentTimeSheet::getId).toList());
        locationRepository.deleteAllByIdInBatch(assignmentLocationList.stream().map(AssignmentLocation::getId).toList());
        timeSheetRecordRepository.deleteAllByIdInBatch(timeSheetRecordList.stream().map(TimeSheetRecord::getId).toList());
        timeSheetWeekEndingRecordRepository.deleteAllByIdInBatch(timeSheetWeekEndingRecordList.stream().map(TimeSheetWeekEndingRecord::getId).toList());
        timeSheetBreakTimeRepository.deleteAllByIdInBatch(timeSheetBreakTimeRecordList.stream().map(TimeSheetBreakTimeRecord::getId).toList());
        expenseRecordRepository.deleteAllByIdInBatch(expenseRecordList.stream().map(ExpenseRecord::getId).toList());
        approveRecordRepository.deleteAllByIdInBatch(approveRecordList.stream().map(ApproveRecord::getId).toList());
        commentsRepository.deleteAllByIdInBatch(timeSheetCommentsList.stream().map(TimeSheetComments::getId).toList());

        JSONArray jsonArray = contractorInvoiceService.deleteByAssignment(assignmentIds);
        assignmentArray.addAll(jsonArray);
        // 同步 hr service 删除 assignment 相关数据
        assignmentSyncToHrService.deleteAssignment(assignmentIds);

        return assignmentArray;
    }


    private void addFileDownLoadHeader(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Headers","*");
        response.setHeader("Access-Control-Allow-Methods","*");
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
    }

    @ProcessConfidentialTalent
    public List<CurrentContractorVO> findCurrentContractorList(CurrentContractorDTO dto, Pageable pageable) {
        String dataSql = """
                SELECT
                    tta.id assignment_id,
                    t.full_name full_name,
                    t.id talent_id,
                    j.title title,
                    j.id job_id,
                    GROUP_CONCAT(distinct (case CONCAT(uam.first_name,uam.last_name) regexp '[一-龥]' when 1 then CONCAT(uam.last_name,uam.first_name) ELSE CONCAT(uam.first_name, " ",uam.last_name) END)) am,
                    GROUP_CONCAT(distinct(case CONCAT(udm.first_name,udm.last_name) regexp '[一-龥]' when 1 then CONCAT(udm.last_name,udm.first_name) ELSE CONCAT(udm.first_name, " ",udm.last_name) END)) dm,
                    GROUP_CONCAT(distinct(case CONCAT(uac.first_name,uac.last_name) regexp '[一-龥]' when 1 then CONCAT(uac.last_name,uac.first_name) ELSE CONCAT(uac.first_name, " ",uac.last_name) END)) ac,
                    GROUP_CONCAT(distinct(case CONCAT(urecruiter.first_name,urecruiter.last_name) regexp '[一-龥]' when 1 then CONCAT(urecruiter.last_name,urecruiter.first_name) ELSE CONCAT(urecruiter.first_name, " ",urecruiter.last_name) END)) recruiter,
                    GROUP_CONCAT(distinct(case CONCAT(usourcer.first_name,usourcer.last_name) regexp '[一-龥]' when 1 then CONCAT(usourcer.last_name,usourcer.first_name) ELSE CONCAT(usourcer.first_name, " ",usourcer.last_name) END)) sourcer,
                    abi.assignment_division,
                    DATE_FORMAT(tta.start_date, '%Y-%m-%d') AS start_date,
                    DATE_FORMAT(tta.end_date, '%Y-%m-%d') AS end_date,
                    al.city,
                    al.province,
                    ec.symbol,
                    ROUND(
                      CASE
                        WHEN apr.time_unit = 1 THEN apr.pay_rate / 8
                        WHEN apr.time_unit = 2 THEN apr.pay_rate / 40
                        WHEN apr.time_unit = 3 THEN apr.pay_rate * 12 / 2080
                        WHEN apr.time_unit = 4 THEN apr.pay_rate / 2080
                        ELSE apr.pay_rate
                      END,
                      2
                    ) AS hourly_bill_rate
                FROM timesheet_talent_assignment tta
                inner join talent t on t.id = tta.talent_id
                inner join job j on j.id = tta.job_id
                left join assignment_contribution am on am.assignment_id = tta.id and am.user_role = 0
                left join user uam on uam.id = am.user_id
                left join assignment_contribution dm on dm.assignment_id = tta.id and dm.user_role = 3
                left join user udm on udm.id = dm.user_id
                left join assignment_contribution ac on ac.assignment_id = tta.id and ac.user_role = 5
                left join user uac on uac.id = ac.user_id
                left join assignment_contribution recruiter on recruiter.assignment_id = tta.id and recruiter.user_role = 1
                left join user urecruiter on urecruiter.id = recruiter.user_id
                left join assignment_contribution sourcer on sourcer.assignment_id = tta.id and sourcer.user_role = 2
                left join user usourcer on usourcer.id = sourcer.user_id
                left join assignment_bill_info abi on abi.assignment_id = tta.id
                left join assignment_location al on al.assignment_id = tta.id
                left join assignment_pay_rate apr on apr.assignment_id = tta.id and apr.pay_type = 2
                left join enum_currency ec on ec.id = apr.currency
                where tta.start_date <= :nowDate AND tta.end_date >= :nowDate and {whereConditionSql} and tta.status != 2
                group by tta.id
                {orderSql}
                limit :start, :size
                """;
        //下面是组装where条件的方法
        String whereCondition = setWhereSql(dto);
        Map<String, String> paramMap = new HashMap<>(16);
        paramMap.put("whereConditionSql", whereCondition);
        paramMap.put("orderSql", setOrderSql(dto.getSort()));
        dataSql = StrUtil.format(dataSql, paramMap);
        Query dataQuery = entityManager.createNativeQuery(dataSql, CurrentContractorVO.class);
        int start = pageable.getPageSize() * pageable.getPageNumber();
        dataQuery.setParameter("start", start).setParameter("size", pageable.getPageSize());
        setParam(dataQuery, dto);
        return dataQuery.getResultList();
    }

    public Long countCurrentContractor(CurrentContractorDTO dto) {
        String countSql = """
                select count(1) from (
                SELECT
                   count(tta.id)
                FROM timesheet_talent_assignment tta
                inner join talent t on t.id = tta.talent_id
                inner join job j on j.id = tta.job_id
                left join assignment_contribution am on am.assignment_id = tta.id and am.user_role = 0
                left join user uam on uam.id = am.user_id
                left join assignment_contribution dm on dm.assignment_id = tta.id and dm.user_role = 3
                left join user udm on udm.id = dm.user_id
                left join assignment_contribution ac on ac.assignment_id = tta.id and ac.user_role = 5
                left join user uac on uac.id = ac.user_id
                left join assignment_contribution recruiter on recruiter.assignment_id = tta.id and recruiter.user_role = 1
                left join user urecruiter on urecruiter.id = recruiter.user_id
                left join assignment_contribution sourcer on sourcer.assignment_id = tta.id and sourcer.user_role = 2
                left join user usourcer on usourcer.id = sourcer.user_id
                left join assignment_bill_info abi on abi.assignment_id = tta.id
                left join assignment_location al on al.assignment_id = tta.id
                where tta.start_date <= :nowDate AND tta.end_date >= :nowDate and {whereConditionSql}  and tta.status != 2
                group by tta.id
                ) temp
                """;
        //下面是组装where条件的方法
        String whereCondition = setWhereSql(dto);
        Map<String, String> paramMap = new HashMap<>(16);
        paramMap.put("whereConditionSql", whereCondition);
        countSql = StrUtil.format(countSql, paramMap);
        Query countQuery = entityManager.createNativeQuery(countSql);
        setParam(countQuery, dto);
        return Long.parseLong(String.valueOf(countQuery.getSingleResult()));
    }

    private String setOrderSql(SearchSortDTO sortDTO) {
        if (ObjectUtil.isNotNull(sortDTO)) {
            String field = StrUtil.toUnderlineCase(sortDTO.getProperty());
            Map<String, String> chineseMap = new HashMap<>(16);
            chineseMap.put("full_name", "t.full_name");
            chineseMap.put("title", "j.title");
            chineseMap.put("am", "GROUP_CONCAT(distinct (case CONCAT(uam.first_name,uam.last_name) regexp '[一-龥]' when 1 then CONCAT(uam.last_name,uam.first_name) ELSE CONCAT(uam.first_name, \" \",uam.last_name) END))");
            chineseMap.put("dm", "GROUP_CONCAT(distinct(case CONCAT(udm.first_name,udm.last_name) regexp '[一-龥]' when 1 then CONCAT(udm.last_name,udm.first_name) ELSE CONCAT(udm.first_name, \" \",udm.last_name) END))");
            chineseMap.put("ac", "GROUP_CONCAT(distinct(case CONCAT(uac.first_name,uac.last_name) regexp '[一-龥]' when 1 then CONCAT(uac.last_name,uac.first_name) ELSE CONCAT(uac.first_name, \" \",uac.last_name) END))");
            chineseMap.put("recruiter", "GROUP_CONCAT(distinct(case CONCAT(urecruiter.first_name,urecruiter.last_name) regexp '[一-龥]' when 1 then CONCAT(urecruiter.last_name,urecruiter.first_name) ELSE CONCAT(urecruiter.first_name, \" \",urecruiter.last_name) END))");
            chineseMap.put("sourcer", "GROUP_CONCAT(distinct(case CONCAT(usourcer.first_name,usourcer.last_name) regexp '[一-龥]' when 1 then CONCAT(usourcer.last_name,usourcer.first_name) ELSE CONCAT(usourcer.first_name, \" \",usourcer.last_name) END))");
            if (chineseMap.containsKey(field)) {
                String value = chineseMap.get(field);
                return " order by CASE WHEN " + value + " IS NULL OR " + value + " = '' THEN 0 ELSE 1 END DESC, CONVERT(" + value + "  USING GBK) " + sortDTO.getDirection() + " ";
            } else {
                return " order by " + field + " " + sortDTO.getDirection() + " ";
            }
        } else {
            return " order by start_date desc ";
        }
    }

    private String setWhereSql(CurrentContractorDTO dto) {
        if (dto.getCompanyId() == null) {
            throw new CustomParameterizedException("companyId is null");
        }
        if (StrUtil.isBlank(dto.getNowDate())) {
            throw new CustomParameterizedException("nowDate is null");
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" tta.company_id = :companyId and tta.tenant_id = :tenantId ");
        if (StrUtil.isNotBlank(dto.getFullName())) {
            sb.append(" and t.full_name like :fullName ");
        }
        if (StrUtil.isNotBlank(dto.getTitle())) {
            sb.append(" and j.title like :jobTitle ");
        }
        if (ObjectUtil.isNotNull(dto.getJobId())) {
            sb.append(" and j.id = :jobId ");
        }
        if (StrUtil.isNotBlank(dto.getAm())) {
            sb.append("""
                    and EXISTS (select 1 from user u where u.id = uam.id and
                    CASE
                      WHEN CONCAT(u.first_name, u.last_name) REGEXP '[一-龥]' THEN CONCAT(u.last_name, u.first_name)
                      ELSE CONCAT(u.first_name, ' ', u.last_name)
                    END LIKE :am
                    )
                    """);
        }
        if (StrUtil.isNotBlank(dto.getDm())) {
            sb.append("""
                    and EXISTS (select 1 from user u where u.id = udm.id and
                    CASE
                      WHEN CONCAT(u.first_name, u.last_name) REGEXP '[一-龥]' THEN CONCAT(u.last_name, u.first_name)
                      ELSE CONCAT(u.first_name, ' ', u.last_name)
                    END LIKE :dm
                    )
                    """);
        }
        if (StrUtil.isNotBlank(dto.getAc())) {
            sb.append("""
                    and EXISTS (select 1 from user u where u.id = uac.id and
                    CASE
                      WHEN CONCAT(u.first_name, u.last_name) REGEXP '[一-龥]' THEN CONCAT(u.last_name, u.first_name)
                      ELSE CONCAT(u.first_name, ' ', u.last_name)
                    END LIKE :ac
                    )
                    """);
        }
        if (StrUtil.isNotBlank(dto.getRecruiter())) {
            sb.append("""
                    and EXISTS (select 1 from user u where u.id = urecruiter.id and
                    CASE
                      WHEN CONCAT(u.first_name, u.last_name) REGEXP '[一-龥]' THEN CONCAT(u.last_name, u.first_name)
                      ELSE CONCAT(u.first_name, ' ', u.last_name)
                    END LIKE :recruiter
                    )
                   """);
        }
        if (StrUtil.isNotBlank(dto.getSourcer())) {
            sb.append("""
                    and EXISTS (select 1 from user u where u.id = usourcer.id and
                    CASE
                      WHEN CONCAT(u.first_name, u.last_name) REGEXP '[一-龥]' THEN CONCAT(u.last_name, u.first_name)
                      ELSE CONCAT(u.first_name, ' ', u.last_name)
                    END LIKE :sourcer
                    )
                   """);
        }
        if (CollUtil.isNotEmpty(dto.getAssignmentDivisionList())) {
            sb.append(" and abi.assignment_division in :assignmentDivision ");
        }
        if (StrUtil.isNotBlank(dto.getProvince())) {
            sb.append(" and al.province like :province ");
        }
        return sb.toString();
    }

    private void setParam(Query query, CurrentContractorDTO dto) {
        if (dto.getCompanyId() == null) {
            throw new CustomParameterizedException("companyId is null");
        }
        if (StrUtil.isBlank(dto.getNowDate())) {
            throw new CustomParameterizedException("nowDate is null");
        }
        query.setParameter("nowDate", dto.getNowDate())
                .setParameter("companyId", dto.getCompanyId())
                .setParameter("tenantId", SecurityUtils.getTenantId());
        if (StrUtil.isNotBlank(dto.getFullName())) {
            query.setParameter("fullName", "%" + dto.getFullName() + "%");
        }
        if (StrUtil.isNotBlank(dto.getTitle())) {
            query.setParameter("jobTitle", "%" + dto.getTitle() + "%");
        }
        if (ObjectUtil.isNotNull(dto.getJobId())) {
            query.setParameter("jobId", dto.getJobId());
        }
        if (StrUtil.isNotBlank(dto.getAm())) {
            query.setParameter("am", "%" + dto.getAm() + "%");
        }
        if (StrUtil.isNotBlank(dto.getDm())) {
            query.setParameter("dm", "%" + dto.getDm() + "%");
        }
        if (StrUtil.isNotBlank(dto.getAc())) {
            query.setParameter("ac", "%" + dto.getAc() + "%");
        }
        if (StrUtil.isNotBlank(dto.getRecruiter())) {
            query.setParameter("recruiter", "%" + dto.getRecruiter() + "%");
        }
        if (StrUtil.isNotBlank(dto.getSourcer())) {
            query.setParameter("sourcer", "%" + dto.getSourcer() + "%");
        }
        if (CollUtil.isNotEmpty(dto.getAssignmentDivisionList())) {
            query.setParameter("assignmentDivision", dto.getAssignmentDivisionList().stream().map(AssignmentDivision::toDbValue).toList());
        }
        if (StrUtil.isNotBlank(dto.getProvince())) {
            query.setParameter("province", "%" + dto.getProvince() + "%");
        }
    }

    @Override
    public void checkAssignmentDate(AssignmentCheckDateDTO dto) {
        Long assignmentId = dto.getAssignmentId();
        AssignmentVO vo = assignmentRepository.findAssigmentInfoByAssignmentId(assignmentId);
        List<Integer> statusList = CollUtil.newArrayList(TimeSheetStatus.DRAFT.toDbValue(), TimeSheetStatus.APPROVED.toDbValue(), TimeSheetStatus.REJECTED.toDbValue(), TimeSheetStatus.APPLIED_APPROVE.toDbValue());
        //开始时间退后
        if (dto.getStartDate().compareTo(vo.getStartDate()) > 0) {
            if (CollUtil.isNotEmpty(timeSheetRecordRepository.findAllByDateAndStatus(vo.getStartDate(), dto.getStartDate().minusDays(1), vo.getTalentId(), vo.getId(), statusList))) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DATE_CHECK_CODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            if (CollUtil.isNotEmpty(expenseRecordRepository.findAllByDateAndStatus(vo.getStartDate(), dto.getStartDate().minusDays(1), vo.getTalentId(), vo.getId(), statusList))) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DATE_CHECK_CODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
        //结束时间提前
        if (dto.getEndDate().compareTo(vo.getEndDate()) < 0) {
            if (CollUtil.isNotEmpty(timeSheetRecordRepository.findAllByDateAndStatus(dto.getEndDate().plusDays(1), vo.getEndDate(), vo.getTalentId(), vo.getId(), statusList))) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DATE_CHECK_CODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            if (CollUtil.isNotEmpty(expenseRecordRepository.findAllByDateAndStatus(dto.getEndDate().plusDays(1), vo.getEndDate(), vo.getTalentId(), vo.getId(), statusList))) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_DATE_CHECK_CODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
    }

    private Long saveAssignment(AssignmentDetailInfoDTO dto) {
        if (dto.getStartId() == null || dto.getTalentId() == null || dto.getJobId() == null || dto.getCompanyId() == null || dto.getStatus() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PARAM_CHECK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (dto.getStartDate().compareTo(dto.getEndDate()) > 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_START_DATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        checkRateChangeAssignmentDate(dto);
        validateStartDateAndEndDate(dto);
        TalentAssigment assigment = Optional.ofNullable(dto.getId()).flatMap(id -> assignmentRepository.findById(id)).orElse(new TalentAssigment());
        ServiceUtils.myCopyProperties(dto, assigment);
        assigment.setIsWeekEnd(1);
        if (dto.getId() == null) {
            assigment.setCreatedUserId(SecurityUtils.getUserId());
        } else {
            TalentAssigment old = assignmentRepository.findById(dto.getId()).orElse(null);
            if (old != null) {
                assigment.setCreatedUserId(old.getCreatedUserId());
                if (!dto.isFrequencyChanged()) {
                    List<Integer> statusList = CollUtil.newArrayList(TimeSheetStatus.DRAFT.toDbValue(), TimeSheetStatus.APPROVED.toDbValue(), TimeSheetStatus.REJECTED.toDbValue(), TimeSheetStatus.APPLIED_APPROVE.toDbValue());
                    if (timeSheetRecordRepository.countByStatusInAndTalentIdAndAssignmentId(statusList, dto.getTalentId(), dto.getId()) > 0
                            || expenseRecordRepository.countByAssignmentId(dto.getId()) > 0) {
                        //如果有timesheet 或者 expense record 不能修改
                        assigment.setIsWeekEnd(old.getIsWeekEnd());
                    }
                }
            }
        }
        assigment.setTenantId(SecurityUtils.getTenantId());
        assigment.setTalentRecruitmentProcessId(dto.getTalentRecruitmentProcessId());
        assigment.setId(dto.getId());
        assigment.setWorkingHours(dto.getWorkingHours());
        assigment.setLastModifiedBy(SecurityUtils.getUserUid());
        assigment.setLastModifiedDate(Instant.now());
        assigment = assignmentRepository.saveAndFlush(assigment);
        return assigment.getId();
    }

    private void saveBillInfo(AssignmentDetailInfoDTO dto) {
        BillInfoDTO bill = dto.getBillInfo();
        if (AssignmentStatusType.APPROVED == dto.getStatus() && bill == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_BILL_INFO.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (bill == null) {
            return;
        }
        AssignmentBillInfo assignmentBillInfo = billInfoRepository.findByAssignmentId(dto.getId());
        if (assignmentBillInfo == null) {
            assignmentBillInfo = new AssignmentBillInfo();
        }
        ServiceUtils.myCopyProperties(bill, assignmentBillInfo);
        assignmentBillInfo.setPaymentTerms(bill.getPaymentTerms());
        List<AssignmentPayRateInfo> payInfos = bill.getPayRateInfo();
        if (AssignmentStatusType.APPROVED == dto.getStatus() && (payInfos == null || bill.getIsExcept() == null)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PAY_INFO.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        log.info("bill info:" + JSON.toJSONString(bill));
        assignmentBillInfo.setAssignmentId(dto.getId());
        billInfoRepository.saveAndFlush(assignmentBillInfo);
        //update holiday ot
        payInfos.stream().filter(info -> Objects.equals(info.getType(), PayRateType.BILL_RATE)).findFirst().ifPresent(info -> {
            timeSheetHolidayRecordRepository.updateHolidayOtByAssignmentId(dto.getId(), info.getCurrency(), RateUnitUtil.rateConvert(info.getPayRate(), info.getTimeUnit()));
        });
    }

    private void saveContribution(AssignmentDetailInfoDTO dto) {
        List<AssignmentContribution> userInfoDtoS = dto.getUserInfo();
        List<TimeSheetManager> ams = new LinkedList<>();
        if (userInfoDtoS == null) {
            return;
        }
        userInfoDtoS.forEach(item -> {
            item.setId(null);
            item.setAssignmentId(dto.getId());
            if (item.getUserRole() == UserRole.AM) {
                TimeSheetManager manager = new TimeSheetManager();
                manager.setAssignmentId(dto.getId());
                manager.setTalentId(dto.getTalentId());
                manager.setRole(ManagerRoleType.AM);
                manager.setClientId(item.getUserId());
                ams.add(manager);
            }
        });
        managerRepository.deleteByAssignmentIdAndUserRole(dto.getId(), ManagerRoleType.AM.toDbValue());
        managerRepository.saveAll(ams);

        List<AssignmentContribution> contributions = contributionRepository.findByAssignmentId(dto.getId());
        List<AssignmentContribution> saveOrAddContributions = new ArrayList<>();
        // add or update
        userInfoDtoS.forEach(contribution -> {
            Optional<AssignmentContribution> existUser = contributions.stream()
                    .filter(u -> u.getUserId().equals(contribution.getUserId()) && u.getUserRole().equals(contribution.getUserRole()))
                    .findAny();
            if (existUser.isPresent()) {
                AssignmentContribution assignmentContribution = existUser.get();
                assignmentContribution.setPercentage(contribution.getPercentage());
                saveOrAddContributions.add(assignmentContribution);
            } else {
                saveOrAddContributions.add(contribution);
            }
        });
        //remove
        List<AssignmentContribution> removeContributions = contributions.stream()
                .filter(contribution -> userInfoDtoS.stream().noneMatch(u -> u.getUserId().equals(contribution.getUserId()) && u.getUserRole().equals(contribution.getUserRole())))
                .toList();

        contributionRepository.saveAllAndFlush(saveOrAddContributions);
        contributionRepository.deleteAll(removeContributions);
    }

    private void savePayInfo(AssignmentDetailInfoDTO dto) {
        AssignmentPayInfo payInfo = payInfoRepository.findByAssignmentId(dto.getId());
        if (payInfo == null) {
            payInfo = new AssignmentPayInfo();
        }
        PayInfoDTO pay = dto.getPayInfo();
        if (pay == null) {
            return;
        }
        ServiceUtils.myCopyProperties(dto.getPayInfo(), payInfo);
        payInfo.setAssignmentId(dto.getId());
        payInfoRepository.saveAndFlush(payInfo);
    }

    private void savePayRate(AssignmentDetailInfoDTO dto) {
        List<AssignmentPayRateInfo> assignmentPayRateInfos = new ArrayList<>();

        BillInfoDTO billInfo = dto.getBillInfo();
        if (billInfo != null) {
            List<AssignmentPayRateInfo> billPayRateInfos = billInfo.getPayRateInfo();
            if (billPayRateInfos != null && !billPayRateInfos.isEmpty()) {
                validatePayRate(dto, billInfo, billPayRateInfos);

                billPayRateInfos.forEach(info -> {
                    info.setAssignmentId(dto.getId());
                    info.setContentType(PayRateContentType.BILLING);
                });
                assignmentPayRateInfos.addAll(billPayRateInfos);
            }
        }
        PayInfoDTO payInfo = dto.getPayInfo();

        if (payInfo != null) {
            List<AssignmentPayRateInfo> payRateInfos = payInfo.getPayRateInfo();
            if (payRateInfos != null && !payRateInfos.isEmpty()) {
                payRateInfos.forEach(info -> {
                    info.setAssignmentId(dto.getId());
                    info.setContentType(PayRateContentType.PAY);
                });
                assignmentPayRateInfos.addAll(payRateInfos);
            }
        }

        List<AssignmentPayRateInfo> existPayRateInfos = payRateRepository.findAllByAssignmentId(dto.getId());
        // save 时，前端传的是完整的数据，直接赋值
        if (existPayRateInfos == null || existPayRateInfos.isEmpty()) {
            existPayRateInfos = new ArrayList<>(assignmentPayRateInfos);
        } else {
            // update 时，前端没有传 id， 需要根据 contentType 和 type 去匹配，然后更新
            Map<PayRateContentType, Map<PayRateType, AssignmentPayRateInfo>> payInfoMap = assignmentPayRateInfos.stream()
                    .collect(Collectors.groupingBy(
                            AssignmentPayRateInfo::getContentType,
                            Collectors.toMap(  // 内层 Map
                                    AssignmentPayRateInfo::getType,
                                    Function.identity()
                            )
                    ));

            existPayRateInfos.forEach(payRateInfo -> {
                if (payInfoMap.containsKey(payRateInfo.getContentType())) {
                    Map<PayRateType, AssignmentPayRateInfo> typeMap = payInfoMap.get(payRateInfo.getContentType());
                    if (typeMap.containsKey(payRateInfo.getType())) {
                        AssignmentPayRateInfo newInfo = typeMap.get(payRateInfo.getType());
                        payRateInfo.setPayRate(newInfo.getPayRate());
                        payRateInfo.setCurrency(newInfo.getCurrency());
                        payRateInfo.setTimeUnit(newInfo.getTimeUnit());
                    }
                }
            });
        }
        payRateRepository.saveAllAndFlush(existPayRateInfos);
    }

    private void saveTimeSheet(AssignmentDetailInfoDTO dto) {
        AssignmentTimeSheet timeSheet = timeSheetRepository.findByAssignmentId(dto.getId());
        if (timeSheet == null) {
            timeSheet = new AssignmentTimeSheet();
        }
        TimeSheetInfoDTO timeSheetInfoDTO = dto.getTimeSheet();
        if (timeSheetInfoDTO == null) {
            return;
        }
        ServiceUtils.myCopyProperties(dto.getTimeSheet(), timeSheet);
        WorkInfoDTO location = dto.getWorkLocation();
        if (location != null) {
            LocationDTO locationDto = location.getLocation();
            if (locationDto != null && (StrUtil.isNotBlank(locationDto.getProvinceCode()) || StrUtil.isNotBlank(locationDto.getCountryCode()) || StrUtil.isNotBlank(locationDto.getCity()))) {
                if (locationDto.getProvinceCode() == null && !CollUtil.newArrayList("PR", "VI").contains(locationDto.getCountryCode()) || locationDto.getCountryCode() == null) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_TIMESHEET_STATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
                //sk 算法有2个,直接根据传参确认
                if (Objects.equals(locationDto.getProvinceCode(), "SK") && Objects.equals(locationDto.getCountryCode(), "CA")) {
                    if (dto.getTimeSheet().getCalculateMethodType() == null || !CollUtil.newArrayList(CalculateMethodType.CANADA_SK_8, CalculateMethodType.CANADA_SK_10).contains(dto.getTimeSheet().getCalculateMethodType())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_TIMESHEET_CALCULATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                    }
                    timeSheet.setCalculateType(dto.getTimeSheet().getCalculateMethodType());
                } else {
                    CalculateTypeAndState calculateTypeAndState = findCalculateTypeAndStateByLocationDto(locationDto);
                    if (calculateTypeAndState == null) {
                        calculateTypeAndState = calculateAndStateRepository.findDefaultCalculateType(locationDto.getCountryCode());
                        if (dto.getStatus() == AssignmentStatusType.APPROVED && calculateTypeAndState == null) {
                            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_TIMESHEET_CALCULATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                        }
                    }
                    timeSheet.setCalculateType(calculateTypeAndState.getCalculateMethodType());
                }
                timeSheet.setAssignmentId(dto.getId());

            }
        }
        LinkedList<Long> approvers = timeSheetInfoDTO.getApprovers();
        saveManager(approvers, dto.getTalentId(), dto.getId());
        timeSheet.setAssignmentId(dto.getId());
        timeSheetRepository.saveAndFlush(timeSheet);
    }

    private void saveLocation(AssignmentDetailInfoDTO dto) {
        WorkInfoDTO location = dto.getWorkLocation();
        AssignmentLocation al = locationRepository.findByAssignmentId(dto.getId());
        if (location == null) {
            location = new WorkInfoDTO();
        }
        LocationDTO ld = location.getLocation();
        if (ld == null) {
            ld = new LocationDTO();
        }
        if (al == null) {
            al = new AssignmentLocation();
        }
        al.setCity(ld.getCity());
        al.setCountry(ld.getCountry());
        al.setProvince(ld.getProvince());
        al.setProvinceCode(ld.getProvinceCode());
        al.setAssignmentId(dto.getId());
        al.setDetailedAddress(location.getDetailedAddress());
        al.setZipCode(location.getZipCode());
        al.setTimeZone(location.getTimeZone());
        al.setCountryCode(ld.getCountryCode());
        locationRepository.saveAndFlush(al);
    }

    private void checkRateChangeAssignmentDate(AssignmentDetailInfoDTO dto) {
        if (dto.getType() != AssignmentType.RATE_CHANGE) {
            return;
        }
        TalentAssigment assigment;
        if (ObjectUtil.isNull(dto.getId())) {
            assigment = assignmentRepository.findLatestAssigmentByStartId(dto.getStartId());
        } else {
            assigment = assignmentRepository.findLatestAssigmentByStartIdAndisNoEqAssignmentId(dto.getStartId(), dto.getId());
        }
        if (assigment == null) {
            return;
        }
        LocalDate lastEndDate = assigment.getEndDate();
        if (!lastEndDate.plusDays(1).isEqual(dto.getStartDate())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_CHECK_RATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
    }

    private void validateStartDateAndEndDate(AssignmentDetailInfoDTO dto) {
        List<TalentAssigment> assignmentsList = null;
        if (ObjectUtil.isNull(dto.getId())) {
            assignmentsList = assignmentRepository.findByStartDateRangeByTalentId(dto.getStartDate(), dto.getEndDate(), dto.getTalentId());
            if (ObjectUtil.isNotEmpty(assignmentsList)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_CHECK_RATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        } else {
            assignmentsList = assignmentRepository.findByStartDateRangeByStartIdNotIn(dto.getStartDate(), dto.getEndDate(), dto.getTalentId(), dto.getId());
            if (ObjectUtil.isNotEmpty(assignmentsList)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_CHECK_RATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
    }

    private void validatePayRate(AssignmentDetailInfoDTO dto, BillInfoDTO bill, List<AssignmentPayRateInfo> payInfos) {
        payInfos.forEach(item -> {
            item.setContentType(PayRateContentType.BILLING);
            item.setAssignmentId(dto.getId());
            if (AssignmentStatusType.PENDING == dto.getStatus()) {
                return;
            }
            if (PayRateType.DOUBLE_TIME == item.getType() && (!bill.getIsExcept())) {
                if (ObjectUtil.isNotNull(item.getPayRate())) {
                    if (bill.getNetDoubleTimeRate() == null) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PAYRATE_DOUBLETIME_RATE_NULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                    }
                    BigDecimal doubleRate = BigDecimal.valueOf(1 - bill.getDiscountType().getValue()).multiply(item.getPayRate());
                    doubleRate = doubleRate.setScale(2, RoundingMode.HALF_UP);
                    if (doubleRate.compareTo(bill.getNetDoubleTimeRate()) != 0) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PAYRATE_DOUBLERATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                    }
                }
            } else if (PayRateType.OVER_TIME == item.getType() && (!bill.getIsExcept())) {
                if (item.getPayRate() == null) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PAYRATE_NULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
                BigDecimal doubleRate = BigDecimal.valueOf(1 - bill.getDiscountType().getValue()).multiply(item.getPayRate());
                doubleRate = doubleRate.setScale(2, RoundingMode.HALF_UP);
                if (doubleRate.compareTo(bill.getNetOverTimeRate()) != 0) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PAYRATE_OVERTIME_RATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(bill.getNetOverTimeRate().doubleValue(), item.getPayRate(), bill.getDiscountType().getValue(), doubleRate.doubleValue()), jobdivaApiPromptProperties.getJobdivaService()));
                }
            } else if (PayRateType.BILL_RATE == item.getType()) {
                if (item.getPayRate() == null) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PAYRATE_NULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
                BigDecimal doubleRate = BigDecimal.valueOf(1 - bill.getDiscountType().getValue()).multiply(item.getPayRate());
                doubleRate = doubleRate.setScale(2, RoundingMode.HALF_UP);
                if (doubleRate.compareTo(bill.getNetBillRate()) != 0) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_SAVA_PAYRATE_BILL_RATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(bill.getNetBillRate().doubleValue(), item.getPayRate(), bill.getDiscountType().getValue(), doubleRate.doubleValue()), jobdivaApiPromptProperties.getJobdivaService()));
                }
            }
        });
    }

    private CalculateTypeAndState findCalculateTypeAndStateByLocationDto(LocationDTO locationDTO) {
        CalculateTypeAndState calculateTypeAndState;
        if (Objects.equals(locationDTO.getCountryCode(), "VI")) {
            calculateTypeAndState = calculateAndStateRepository.findByStateAndCountryCode("VI", "US");
        } else if (Objects.equals(locationDTO.getCountryCode(), "PR")) {
            calculateTypeAndState = calculateAndStateRepository.findByStateAndCountryCode("PR", "US");
        } else {
            calculateTypeAndState = calculateAndStateRepository.findByStateAndCountryCode(locationDTO.getProvinceCode(), locationDTO.getCountryCode());
        }
        return calculateTypeAndState;
    }

    private void saveManager(LinkedList<Long> approvers, Long talentId, Long id) {
        List<TimeSheetManager> existManagers = managerRepository.findByAssignmentId(id).stream().filter(manager -> ManagerRoleType.AM != manager.getRole()).toList();
        if (approvers == null || approvers.isEmpty()) {
            managerRepository.deleteByIdIn(existManagers.stream().map(TimeSheetManager::getId).toList());
            return;
        }
        Map<Long, TimeSheetManager> existManagerMap = existManagers.stream().collect(Collectors.toMap(TimeSheetManager::getClientId, Function.identity(), (a, b) -> a));
        List<TimeSheetManager> removeManagers = existManagers.stream().map(TimeSheetManager::getClientId)
                .filter(clientId -> !approvers.contains(clientId))
                .map(existManagerMap::get).toList();

        if (ObjectUtil.isNotEmpty(removeManagers)) {
            managerRepository.deleteByIdIn(removeManagers.stream().map(TimeSheetManager::getId).toList());
        }
        List<TimeSheetManager> allManager = new LinkedList<>();
        for (int i = 0; i < approvers.size(); i++) {
            ManagerRoleType roleType = i == 0 ? ManagerRoleType.PRIMARY_CLIENT : ManagerRoleType.NORMAL_CLIENT;
            if (existManagerMap.containsKey(approvers.get(i))) {
                TimeSheetManager timeSheetManager = existManagerMap.get(approvers.get(i));
                timeSheetManager.setRole(roleType);
                allManager.add(timeSheetManager);
                continue;
            }
            TimeSheetManager timeSheetManager = new TimeSheetManager();
            timeSheetManager.setClientId(approvers.get(i));
            timeSheetManager.setRole(roleType);
            timeSheetManager.setTalentId(talentId);
            timeSheetManager.setAssignmentId(id);
            allManager.add(timeSheetManager);
        }
        managerRepository.saveAllAndFlush(allManager);
    }

    private void checkAssignmentAlreadyStarted(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo) {
        if (vo.getIsClockIn()) {
            AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(vo.getId());
            if (dto.getTimeSheet().getTimeSheetType() != assignmentTimeSheet.getTimeSheetType()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_TIMESHEETTYPE_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            if (dto.getTimeSheet().getFrequency() != assignmentTimeSheet.getFrequency()) {
                long count = contractorInvoiceRepository.searchNoVoidInvoiceCountByAssignmentId(vo.getId());
                if (count > 0) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_FREQUENCY_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
            }
            if (dto.getTimeSheet().getWeekEnding() != assignmentTimeSheet.getWeekEnding()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_WEEKENDING_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            if (!Objects.equals(dto.getTimeSheet().getAllowSubmitTimeSheet(), assignmentTimeSheet.getAllowSubmitTimeSheet())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_PERMISSION_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            AssignmentLocation assignmentLocation = locationRepository.findByAssignmentId(vo.getId());
            LocationDTO locationDTO = dto.getWorkLocation().getLocation();
            if (!Objects.equals(locationDTO.getCity(), assignmentLocation.getCity())
                    || !Objects.equals(locationDTO.getCountry(), assignmentLocation.getCountry())
                    || !Objects.equals(locationDTO.getCountryCode(), assignmentLocation.getCountryCode())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_LOCATION_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            AssignmentPayInfo assignmentPayInfo = payInfoRepository.findByAssignmentId(vo.getId());
            if (!Objects.equals(dto.getPayInfo().getIsExcept(), assignmentPayInfo.getIsExcept())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_PAYISEXCEPT_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            AssignmentBillInfo assignmentBillInfo = billInfoRepository.findByAssignmentId(vo.getId());
            if (!Objects.equals(dto.getBillInfo().getIsExcept(), assignmentBillInfo.getIsExcept())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_BILLISEXCEPT_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
        Integer count = expenseRecordRepository.countByAssignmentId(vo.getId());
        if (count > 0) {
            AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(vo.getId());
            if (dto.getTimeSheet().getWeekEnding() != assignmentTimeSheet.getWeekEnding()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_WEEKENDING_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
            if (!Objects.equals(dto.getTimeSheet().getAllowSubmitExpense(), assignmentTimeSheet.getAllowSubmitExpense())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ASSIGNMENT_PERMISSION_NOT_UPDATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
    }

    private void startDateAndEndDateChangHandler(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo, AssignmentTimeSheet assignmentTimeSheet) {
        if (dto.getStartDate().compareTo(vo.getStartDate()) == 0 && dto.getEndDate().compareTo(vo.getEndDate()) == 0) {
            return;
        }
        log.info("[timesheet: User @{}] REST reset change date assignmentId = {}, new startDate = [{}], new endDate = [{}], old startDate = [{}], old endDate = [{}] "
                , SecurityUtils.getUserId(), vo.getId(), dto.getStartDate(), dto.getEndDate(), vo.getStartDate(), vo.getEndDate());
        List<WeekDataVO> oldWeekDataVOList = TimeSheetUtil.getWeekEnding(vo.getStartDate(), vo.getEndDate(), assignmentTimeSheet.getWeekEnding().toDbValue(), vo.getIsWeekEnd(), assignmentTimeSheet.getFrequency());
        Map<LocalDate, WeekDataVO> oldMap = oldWeekDataVOList.stream().collect(Collectors.toMap(WeekDataVO::getWorkDate, a -> a, (a1, a2) -> a1));
        List<WeekDataVO> newWeekDataVOList = TimeSheetUtil.getWeekEnding(dto.getStartDate(), dto.getEndDate(), assignmentTimeSheet.getWeekEnding().toDbValue(), vo.getIsWeekEnd(), assignmentTimeSheet.getFrequency());
        Map<LocalDate, WeekDataVO> newMap = newWeekDataVOList.stream().collect(Collectors.toMap(WeekDataVO::getWorkDate, a -> a, (a1, a2) -> a1));
        //startDate advance
        startDateAdvance(dto, vo, oldMap, newMap);
        //startDate delay
        startDateDelay(dto, vo, oldMap, newMap);
        //endDate advance
        endDateAdvance(dto, vo, oldMap, newMap);
        //将endDate delay
        endDateDelay(dto, vo, oldMap, newMap);
    }

    /**
     * startDate in advance
     *
     * @param dto
     * @param vo
     */
    private void startDateAdvance(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo, Map<LocalDate, WeekDataVO> oldMap, Map<LocalDate, WeekDataVO> newMap) {
        if (dto.getStartDate().compareTo(vo.getStartDate()) < 0) {
            log.info("[timesheet: User @{}] REST reset startDate advance", SecurityUtils.getUserId());
            //check is the same week ending
            WeekDataVO newWeekVO = newMap.get(dto.getStartDate());
            WeekDataVO oldWeekVO = oldMap.get(vo.getStartDate());
            boolean flag = newWeekVO.getWeekEnd().isEqual(oldWeekVO.getWeekEnd());
            WeekDataVO newWeekVOByOldStartDate = newMap.get(vo.getStartDate());
            if (newWeekVOByOldStartDate != null) {
                timeSheetRecordRepository.updateWeekStartAndWeekEndByDates(TimeSheetUtil.getWeekByWeekEndingDate(newWeekVOByOldStartDate.getWeekStart(), newWeekVOByOldStartDate.getWeekEnd()), vo.getTalentId(), vo.getId(), newWeekVOByOldStartDate.getWeekStart(), newWeekVOByOldStartDate.getWeekEnd());
            }
            if (flag) {
                LocalDate resetEndDate = oldWeekVO.getWeekEnd();
                LocalDate startDate = oldWeekVO.getWeekStart();
                TimeSheetRecord timeSheetRecord = timeSheetRecordRepository.findByDate(resetEndDate, vo.getTalentId(), vo.getId());
                log.info("[timesheet: User @{}] REST reset startDate advance, resetStatus begin = [{}], end = [{}] ", SecurityUtils.getUserId(), vo.getStartDate(), resetEndDate);
                if (ObjectUtil.isNotNull(timeSheetRecord) && ObjectUtil.isNotNull(timeSheetRecord.getStatus())) {
                    TimeSheetStatus status = timeSheetRecord.getStatus();
                    if (status == TimeSheetStatus.APPLIED_APPROVE || status == TimeSheetStatus.REJECTED) {
                        deleteTimesheetOrExpenseApproveRecord(startDate, resetEndDate, vo.getTalentId(), vo.getId(), CommentsType.TIME_SHEET, null);
                        timeSheetRecordRepository.resetSubmittedDateAndStatus(startDate, resetEndDate, TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId());
                    }
                }

                List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByDateRange(resetEndDate, vo.getTalentId(), vo.getId());
                expenseRecordRepository.updateWeekStartAndWeekEndByDates(TimeSheetUtil.getWeekByWeekEndingDate(newWeekVO.getWeekStart(), newWeekVO.getWeekEnd()), vo.getTalentId(), vo.getId(), newWeekVO.getWeekStart(), newWeekVO.getWeekEnd());
                if (CollUtil.isNotEmpty(expenseRecordList)) {
                    Map<Integer, List<ExpenseRecord>> expenseIndexMap = expenseRecordList.stream().collect(Collectors.groupingBy(ExpenseRecord::getExpenseIndex));
                    expenseIndexMap.forEach((expenseIndex, list) -> {
                        ExpenseRecord expenseRecord = list.get(0);
                        TimeSheetStatus status = expenseRecord.getStatus();
                        if (status == TimeSheetStatus.APPLIED_APPROVE || status == TimeSheetStatus.REJECTED) {
                            deleteTimesheetOrExpenseApproveRecord(startDate, resetEndDate, vo.getTalentId(), vo.getId(), CommentsType.EXPENSE, expenseIndex);
                            expenseRecordRepository.resetSubmittedDateAndStatus(startDate, resetEndDate, TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId(), expenseIndex);
                        }
                    });
                }
            } else {
                LocalDate oldStartDateSubtractOneDay = vo.getStartDate().minusDays(1);
                WeekDataVO oldStartDateSubtractOneDayWeekVO = newMap.get(oldStartDateSubtractOneDay);
                if (oldStartDateSubtractOneDayWeekVO != null && !oldStartDateSubtractOneDay.isEqual(oldStartDateSubtractOneDayWeekVO.getWeekEnd())) {
                    updateResetSubmittedDateAndStatus(oldWeekVO, vo, dto);
                }
            }
        }
    }

    /**
     * startDate delay
     *
     * @param dto
     * @param vo
     */
    private void startDateDelay(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo, Map<LocalDate, WeekDataVO> oldMap, Map<LocalDate, WeekDataVO> newMap) {
        if (dto.getStartDate().compareTo(vo.getStartDate()) > 0) {
            log.info("[timesheet: User @{}] REST reset startDate delay", SecurityUtils.getUserId());
            //需要删除的endDate
            LocalDate deleteEndDate = dto.getStartDate().minusDays(1);
            boolean isReset = false;
            WeekDataVO newWeekDataVO = newMap.get(dto.getStartDate());
            WeekDataVO oldWeekDataVO = oldMap.get(vo.getStartDate());
            //重置weekStart  02-05  => 02-06
            LocalDate weekEndIng = newWeekDataVO.getWeekEnd();
            TimeSheetRecord timeSheetRecord = timeSheetRecordRepository.findByDate(weekEndIng, vo.getTalentId(), vo.getId());
            if (!ObjectUtils.isEmpty(timeSheetRecord) && ObjectUtil.isNotNull(timeSheetRecord.getStatus())) {
                isReset = true;
            }
            timeSheetRecordRepository.updateWeekStartAndWeekEndByDates(TimeSheetUtil.getWeekByWeekEndingDate(newWeekDataVO.getWeekStart(), newWeekDataVO.getWeekEnd()), vo.getTalentId(), vo.getId(), newWeekDataVO.getWeekStart(), newWeekDataVO.getWeekEnd());
            //delete
            LocalDate startDate = oldWeekDataVO.getWeekStart();
            log.info("[timesheet: User @{}] REST reset startDate delay, delete begin = [{}], end = [{}] ", SecurityUtils.getUserId(), startDate, deleteEndDate);
            deleteTimeSheetData(startDate, deleteEndDate, vo.getTalentId(), vo.getId());
            //reset
            if (isReset) {
                TimeSheetStatus status = timeSheetRecord.getStatus();
                log.info("[timesheet: User @{}] REST reset startDate delay, reset date begin = [{}], end = [{}] ", SecurityUtils.getUserId(), deleteEndDate, dto.getStartDate().minusDays(1));
                timeSheetRecordRepository.resetData(deleteEndDate, dto.getStartDate().minusDays(1), status.toDbValue(), vo.getTalentId(), vo.getId());
                breakTimeRepository.resetData(deleteEndDate, dto.getStartDate().minusDays(1), vo.getTalentId(), vo.getId());
                //Recalculate the OTDT
                //如果dto.startDate 变成了当前周期的开始时间则不触发从新计算
                if (!dto.getStartDate().isEqual(timeSheetRecord.getWeekStart())) {
                    doHandlerOtDt(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEndingDate(), vo, timeSheetRecord, dto);
                }
                if (status == TimeSheetStatus.APPLIED_APPROVE || status == TimeSheetStatus.REJECTED) {
                    //判断修改的startDate是不是刚好是一周的开始, 因为startDate在变大
                    LocalDate newStartDateSubtractOneDay = dto.getStartDate().minusDays(1);
                    WeekDataVO newStartDateSubtractOneDayWeekVO = oldMap.get(newStartDateSubtractOneDay);
                    log.info("[timesheet: User @{}] newStartDate = {}, subtractOneDay = {}, weekVo = {}", SecurityUtils.getUserId(), dto.getStartDate(), newStartDateSubtractOneDay, newStartDateSubtractOneDayWeekVO);
                    if (newStartDateSubtractOneDayWeekVO != null && !newStartDateSubtractOneDay.isEqual(newStartDateSubtractOneDayWeekVO.getWeekEnd())) {
                        log.info("[timesheet: User @{}] REST reset startDate delay, reset status begin = [{}], end = [{}] ", SecurityUtils.getUserId(), timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd());
                        deleteTimesheetOrExpenseApproveRecord(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd(), vo.getTalentId(), vo.getId(), CommentsType.TIME_SHEET, null);
                        timeSheetRecordRepository.resetSubmittedDateAndStatus(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd(), TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId());
                    }
                }
            }
            isReset = true;
            List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByDateRange(weekEndIng, vo.getTalentId(), vo.getId());
            expenseRecordRepository.updateWeekStartAndWeekEndByDates(TimeSheetUtil.getWeekByWeekEndingDate(newWeekDataVO.getWeekStart(), newWeekDataVO.getWeekEnd()), vo.getTalentId(), vo.getId(), newWeekDataVO.getWeekStart(), newWeekDataVO.getWeekEnd());
            if (CollUtil.isEmpty(expenseRecordList)) {
                isReset = false;
            }
            expenseRecordRepository.deleteByDate(startDate, deleteEndDate, vo.getTalentId(), vo.getId());
            commentsRepository.deleteAllByWorkDateBetweenAndTalentIdAndAssignmentIdAndCommentsType(startDate, deleteEndDate, vo.getTalentId(), vo.getId(), CommentsType.EXPENSE);
            expenseRecordList.stream().map(ExpenseRecord::getExpenseIndex).distinct().forEach(expenseIndex -> deleteTimesheetOrExpenseApproveRecord(startDate, deleteEndDate, vo.getTalentId(), vo.getId(), CommentsType.EXPENSE, expenseIndex));
            if (isReset && CollUtil.isNotEmpty(expenseRecordList)) {
                Map<Integer, List<ExpenseRecord>> expenseIndexMap = expenseRecordList.stream().collect(Collectors.groupingBy(ExpenseRecord::getExpenseIndex));
                expenseIndexMap.forEach((expenseIndex, list) -> {
                    ExpenseRecord expenseRecord = list.get(0);
                    TimeSheetStatus expenseRecordStatus = expenseRecord.getStatus();
                    expenseRecordRepository.resetData(deleteEndDate, dto.getStartDate().minusDays(1), expenseRecordStatus.toDbValue(), vo.getTalentId(), vo.getId(), expenseIndex);
                    if (expenseRecordStatus == TimeSheetStatus.APPLIED_APPROVE || expenseRecordStatus == TimeSheetStatus.REJECTED) {
                        LocalDate newStartDateSubtractOneDay = dto.getStartDate().minusDays(1);
                        WeekDataVO newStartDateSubtractOneDayWeekVO = oldMap.get(newStartDateSubtractOneDay);
                        if (newStartDateSubtractOneDayWeekVO != null && !newStartDateSubtractOneDay.isEqual(newStartDateSubtractOneDayWeekVO.getWeekEnd())) {
                            deleteTimesheetOrExpenseApproveRecord(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd(), vo.getTalentId(), vo.getId(), CommentsType.EXPENSE, expenseIndex);
                            expenseRecordRepository.resetSubmittedDateAndStatus(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd(), TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId(), expenseIndex);
                        }
                    }
                    expenseRecordRepository.updateLastModifyDatedByDateAndStatus(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd(), vo.getTalentId(), vo.getId(), Instant.now());
                });
            }
        }
    }

    /**
     * endDate advance
     *
     * @param dto
     * @param vo
     */
    private void endDateAdvance(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo, Map<LocalDate, WeekDataVO> oldMap, Map<LocalDate, WeekDataVO> newMap) {
        if (dto.getEndDate().compareTo(vo.getEndDate()) < 0) {
            log.info("[timesheet: User @{}] REST reset endDate advance", SecurityUtils.getUserId());
            WeekDataVO oldWeekDataVO = oldMap.get(vo.getEndDate());
            WeekDataVO newWeekDataVO = newMap.get(dto.getEndDate());
            //结束时间提前,先获取历史的结束时间
            //删除数据的开始时间
            LocalDate deleteStartDate = dto.getEndDate().plusDays(1);
            //删除数据的结束时间
            LocalDate deleteEndDate = oldWeekDataVO.getWeekEnd();
            boolean isReset = false;
            TimeSheetRecord timeSheetRecord = timeSheetRecordRepository.findByDate(newWeekDataVO.getWeekEnd(), vo.getTalentId(), vo.getId());
            if (!ObjectUtils.isEmpty(timeSheetRecord) && !ObjectUtils.isEmpty(timeSheetRecord.getStatus())) {
                isReset = true;
                deleteStartDate = timeSheetRecord.getWeekEnd().plusDays(1);
            }
            log.info("[timesheet: User @{}] REST reset endDate advance, delete date begin = [{}], end = [{}]", SecurityUtils.getUserId(), deleteStartDate, deleteEndDate);
            deleteTimeSheetData(deleteStartDate, deleteEndDate, vo.getTalentId(), vo.getId());
            if (isReset) {
                TimeSheetStatus status = timeSheetRecord.getStatus();
                log.info("[timesheet: User @{}] REST reset endDate advance, reset date begin = [{}], end = [{}]", SecurityUtils.getUserId(), dto.getEndDate().plusDays(1), deleteStartDate);
                timeSheetRecordRepository.resetData(dto.getEndDate().plusDays(1), newWeekDataVO.getWeekEnd(), status.toDbValue(), vo.getTalentId(), vo.getId());
                breakTimeRepository.resetData(dto.getEndDate().plusDays(1), newWeekDataVO.getWeekEnd(), vo.getTalentId(), vo.getId());
                //Recalculate the OTDT
                if (!dto.getEndDate().isEqual(timeSheetRecord.getWeekEnd())) {
                    doHandlerOtDt(TimeSheetUtil.findDateInWeek(newWeekDataVO.getWorkDate(), dto.getTimeSheet().getWeekEnding().toDbValue(), 2), newWeekDataVO.getWeekendingDate(), vo, timeSheetRecord, dto);
                }
                if (status == TimeSheetStatus.APPLIED_APPROVE || status == TimeSheetStatus.REJECTED) {
                    if (!dto.getEndDate().isEqual(newWeekDataVO.getWeekEnd())) {
                        log.info("[timesheet: User @{}] REST reset endDate advance, reset status begin = [{}], end = [{}]", SecurityUtils.getUserId(), timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd());
                        deleteTimesheetOrExpenseApproveRecord(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd(), vo.getTalentId(), vo.getId(), CommentsType.TIME_SHEET, null);
                        timeSheetRecordRepository.resetSubmittedDateAndStatus(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd(), TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId());
                    }
                }
            }
            List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByDateRange(newWeekDataVO.getWeekEnd(), vo.getTalentId(), vo.getId());
            if (CollUtil.isNotEmpty(expenseRecordList)) {
                ExpenseRecord expenseRecord = expenseRecordList.get(0);
                isReset = true;
                deleteStartDate = expenseRecord.getWeekEnd().plusDays(1);
            } else {
                isReset = false;
            }
            expenseRecordRepository.deleteByDate(deleteStartDate, deleteEndDate, vo.getTalentId(), vo.getId());
            commentsRepository.deleteAllByWorkDateBetweenAndTalentIdAndAssignmentIdAndCommentsType(deleteStartDate, deleteEndDate, vo.getTalentId(), vo.getId(), CommentsType.EXPENSE);
            LocalDate finalDeleteStartDate = deleteStartDate;
            expenseRecordList.stream().map(ExpenseRecord::getExpenseIndex).distinct().forEach(expenseIndex -> deleteTimesheetOrExpenseApproveRecord(finalDeleteStartDate, deleteEndDate, vo.getTalentId(), vo.getId(), CommentsType.EXPENSE, expenseIndex));
            if (isReset && CollUtil.isNotEmpty(expenseRecordList)) {
                Map<Integer, List<ExpenseRecord>> expenseIndexMap = expenseRecordList.stream().collect(Collectors.groupingBy(ExpenseRecord::getExpenseIndex));
                expenseIndexMap.forEach((expenseIndex, list) -> {
                    ExpenseRecord expenseRecord = list.get(0);
                    TimeSheetStatus status = expenseRecord.getStatus();
                    expenseRecordRepository.resetData(dto.getEndDate().plusDays(1), expenseRecord.getWeekEnd(), status.toDbValue(), vo.getTalentId(), vo.getId(), expenseIndex);
                    if (status == TimeSheetStatus.APPLIED_APPROVE || status == TimeSheetStatus.REJECTED) {
                        if (!dto.getEndDate().isEqual(newWeekDataVO.getWeekEnd())) {
                            deleteTimesheetOrExpenseApproveRecord(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd(), vo.getTalentId(), vo.getId(), CommentsType.EXPENSE, expenseIndex);
                            expenseRecordRepository.resetSubmittedDateAndStatus(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd(), TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId(), expenseIndex);
                        }
                    }
                    expenseRecordRepository.updateLastModifyDatedByDateAndStatus(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd(), vo.getTalentId(), vo.getId(), Instant.now());
                });
            }
        }
    }

    /**
     * endDate delay
     *
     * @param dto
     * @param vo
     */
    private void endDateDelay(AssignmentDetailInfoDTO dto, AssignmentGeneralInfoVO vo, Map<LocalDate, WeekDataVO> oldMap, Map<LocalDate, WeekDataVO> newMap) {
        if (dto.getEndDate().compareTo(vo.getEndDate()) > 0) {
            log.info("[timesheet: User @{}] REST reset endDate delay", SecurityUtils.getUserId());
            WeekDataVO newWeekVO = newMap.get(dto.getEndDate());
            WeekDataVO oldWeekVO = oldMap.get(vo.getEndDate());
            //同一周内
            boolean flag = newWeekVO.getWeekEnd().isEqual(oldWeekVO.getWeekEnd());
            if (flag) {
                LocalDate startDate = oldWeekVO.getWeekStart();
                LocalDate endDate = oldWeekVO.getWeekEnd();
                TimeSheetRecord timeSheetRecord = timeSheetRecordRepository.findByDate(endDate, vo.getTalentId(), vo.getId());
                if (!ObjectUtils.isEmpty(timeSheetRecord) && !ObjectUtils.isEmpty(timeSheetRecord.getStatus())) {
                    if (timeSheetRecord.getStatus() == TimeSheetStatus.APPLIED_APPROVE || timeSheetRecord.getStatus() == TimeSheetStatus.REJECTED) {
                        log.info("[timesheet: User @{}] REST reset endDate delay, reset begin = [{}], end = [{}]", SecurityUtils.getUserId(), startDate, endDate);
                        deleteTimesheetOrExpenseApproveRecord(startDate, endDate, vo.getTalentId(), vo.getId(), CommentsType.TIME_SHEET, null);
                        timeSheetRecordRepository.resetSubmittedDateAndStatus(startDate, endDate, TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId());
                    }
                }
                List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByDateRange(endDate, vo.getTalentId(), vo.getId());
                if (CollUtil.isNotEmpty(expenseRecordList)) {
                    Map<Integer, List<ExpenseRecord>> expenseIndexMap = expenseRecordList.stream().collect(Collectors.groupingBy(ExpenseRecord::getExpenseIndex));
                    expenseIndexMap.forEach((expenseIndex, list) -> {
                        ExpenseRecord expenseRecord = list.get(0);
                        if (expenseRecord.getStatus() == TimeSheetStatus.APPLIED_APPROVE || expenseRecord.getStatus() == TimeSheetStatus.REJECTED) {
                            deleteTimesheetOrExpenseApproveRecord(startDate, endDate, vo.getTalentId(), vo.getId(), CommentsType.EXPENSE, expenseIndex);
                            expenseRecordRepository.resetSubmittedDateAndStatus(startDate, endDate, TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId(), expenseIndex);
                        }
                    });
                }
            } else {
                if (!vo.getEndDate().isEqual(oldWeekVO.getWeekEnd())) {
                    updateResetSubmittedDateAndStatus(oldWeekVO, vo, dto);
                }
            }
        }
    }

    private LocalDate getWeekEndDay(LocalDate localDate, Integer setWeekEnd) {
        int week = localDate.getDayOfWeek().getValue();
        int diff = setWeekEnd - week;
        if (diff >= 0) {
            return localDate.plus(diff, ChronoUnit.DAYS);
        }
        return localDate.plus(diff + 7, ChronoUnit.DAYS);
    }

    private void deleteTimesheetOrExpenseApproveRecord(LocalDate startDate, LocalDate resetEndDate, Long talentId, Long id, CommentsType type, Integer expenseIndex) {
        List<Long> recordIdList;
        switch (type) {
            case TIME_SHEET:
                recordIdList = timeSheetRecordRepository.findByDateAndTalentIdAndAssignmentId(startDate, resetEndDate, talentId, id);
                break;
            case EXPENSE:
                recordIdList = expenseRecordRepository.findByDateAndTalentIdAndAssignmentId(startDate, resetEndDate, talentId, id, expenseIndex);
                break;
            default:
                recordIdList = new ArrayList<>();
                break;
        }
        if (CollUtil.isNotEmpty(recordIdList)) {
            if (recordIdList.size() > SqlUtil.PARTITION_COUNT_999) {
                CollUtil.split(recordIdList, SqlUtil.PARTITION_COUNT_999).forEach(list -> approveRecordRepository.deleteAllByRecordIdInAndType(list, type.toDbValue()));
            } else {
                approveRecordRepository.deleteAllByRecordIdInAndType(recordIdList, type.toDbValue());
            }
        }
    }

    private void updateResetSubmittedDateAndStatus(WeekDataVO weekDataVO, AssignmentGeneralInfoVO vo, AssignmentDetailInfoDTO dto) {
        List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByDateRange(weekDataVO.getWeekEnd(), vo.getTalentId(), vo.getId());
        if (CollUtil.isNotEmpty(expenseRecordList)) {
            Map<Integer, List<ExpenseRecord>> expenseIndexMap = expenseRecordList.stream().collect(Collectors.groupingBy(ExpenseRecord::getExpenseIndex));
            expenseIndexMap.forEach((expenseIndex, list) -> {
                ExpenseRecord expenseRecord = list.get(0);
                TimeSheetStatus status = expenseRecord.getStatus();
                if (status == TimeSheetStatus.APPLIED_APPROVE || status == TimeSheetStatus.REJECTED) {
                    deleteTimesheetOrExpenseApproveRecord(weekDataVO.getWeekStart(), weekDataVO.getWeekEnd(), vo.getTalentId(), vo.getId(), CommentsType.EXPENSE, expenseIndex);
                    expenseRecordRepository.resetSubmittedDateAndStatus(weekDataVO.getWeekStart(), weekDataVO.getWeekEnd(), TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId(), expenseIndex);
                }
            });
        }
        TimeSheetRecord timeSheetRecord = timeSheetRecordRepository.findByDate(weekDataVO.getWeekEnd(), vo.getTalentId(), vo.getId());
        if (ObjectUtil.isNotNull(timeSheetRecord) && ObjectUtil.isNotNull(timeSheetRecord.getStatus())) {
            if (timeSheetRecord.getStatus() == TimeSheetStatus.APPLIED_APPROVE || timeSheetRecord.getStatus() == TimeSheetStatus.REJECTED) {
                deleteTimesheetOrExpenseApproveRecord(weekDataVO.getWeekStart(), weekDataVO.getWeekEnd(), vo.getTalentId(), vo.getId(), CommentsType.TIME_SHEET, null);
                timeSheetRecordRepository.resetSubmittedDateAndStatus(weekDataVO.getWeekStart(), weekDataVO.getWeekEnd(), TimeSheetStatus.DRAFT.toDbValue(), vo.getTalentId(), vo.getId());
            }
        }
    }

    private void deleteTimeSheetData(LocalDate startDate, LocalDate endDate, Long talentId, Long id) {
        timeSheetRecordRepository.deleteByDate(startDate, endDate, talentId, id);
        breakTimeRepository.deleteByDate(startDate, endDate, talentId, id);
        commentsRepository.deleteAllByWorkDateBetweenAndTalentIdAndAssignmentIdAndCommentsType(startDate, endDate, talentId, id, CommentsType.TIME_SHEET);
        deleteTimesheetOrExpenseApproveRecord(startDate, endDate, talentId, id, CommentsType.TIME_SHEET, null);
        //delete holiday
        timeSheetHolidayRecordRepository.deleteHolidayByWeekEnd(id, startDate, endDate);
    }

    /**
     * @param startDate
     * @param endDate
     * @param vo
     * @param timeSheetRecord
     */
    private void doHandlerOtDt(LocalDate startDate, LocalDate endDate, AssignmentGeneralInfoVO vo, TimeSheetRecord timeSheetRecord, AssignmentDetailInfoDTO dto) {
        entityManager.clear();
        List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByDate(startDate, endDate, vo.getTalentId(), vo.getId());
        AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(vo.getId());
        if (dto.getBillInfo().getIsExcept() || dto.getBillInfo().getOvertimeType() == OverTimeType.MANUALLY) {
            return;
        }
        OtDtUtil.calculateOtDtByLocationAndTimeSheetType(timeSheetRecordList, timeSheetRecord.getTimeSheetType(), assignmentTimeSheet.getCalculateType());
        timeSheetRecordRepository.saveAllAndFlush(timeSheetRecordList);
        timeSheetRecordList.stream().filter(timeSheetRecord1 -> timeSheetRecord1.getWorkDate().isEqual(timeSheetRecord1.getWeekEnd())).forEach(weekendRecord ->
                timeSheetRecordRepository.updateLastModifiedDateById(CollUtil.newArrayList(weekendRecord.getId()), Instant.now()));
    }

    /**
     * Recalculate the OTDT
     * 涉及到切割周的时候，存在 approved 和 draft 混合数据，这个时候approved 需要变成pending 状态
     *
     * @param startDate
     * @param endDate
     * @param vo
     * @param timeSheetRecord
     * @param calculateMethodType
     */
    private void doHandlerOtDtByDraft(LocalDate startDate, LocalDate endDate, AssignmentGeneralInfoVO vo, TimeSheetRecord timeSheetRecord, CalculateMethodType calculateMethodType) {
        entityManager.clear();
        List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByDate(startDate, endDate, vo.getTalentId(), vo.getId());
        OtDtUtil.calculateOtDtByLocationAndTimeSheetType(timeSheetRecordList, timeSheetRecord.getTimeSheetType(), calculateMethodType);
        timeSheetRecordRepository.saveAllAndFlush(timeSheetRecordList);
        //ot,dt 计算完成后, approved 的数据需要变成pending 状态
        timeSheetRecordList.stream().filter(timeSheetRecord1 -> Objects.equals(TimeSheetStatus.APPROVED, timeSheetRecord1.getStatus())
                && timeSheetRecord1.getWorkDate().isEqual(timeSheetRecord1.getWeekEnd())).findAny().ifPresent(approvedWeekendRecord -> {
            //删除数据重新生成
            timeSheetRecordRepository.deleteById(approvedWeekendRecord.getId());
            TimeSheetRecord pendingWeekEndRecord = new TimeSheetRecord();
            BeanUtil.copyProperties(approvedWeekendRecord, pendingWeekEndRecord, "id", "status");
            //approved 的数据修改为pending
            pendingWeekEndRecord.setStatus(TimeSheetStatus.APPLIED_APPROVE);
            timeSheetRecordRepository.updateStatusByDates(TimeSheetUtil.getWeekByWeekEndingDate(pendingWeekEndRecord.getWeekStart(), pendingWeekEndRecord.getWeekEnd()), vo.getTalentId(), TimeSheetStatus.APPLIED_APPROVE.toDbValue(), vo.getId());
            //重新生成最后一天weekEnd 的数据, 重新生成数据后id变化了,不需要删除 审批的数据
            timeSheetRecordRepository.save(pendingWeekEndRecord);
            TimeSheetUtil.setWeekId(List.of(pendingWeekEndRecord));
        });
        timeSheetRecordList.stream().filter(timeSheetRecord1 ->  !Objects.equals(TimeSheetStatus.APPROVED, timeSheetRecord1.getStatus())
                && timeSheetRecord1.getWorkDate().isEqual(timeSheetRecord1.getWeekEnd())).forEach(weekendRecord ->
                timeSheetRecordRepository.updateLastModifiedDateById(CollUtil.newArrayList(weekendRecord.getId()), Instant.now()));
    }

    private void deleteInfo(Long assignmentId) {
        billInfoRepository.deleteByAssignmentId(assignmentId);
        payInfoRepository.deleteByAssignmentId(assignmentId);
        contributionRepository.deleteByAssignmentId(assignmentId);
        managerRepository.deleteByAssignmentId(assignmentId);
        timeSheetRepository.deleteByAssignmentId(assignmentId);
        payRateRepository.deleteByAssignmentId(assignmentId);
        locationRepository.deleteByAssignmentId(assignmentId);
    }

    private AssignmentGeneralInfoVO findNearByAssignment(List<TalentAssigment> assignments, Long startId) {
        LocalDate recently = LocalDate.now();
        if (assignments.size() == 1) {
            recently = assignments.get(0).getStartDate();
        }
        AssignmentGeneralInfoVO vo = assignmentRepository.findByDateIn(recently, startId);
        if (vo != null) {
            return vo;
        }
        Long min = null;
        LocalDate now = LocalDate.now();
        for (TalentAssigment talentAssigment : assignments) {
            if (talentAssigment.getStartDate().isEqual(now) || talentAssigment.getEndDate().isEqual(now) ||
                    (talentAssigment.getStartDate().isBefore(now) && talentAssigment.getEndDate().isAfter(now))) {
                recently = talentAssigment.getStartDate();
                break;
            }
            long startAbs = Math.abs(talentAssigment.getStartDate().toEpochDay() - now.toEpochDay());
            long endAbs = Math.abs(talentAssigment.getEndDate().toEpochDay() - now.toEpochDay());
            long assignmentMin = Math.min(startAbs, endAbs);
            if (min == null || min >= assignmentMin) {
                min = assignmentMin;
                recently = talentAssigment.getStartDate();
            }
        }
        return assignmentRepository.findByDateIn(recently, startId);
    }

    private BillInfoDTO getBillInfo(Long assignmentId) {
        AssignmentBillInfo billInfo = billInfoRepository.findByAssignmentId(assignmentId);
        BillInfoDTO dto = new BillInfoDTO();
        ServiceUtils.myCopyProperties(billInfo, dto);
        List<AssignmentPayRateInfo> payRateInfos = payRateRepository.findByAssignmentIdAndContentType(assignmentId, PayRateContentType.BILLING);
        dto.setPayRateInfo(payRateInfos);
        return dto;
    }

    private List<AssignmentContribution> getContribution(Long assignmentId) {
        return contributionRepository.findByAssignmentId(assignmentId);
    }

    private PayInfoDTO getPayInfo(Long assignmentId) {
        PayInfoDTO dto = new PayInfoDTO();
        AssignmentPayInfo payInfo = payInfoRepository.findByAssignmentId(assignmentId);
        ServiceUtils.myCopyProperties(payInfo, dto);
        List<AssignmentPayRateInfo> payRateInfos = payRateRepository.findByAssignmentIdAndContentType(assignmentId, PayRateContentType.PAY);
        dto.setPayRateInfo(payRateInfos);
        return dto;
    }

    private TimeSheetInfoDTO getTimeSheet(Long assignmentId) {
        TimeSheetInfoDTO dto = new TimeSheetInfoDTO();
        AssignmentTimeSheet tm = timeSheetRepository.findByAssignmentId(assignmentId);
        List<TimeSheetManager> approvers = managerRepository.findClientApprover(assignmentId);
        LinkedList<Long> approverIds = new LinkedList<>();
        if (CollUtil.isNotEmpty(approvers)) {
            approvers.forEach(item -> approverIds.add(item.getClientId()));
        }
        ServiceUtils.myCopyProperties(tm, dto);
        dto.setApprovers(approverIds);
        dto.setCalculateMethodType(tm.getCalculateType());
        return dto;
    }

    private WorkInfoDTO getLocation(Long assignmentId) {
        AssignmentLocation location = locationRepository.findByAssignmentId(assignmentId);
        if (location == null) {
            return null;
        }
        LocationDTO locationDTO = new LocationDTO();
        WorkInfoDTO dto = new WorkInfoDTO();
        ServiceUtils.myCopyProperties(location, locationDTO);
        ServiceUtils.myCopyProperties(location, dto);
        dto.setLocation(locationDTO);
        return dto;
    }

    private Integer getExtensionAssignmentOrder(Long assignmentId, Long startId) {
        List<TalentAssigment> assignments = assignmentRepository.findByType(startId, AssignmentType.EXTENSION.toDbValue());
        if (CollUtil.isEmpty(assignments)) {
            return 0;
        }
        for (int i = 0; i < assignments.size(); i++) {
            if (assignments.get(i).getId().longValue() == assignmentId.longValue()) {
                return i + 1;
            }
        }
        return 0;
    }

}
