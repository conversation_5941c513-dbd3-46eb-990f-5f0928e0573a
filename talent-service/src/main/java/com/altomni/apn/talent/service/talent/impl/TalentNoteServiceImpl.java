package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.constants.TalentRecruitmentProcessConstants;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.domain.talent.TalentVoiceMessageNote;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.talent.CreateTalentNoteDTO;
import com.altomni.apn.common.dto.talent.CreateTalentVoiceMessageNoteDTO;
import com.altomni.apn.common.dto.talent.TalentNoteDTO;
import com.altomni.apn.common.dto.talent.TalentReviewNoteDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.voip.PhoneRecordingRequestDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.enumeration.ReviewedByType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.user.SimpleUserRepository;
import com.altomni.apn.common.service.voipserver.VoipProxyAPIHttp;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.voip.VoipContactDetailVO;
import com.altomni.apn.talent.constants.Constants;
import com.altomni.apn.talent.constants.EnrichStatusEnum;
import com.altomni.apn.talent.constants.TalentNoteSearchType;
import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.domain.record.TalentTrackingNote;
import com.altomni.apn.talent.domain.talent.TalentReviewNote;
import com.altomni.apn.talent.repository.job.JobBriefRepository;
import com.altomni.apn.talent.repository.record.TalentTrackingNoteRepository;
import com.altomni.apn.talent.repository.talent.TalentNoteRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.repository.talent.TalentReviewNoteRepository;
import com.altomni.apn.talent.repository.talent.TalentVoiceMessageNoteRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.application.AllNotesVO;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.application.NodeAndNote;
import com.altomni.apn.talent.service.application.TalentRecruitmentProcessForTalentVOSimple;
import com.altomni.apn.talent.service.folder.impl.TalentRelateJobFolderServiceImpl;
import com.altomni.apn.talent.service.job.JobService;
import com.altomni.apn.talent.service.rabbitmq.listener.NoteEnrichListener;
import com.altomni.apn.talent.service.talent.TalentNoteService;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.service.voip.VoipService;
import com.altomni.apn.talent.web.rest.talent.dto.TopTalentNoteDTO;
import com.altomni.apn.user.config.TopNoteConfig;
import com.altomni.apn.user.config.UserPersonalizationConfig;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.repository.permission.PermissionTeamLeaderRepository;
import com.altomni.apn.user.repository.permission.PermissionTeamRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.zalando.problem.Status;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class TalentNoteServiceImpl implements TalentNoteService {

    @Resource
    private NoteEnrichListener noteEnrichListener;

    @Resource
    private TalentNoteRepository talentNoteRepository;

    @Resource
    private TalentVoiceMessageNoteRepository talentVoiceMessageNoteRepository;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private JobBriefRepository jobBriefRepository;

    @Resource
    private VoipService voipService;

    @Resource
    private JobService jobService;

    @Resource
    private VoipProxyAPIHttp voipProxyAPIHttp;

    private static final String PHONE_RECORDING_CHECK_POST = "/api/v1/connect/phone-recording/check-exist";


    @Override
    @Transactional
    public void deleteTalentNote(Long id) {
        TalentNote existing = talentNoteRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("The note to change can not be found"));
        if (!existing.getUserId().equals(SecurityUtils.getUserId())){
            throw new CustomParameterizedException("You are not authorized to change the note");
        }
        Long talentId = existing.getTalentId();
        //获取删除时的talentId
        Optional<TalentV3> talent = talentRepository.findById(talentId);
        if(talent.isPresent()) {
            TalentV3 talentV3 = talent.get();
            talentV3.setLastEditedTime(Instant.now());
            talentRepository.saveAndFlush(talentV3);
        }
        talentNoteRepository.deleteById(id);
    }

    @Override
    @Transactional
    public TalentNote updateTalentNote(CreateTalentNoteDTO talentNoteInput) {
        TalentNote talentNote = new TalentNote();
        BeanUtil.copyProperties(talentNoteInput, talentNote);
        String note = talentNote.getNote();
        if(StringUtils.isNotEmpty(note) && note.length() > Constants.NOTE_MAX_LENGTH) {
            throw new CustomParameterizedException("Data too long for note.");
        }
        TalentNote existing = talentNoteRepository.findById(talentNote.getId()).orElseThrow(() -> new CustomParameterizedException("The note to change can not be found"));
        //add admin authority
        if (!existing.getUserId().equals(SecurityUtils.getUserId()) && !SecurityUtils.isAdmin()){
            throw new CustomParameterizedException("You are not authorized to change the note");
        }
        if(!checkICIUnique(talentNote)) {
            throw new CustomParameterizedException("ICI type note only one.");
        }
        if(talentNote.getNoteType().equals(TalentNoteType.CALL_CANDIDATE_VOICE_MESSAGE)) talentNote.setReadStatus(true);
        ServiceUtils.myCopyProperties(talentNote, existing, TalentNote.UpdateSkipProperties);
        existing.setNoteStatus(talentNote.getNoteStatus());
        TalentNote result = talentNoteRepository.save(existing);
        TalentV3 talent = talentRepository.getById(result.getTalentId());
        talent.setLastEditedTime(Instant.now());
        talentRepository.save(talent);
        notifyNoteEnrich(result);
        return result;
    }

    private void notifyNoteEnrich(TalentNote result) {
        TalentNoteType noteType = result.getNoteType();
        if(noteType == null) {
            return;
        }
        switch (noteType) {
            case CALL_CANDIDATES:
                Map<String, Object> additionalInfo = result.getAdditionalInfo();
                if(additionalInfo != null && additionalInfo.containsKey("phoneCallId")) {
                    noteEnrichListener.sendNoteEnrichNotify(getCallNoteNotify(result));
                } else {
                    noteEnrichListener.sendNoteEnrichNotify(getNormalNoteNotify(result));
                }
                talentNoteRepository.updateEnrich(result.getId(), null, null);
                break;
            case CONSULTANT_INTERVIEW:
            case OTHERS:
            case EMAIL_CANDIDATES:
            case CALL_VIDEO:
            case ICI:
                noteEnrichListener.sendNoteEnrichNotify(getNormalNoteNotify(result));
                talentNoteRepository.updateEnrich(result.getId(), null, null);
                break;
        }

    }

    private JSONObject getCallNoteNotify(TalentNote result) {
        JSONObject jsonObject = new JSONObject();
        JSONObject source = new JSONObject();

        jsonObject.put("_talent_id", result.getTalentId());
        Long tenantId = talentRepository.findTenantIdById(result.getTalentId());
        jsonObject.put("_tenant_id", tenantId);
        jsonObject.put("_note_id", result.getId());

        // 构建source结构
        source.put("type", TalentNoteSearchType.CALL_NOTE);

        // 构建data结构
        source.put("data", convertAdditionalInfo(result));

        jsonObject.put("_source", source);
        return jsonObject;
    }

    public JSONObject convertAdditionalInfo(TalentNote result) {
        Map<String, Object> additionalInfo = result.getAdditionalInfo();
        JSONObject data = new JSONObject();
        // 构建data结构
        if (StringUtils.isNotBlank(result.getTitle())) {
            data.put("title", result.getTitle());
        }
        if (StringUtils.isNotBlank(result.getNote())) {
            data.put("note", result.getNote());
        }

        // 1. 检查顶层Map是否有效
        if (additionalInfo == null || additionalInfo.isEmpty()) {
            return data;
        }

        // 2. 处理summary节点
        if (!additionalInfo.containsKey("summary")) {
            return data;
        }

        Object summaryObj = additionalInfo.get("summary");
        if (!(summaryObj instanceof Map)) {
            return data;
        }
        Map<String, Object> summary = (Map<String, Object>) summaryObj;

        // 3. 处理notes数组
        if (summary.containsKey("notes") && summary.get("notes") instanceof List) {
            List<?> originNotes = (List<?>) summary.get("notes");
            JSONArray notesArray = new JSONArray();

            for (Object noteObj : originNotes) {
                if (!(noteObj instanceof Map)) continue;
                Map<String, Object> note = (Map<String, Object>) noteObj;

                JSONObject newNote = new JSONObject();
                // 处理topic字段
                if (note.containsKey("topic")) {
                    newNote.put("topic", note.get("topic"));
                }

                // 处理content数组
                if (note.containsKey("content") && note.get("content") instanceof List) {
                    List<?> contents = (List<?>) note.get("content");
                    JSONArray contentArray = new JSONArray();

                    for (Object contentObj : contents) {
                        if (!(contentObj instanceof Map)) continue;
                        Map<String, Object> content = (Map<String, Object>) contentObj;

                        JSONObject newContent = new JSONObject();
                        if (content.containsKey("summary")) {
                            newContent.put("summary", content.get("summary"));
                        }
                        if (content.containsKey("subTopic")) {
                            newContent.put("subTopic", content.get("subTopic"));
                        }
                        if (content.containsKey("conclusions") &&
                                content.get("conclusions") instanceof List) {
                            newContent.put("conclusions", content.get("conclusions"));
                        }
                        contentArray.put(newContent);
                    }
                    newNote.put("content", contentArray);
                }
                notesArray.put(newNote);
            }
            data.put("notes", notesArray);
        }

        return data;
    }

    private JSONObject getNormalNoteNotify(TalentNote result) {
        JSONObject jsonObject = new JSONObject();
        JSONObject source = new JSONObject();
        JSONObject data = new JSONObject();

        jsonObject.put("_talent_id", result.getTalentId());
        Long tenantId = talentRepository.findTenantIdById(result.getTalentId());
        jsonObject.put("_tenant_id", tenantId);
        jsonObject.put("_note_id", result.getId());

        // 构建source结构
        source.put("type", TalentNoteSearchType.FOLLOW_UP_NOTE);

        // 构建data结构
        if (StringUtils.isNotBlank(result.getTitle())) {
            data.put("title", result.getTitle());
        }
        if (StringUtils.isNotBlank(result.getNote())) {
            data.put("note", result.getNote());
        }

        source.put("data", data);
        jsonObject.put("_source", source);
        return jsonObject;
    }

    @Override
    public TalentNote createTalentNote(CreateTalentNoteDTO talentNoteInput) {
        TalentNote talentNote = new TalentNote();
        BeanUtil.copyProperties(talentNoteInput, talentNote);
        String note = talentNote.getNote();
        if(StringUtils.isNotEmpty(note) && note.length() > Constants.NOTE_MAX_LENGTH) {
            throw new CustomParameterizedException("Data too long for note.");
        }
        if (talentNote.getId() != null) {
            throw new CustomParameterizedException("The object to create cannot already have an id.");
        }
        Long talentId = talentNote.getTalentId();
        TalentV3 talent = talentRepository.getById(talentId);
        //if getIsSystem is true, it represents that the talent note will be created by system, and it'll ignore tenant authority
        if (!talentNoteInput.getIsSystem() && !SecurityUtils.isCurrentTenant(talent.getTenantId())){
            throw new CustomParameterizedException("No Permission: The talent note to create does not belong to your tenant.");
        }
        if(!checkICIUnique(talentNote)) {
            throw new CustomParameterizedException("ICI type note only one.");
        }
        //check call candidate note if phone call recording doesn't exist, isAudio set false
        if(talentNote.getNoteType().equals(TalentNoteType.CALL_CANDIDATES) && talentNote.getAdditionalInfo() != null) {
            Map<String,Object> additionalInfo = talentNote.getAdditionalInfo();
            if(additionalInfo.get("summary") != null) {
                JSONObject summary = JSONUtil.parseObj(additionalInfo.get("summary"));
                Boolean isAudio = summary.getBool("isAudio");
                if(isAudio != null && isAudio) {
                    String phoneCallId = additionalInfo.get("phoneCallId")!= null ? additionalInfo.get("phoneCallId").toString() : null;
                    if(phoneCallId != null) {
                        VoipContactDTO voipContactDTO = voipService.getVoipContact(phoneCallId).getBody();
                        if(voipContactDTO != null) {
                            UserBriefDTO userBriefDTO = userService.findUserBriefById(voipContactDTO.getUserId()).getBody();
                            if(userBriefDTO != null) {
                                PhoneRecordingRequestDTO request = new PhoneRecordingRequestDTO(phoneCallId, userBriefDTO.getEmail(), voipContactDTO.getCreatedDate());
                                Boolean exist = voipProxyAPIHttp.proxyApiRequest(PHONE_RECORDING_CHECK_POST, request, HttpMethod.POST, Boolean.class).getBody();
                                if(exist != null && !exist) {
                                    summary.put("isAudio", false);
                                    additionalInfo.put("summary", summary);
                                    talentNote.setAdditionalInfo(additionalInfo);
                                }
                            }
                        }
                    }
                }
            }
        }
        TalentNote result = talentNoteRepository.save(talentNote);
        Integer noteStatus = result.getNoteStatus();
        if(noteStatus != null) {
            talent.setMotivationId(noteStatus);
            talent.setLastEditedTime(Instant.now());
            talentRepository.save(talent);
        }
        notifyNoteEnrich(result);
        return result;
    }



    /**
     * !!! mention TalentVoiceMessageNote is different from TalentNote
     * */
    @Override
    public TalentVoiceMessageNote createTalentVoiceMessageNote(CreateTalentVoiceMessageNoteDTO talentNoteInput) {
        if(!talentNoteInput.getNoteType().equals(TalentNoteType.CALL_CANDIDATE_VOICE_MESSAGE)) throw new CustomParameterizedException("The note type is wrong! Only support for voice message note.");
        TalentVoiceMessageNote talentVoiceMessageNote = new TalentVoiceMessageNote();
        BeanUtil.copyProperties(talentNoteInput, talentVoiceMessageNote);
        String note = talentVoiceMessageNote.getNote();
        if(StringUtils.isNotEmpty(note) && note.length() > Constants.NOTE_MAX_LENGTH) {
            throw new CustomParameterizedException("Data too long for note.");
        }
        if (talentVoiceMessageNote.getId() != null) {
            throw new CustomParameterizedException("The object to create cannot already have an id.");
        }
        Long talentId = talentVoiceMessageNote.getTalentId();
        TalentV3 talent = talentRepository.getById(talentId);
        //if getIsSystem is true, it represents that the talent note will be created by system, and it'll ignore tenant authority
        if (!talentNoteInput.getIsSystem() && !SecurityUtils.isCurrentTenant(talent.getTenantId())){
            throw new CustomParameterizedException("No Permission: The talent note to create does not belong to your tenant.");
        }
        TalentVoiceMessageNote result = talentVoiceMessageNoteRepository.save(talentVoiceMessageNote);
        return result;
    }

    private boolean checkICIUnique(TalentNote talentNote) {
        if(!TalentNoteType.ICI.equals(talentNote.getNoteType())) {
            return true;
        }
        List<TalentNote> allByTalentId = talentNoteRepository.findAllByTalentId(talentNote.getTalentId());
        for(TalentNote note : allByTalentId) {
            if(!note.getId().equals(talentNote.getId()) && TalentNoteType.ICI.equals(note.getNoteType())) {
                return false;
            }
        }
        return true;
    }

    @Resource
    private TalentReviewNoteRepository reviewNoteRepository;

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public TalentReviewNoteDTO createTalentReviewNote(TalentReviewNoteDTO talentReviewNoteDTO) {
        if(existRepeatReviewStatus(talentReviewNoteDTO, null)) {
            throw new CustomParameterizedException("The review note already exists");
        }
        TalentReviewNote note = new TalentReviewNote();
        note.setReviewedByType(talentReviewNoteDTO.getReviewedByType());
        note.setTalentId(talentReviewNoteDTO.getTalentId());
        note.setReviewedBy(talentReviewNoteDTO.getReviewedBy());
        note.setReviewedDate(LocalDateTime.parse(talentReviewNoteDTO.getReviewedDate(), formatter));
        note.setTimezone(talentReviewNoteDTO.getTimezone());
        note.setNote(talentReviewNoteDTO.getNote());
        TalentReviewNote talentReviewNote = reviewNoteRepository.saveAndFlush(note);
        return getTalentReviewNoteDTO(talentReviewNote);
    }

    @Override
    public TalentReviewNoteDTO updateTalentReviewNote(Long id, TalentReviewNoteDTO talentReviewNoteDTO) {
        if(existRepeatReviewStatus(talentReviewNoteDTO, id)) {
            throw new CustomParameterizedException("The review note already exists.");
        }
        TalentReviewNote talentReviewNote = reviewNoteRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Review Note not exist."));
        if(!checkUpdateReviewNotePermission(talentReviewNote)) {
            throw new CustomParameterizedException("No permission update review note.");
        }
        talentReviewNote.setReviewedByType(talentReviewNoteDTO.getReviewedByType());
        talentReviewNote.setReviewedBy(talentReviewNoteDTO.getReviewedBy());
        talentReviewNote.setReviewedDate(LocalDateTime.parse(talentReviewNoteDTO.getReviewedDate(), formatter));
        talentReviewNote.setTimezone(talentReviewNoteDTO.getTimezone());
        talentReviewNote.setNote(talentReviewNoteDTO.getNote());
        TalentReviewNote updateNote = reviewNoteRepository.saveAndFlush(talentReviewNote);
        return getTalentReviewNoteDTO(updateNote);
    }

    @Resource
    private TalentTrackingNoteRepository talentTrackingNoteRepository;

    @Override
    public List<Map<String, Object>> getTalentNoteByType(Long talentId, List<String> typeList, String noteType) {
        List<String> filterTypeList = filterTypeList(typeList);
        List<Map<String, Object>> list = new ArrayList<>();
        for(String type : filterTypeList) {
            if(TalentNoteSearchType.ALL.equalsIgnoreCase(type)) {
                list.addAll(getNote(talentId, TalentNoteSearchType.CALL_NOTE));
                list.addAll(getNote(talentId, TalentNoteSearchType.FOLLOW_UP_NOTE));
                list.addAll(getPipelineNote(talentId));
                list.addAll(getReviewNote(talentId));
                list.addAll(getApnProNoteList(talentId));
            } else if(TalentNoteSearchType.CALL_NOTE.equalsIgnoreCase(type) || TalentNoteSearchType.FOLLOW_UP_NOTE.equalsIgnoreCase(type)) {
                list.addAll(getNote(talentId, type));
            } else if(TalentNoteSearchType.PIPE_LINE_NOTE.equalsIgnoreCase(type)) {
                list.addAll(getPipelineNote(talentId));
            } else if(TalentNoteSearchType.REVIEW_NOTE.equalsIgnoreCase(type)) {
                list.addAll(getReviewNote(talentId));
            } else if(TalentNoteSearchType.APN_PRO_NOTE.equalsIgnoreCase(type)) {
                list.addAll(getApnProNoteList(talentId));
            }
        }
        sortMapListByCreatedDate(list);
        list = topShowBefore(talentId, list);
        for(Map<String, Object> note : list) {
            Object noteContent = note.get("note");
            Object companyName = note.getOrDefault("companyName", null);
            note.put("hasPermission", Boolean.TRUE);
            if(noteContent == null) {
                note.put("note", "");
            }else if (Objects.nonNull(companyName) && String.valueOf(companyName).equals(TalentRecruitmentProcessConstants.MASK)){ // If company name is masked, the current user has no permission for this application
                note.put("note", TalentRecruitmentProcessConstants.MASK);
                note.put("hasPermission", Boolean.FALSE);
            }
        }
        if(noteType != null) list = list.stream().filter(note -> note.get("noteType") != null && note.get("noteType").toString().equalsIgnoreCase(noteType)).toList();
        //防止/voice-mail/new/{talentId} 接口调用时的入参callNote将用户自定义配置覆盖
        if(noteType == null) {
            updatePersonalizationConfig(filterTypeList);
        }
        return list;
    }

    private List<Map<String, Object>> topShowBefore(Long talentId, List<Map<String, Object>> list) {
        UserPersonalizationConfig userPersonalizationConfig = userService.getPersonalizationConfig().getBody();
        if(userPersonalizationConfig == null) {
            return list;
        }
        Map<Long, TopNoteConfig> topNotes = userPersonalizationConfig.getTopNotes();
        if(topNotes == null) {
            return list;
        }
        TopNoteConfig topNoteConfig = topNotes.get(talentId);
        if(topNoteConfig == null) {
            return list;
        }
        return sortNotes(list, topNoteConfig);
    }

    public static List<Map<String, Object>> sortNotes(List<Map<String, Object>> notes, TopNoteConfig config) {
        if (notes == null || notes.isEmpty() || config == null) {
            return notes;
        }

        List<Map<String, Object>> sortedNotes = new ArrayList<>(notes.size());
        List<Map<String, Object>> remainingNotes = new ArrayList<>(notes.size());

        for (Map<String, Object> note : notes) {
            String type = (String) note.get("type");
            Long id = ((Number) note.get("id")).longValue();

            boolean isTopNote = false;

            if (TalentNoteSearchType.FOLLOW_UP_NOTE.equals(type) && config.getFollowUpNoteTopIds() != null) {
                isTopNote = config.getFollowUpNoteTopIds().contains(id.toString());
            } else if (TalentNoteSearchType.CALL_NOTE.equals(type) && config.getCallNoteTopIds() != null) {
                isTopNote = config.getCallNoteTopIds().contains(id.toString());
            } else if (TalentNoteSearchType.PIPE_LINE_NOTE.equals(type) && config.getPipelineNoteIds() != null) {
                isTopNote = config.getPipelineNoteIds().contains(id.toString());
            } else if (TalentNoteSearchType.REVIEW_NOTE.equals(type) && config.getReviewNoteTopIds() != null) {
                isTopNote = config.getReviewNoteTopIds().contains(id.toString());
            } else if (TalentNoteSearchType.APN_PRO_NOTE.equals(type) && config.getApnProNoteTopIds() != null) {
                isTopNote = config.getApnProNoteTopIds().contains(id.toString());
            }

            if (isTopNote) {
                note.put("top", true);
                sortedNotes.add(note);
            } else {
                note.put("top", false);
                remainingNotes.add(note);
            }
        }

        sortedNotes.addAll(remainingNotes);
        return sortedNotes;
    }

    @Override
    public void topTalentNote(TopTalentNoteDTO dto) {
        UserPersonalizationConfig userPersonalizationConfig = userService.getPersonalizationConfig().getBody();
        if(userPersonalizationConfig == null) {
            userPersonalizationConfig = new UserPersonalizationConfig();
        }
        Map<Long, TopNoteConfig> topNotes = userPersonalizationConfig.getTopNotes();
        if(topNotes == null) {
            topNotes = new HashMap<>();
        }
        TopNoteConfig topNoteConfig = topNotes.get(dto.getTalentId());
        if(topNoteConfig == null) {
            topNoteConfig = new TopNoteConfig();
        }
        switch (dto.getTalentNoteSearchType()) {
            case TalentNoteSearchType.FOLLOW_UP_NOTE:
                Set<String> followUpNoteTopIds = resolveTopIds(topNoteConfig.getFollowUpNoteTopIds(), dto.getNoteId(), dto.getTop());
                topNoteConfig.setFollowUpNoteTopIds(followUpNoteTopIds);
                break;
            case TalentNoteSearchType.CALL_NOTE:
                Set<String> callNoteTopIds = resolveTopIds(topNoteConfig.getCallNoteTopIds(), dto.getNoteId(), dto.getTop());
                topNoteConfig.setCallNoteTopIds(callNoteTopIds);
                break;
            case TalentNoteSearchType.PIPE_LINE_NOTE:
                Set<String> pipelineNoteIds = resolveTopIds(topNoteConfig.getPipelineNoteIds(), dto.getNoteId(), dto.getTop());
                topNoteConfig.setPipelineNoteIds(pipelineNoteIds);
                break;
            case TalentNoteSearchType.REVIEW_NOTE:
                Set<String> reviewNoteTopIds = resolveTopIds(topNoteConfig.getReviewNoteTopIds(), dto.getNoteId(), dto.getTop());
                topNoteConfig.setReviewNoteTopIds(reviewNoteTopIds);
                break;
            case TalentNoteSearchType.APN_PRO_NOTE:
                Set<String> apnNoteTopIds = resolveTopIds(topNoteConfig.getApnProNoteTopIds(), dto.getNoteId(), dto.getTop());
                topNoteConfig.setApnProNoteTopIds(apnNoteTopIds);
                break;
        }
        topNotes.put(dto.getTalentId(), topNoteConfig);
        userPersonalizationConfig.setTopNotes(topNotes);
        userService.updatePersonalizationConfig(userPersonalizationConfig);
    }

    @Resource
    private TalentService talentService;

    @Override
    public List<TalentNoteDTO> findAllByTalentId(Long talentId) {
        if(!talentService.hasTalentViewAuthority(talentId)) {
            throw new CustomParameterizedException(Status.FORBIDDEN.getStatusCode(), "No permission", "No client contact access permission");
        }
        List<TalentNote> allByTalentId = talentNoteRepository.findAllByTalentId(talentId);
        return allByTalentId.stream().map(n -> {
            TalentNoteDTO dto = new TalentNoteDTO();
            BeanUtils.copyProperties(n, dto);
            return dto;
        }).toList();
    }

    @Override
    public List<TalentNoteDTO> findAllByTalentIdAndAgencyId(Long talentId, Long agencyId) {
        if(!talentService.hasTalentViewAuthority(talentId)) {
            throw new CustomParameterizedException(Status.FORBIDDEN.getStatusCode(), "No permission", "No client contact access permission");
        }
        List<TalentNote> allByTalentId = talentNoteRepository.findAllByTalentIdAndAgencyIdOrderByCreatedDateDesc(talentId, agencyId);
        return allByTalentId.stream().map(n -> {
            TalentNoteDTO dto = new TalentNoteDTO();
            BeanUtils.copyProperties(n, dto);
            return dto;
        }).toList();
    }

    @Override
    public List<TalentNoteDTO> findAllByVoipPhoneCallIds(List<String> phoneCallIds) {
        List<Object[]> results = talentNoteRepository.findAllTalentNoteByPhoneCallIdIn(phoneCallIds);
        return results.stream().map(r -> {
            try {
                JSONObject jsonObject = JSONUtil.parseObj((String) r[3]);
                Map<String, Object> additionalInfo = jsonObject;

                return new TalentNoteDTO(
                        ((Number) r[0]).longValue(),  // ID
                        (String) r[1],                // Title
                        (String) r[2],                // Note
                        additionalInfo,               // Parsed JSON (Map<String, Object>)
                        (String) r[4]                 // VoIP Phone Call ID
                );
            } catch (Exception e) {
                throw new RuntimeException("Failed to parse additional_info JSON", e);
            }
        }).collect(Collectors.toList());
    }

    @Override
    public void saveParsedResult(String json) {
        JSONObject result = JSONUtil.parseObj(json);
        if(result.containsKey("_note_id")) {
            Long id = result.getLong("_note_id");
            TalentNote note = talentNoteRepository.findById(id).orElse(null);
            if(note == null) {
                return;
            }
            JSONObject source = result.getJSONObject("_source");
            if(source == null) {
                source = new JSONObject();
            }
            if(!source.isEmpty()) {
                talentNoteRepository.updateEnrich(id, source.toString(), null);
            }
        }
    }

    @Override
    public String getTalentNoteEnrich(Long talentId) {
        List<TalentNote> waitMergeNotes = talentNoteRepository.findAllByTalentIdAndParsedResultIsNotNullAndEnrichResultIsNull(talentId);
        if(waitMergeNotes.isEmpty()) {
            return "{}";
        }
        // 按编辑时间排序，确保新的数据能覆盖旧的数据
        waitMergeNotes.sort(Comparator.comparing(TalentNote::getLastModifiedDate, Comparator.nullsFirst(Comparator.naturalOrder())));

        return mergeParsedResult(waitMergeNotes);
    }



    public String mergeParsedResult(List<TalentNote> waitMergeNotes) {
        JSONObject enrichNoteData = new JSONObject();

        for (TalentNote note : waitMergeNotes) {
            String parsedResult = note.getParsedResult();
            if (parsedResult == null || parsedResult.trim().isEmpty()) {
                continue;
            }

            try {
                JSONObject noteData = JSONUtil.parseObj(parsedResult);
                mergeNoteDataRecursively(enrichNoteData, noteData, note.getId(), note.getLastModifiedDate());
            } catch (Exception e) {
                System.out.println(e);
                throw e;
            }
        }
        // 清理内部比较的时间字段
        cleanupInternalFields(enrichNoteData);
        enrichNoteData.remove("miscellaneous");
        return enrichNoteData.toString();
    }

    // 清理内部字段（在最终输出前调用）
    private void cleanupInternalFields(JSONObject obj) {
        if (obj == null) return;

        // 移除内部时间字段
        obj.remove("_internal_updatedAt");

        // 递归清理子对象
        for (String key : obj.keySet()) {
            Object value = obj.get(key);
            if (value instanceof JSONObject) {
                cleanupInternalFields((JSONObject) value);
            } else if (value instanceof JSONArray) {
                JSONArray array = (JSONArray) value;
                for (int i = 0; i < array.size(); i++) {
                    if (array.get(i) instanceof JSONObject) {
                        cleanupInternalFields((JSONObject) array.get(i));
                    }
                }
            }
        }
    }

    private void mergeNoteDataRecursively(JSONObject target, JSONObject source, Long noteId, Instant updatedAt) {
        for (String key : source.keySet()) {
            Object sourceValue = source.get(key);
            if (sourceValue == null) {
                continue;
            }

            // 处理特殊字段的合并逻辑
            switch (key) {
                case "salaryRange":
                case "currency":
                case "payType":
                case "payTimes":
                    // 这里不做处理，在后面统一处理薪资字段
                    break;
                case "experiences":
                    mergeExperiencesArray(target, sourceValue, noteId, updatedAt);
                    break;
                case "educations":
                    mergeEducationsArray(target, sourceValue, noteId, updatedAt);
                    break;
                case "projects":
                    mergeProjectsArray(target, sourceValue, noteId, updatedAt);
                    break;
                case "miscellaneous":
                    break;
                default:
                    // 其他字段的默认处理逻辑
                    mergeDefaultField(target, key, sourceValue, noteId, updatedAt);
                    break;
            }
        }
        mergeSalaryFields(target, source, noteId, updatedAt);
    }

    private void mergeSalaryFields(JSONObject target, JSONObject source, Long noteId, Instant updatedAt) {
        // 检查source中是否包含任何薪资字段
        String[] salaryFields = {"salaryRange", "currency", "payType", "payTimes"};
        boolean hasSalaryData = Arrays.stream(salaryFields)
                .anyMatch(field -> source.containsKey(field));

        if (!hasSalaryData) {
            return; // 没有薪资数据，直接返回
        }

        // 检查现有薪资字段的时间（取任意一个字段的时间作为整体时间）
        Instant existingUpdatedAt = null;
        for (String field : salaryFields) {
            JSONObject existingField = target.getJSONObject(field);
            if (existingField != null) {
                existingUpdatedAt = getFieldUpdatedAt(existingField);
                break; // 找到一个就够了，因为它们是整体
            }
        }

        // 如果新数据更新或者没有现有数据，统一替换所有薪资字段
        if (updatedAt != null && (existingUpdatedAt == null || updatedAt.isAfter(existingUpdatedAt))) {
            // 先移除所有现有的薪资字段
            for (String field : salaryFields) {
                target.remove(field);
            }

            // 添加新的薪资字段
            for (String field : salaryFields) {
                Object value = source.get(field);
                if (value != null) {
                    JSONObject fieldObj = createFieldObject(value, noteId);
                    setFieldUpdatedAt(fieldObj, updatedAt);
                    target.put(field, fieldObj);
                }
            }
        }
    }

    private void mergeExperiencesArray(JSONObject target, Object sourceValue, Long noteId, Instant updatedAt) {
        if (!(sourceValue instanceof JSONArray)) return;

        JSONArray targetArray = target.getJSONArray("experiences");
        if (targetArray == null) {
            targetArray = new JSONArray();
            target.put("experiences", targetArray);
        }

        JSONArray sourceArray = (JSONArray) sourceValue;
        for (int i = 0; i < sourceArray.size(); i++) {
            JSONObject sourceExp = sourceArray.getJSONObject(i);
            JSONObject existingExp = findDuplicateExperience(targetArray, sourceExp);

            if (existingExp != null) {
                // 递归合并重复的experience
                mergeComplexObjectRecursively(existingExp, sourceExp, noteId, updatedAt);
            } else {
                // 新增experience
                JSONObject newExp = wrapComplexObject(sourceExp, noteId, updatedAt);
                targetArray.add(newExp);
            }
        }
    }

    private void mergeEducationsArray(JSONObject target, Object sourceValue, Long noteId, Instant updatedAt) {
        if (!(sourceValue instanceof JSONArray)) return;

        JSONArray targetArray = target.getJSONArray("educations");
        if (targetArray == null) {
            targetArray = new JSONArray();
            target.put("educations", targetArray);
        }

        JSONArray sourceArray = (JSONArray) sourceValue;
        for (int i = 0; i < sourceArray.size(); i++) {
            JSONObject sourceEdu = sourceArray.getJSONObject(i);
            JSONObject existingEdu = findDuplicateEducation(targetArray, sourceEdu);

            if (existingEdu != null) {
                // 递归合并重复的education
                mergeComplexObjectRecursively(existingEdu, sourceEdu, noteId, updatedAt);
            } else {
                // 新增education
                JSONObject newEdu = wrapComplexObject(sourceEdu, noteId, updatedAt);
                targetArray.add(newEdu);
            }
        }
    }

    private void mergeProjectsArray(JSONObject target, Object sourceValue, Long noteId, Instant updatedAt) {
        if (!(sourceValue instanceof JSONArray)) return;

        JSONArray targetArray = target.getJSONArray("projects");
        if (targetArray == null) {
            targetArray = new JSONArray();
            target.put("projects", targetArray);
        }

        JSONArray sourceArray = (JSONArray) sourceValue;
        for (int i = 0; i < sourceArray.size(); i++) {
            JSONObject sourceProject = sourceArray.getJSONObject(i);
            JSONObject existingProject = findDuplicateProject(targetArray, sourceProject);

            if (existingProject != null) {
                // 递归合并重复的project
                mergeComplexObjectRecursively(existingProject, sourceProject, noteId, updatedAt);
            } else {
                // 新增project
                JSONObject newProject = wrapComplexObject(sourceProject, noteId, updatedAt);
                targetArray.add(newProject);
            }
        }
    }


    private void mergeDefaultField(JSONObject target, String fieldName, Object sourceValue, Long noteId, Instant updatedAt) {
        if (sourceValue instanceof JSONArray) {
            // 不认识的数组字段，对比内容后决定是否新增
            appendArrayFieldWithDeduplication(target, fieldName, sourceValue, noteId, updatedAt);
        } else {
            // 不认识的对象字段根据时间覆盖
            JSONObject fieldObj = createFieldObject(sourceValue, noteId);
            if (shouldOverride(target.getJSONObject(fieldName), updatedAt)) {
                target.put(fieldName, fieldObj);
                // 内部记录时间用于比较，但不输出到最终JSON
                setFieldUpdatedAt(fieldObj, updatedAt);
            }
        }
    }



    private void appendArrayFieldWithDeduplication(JSONObject target, String fieldName, Object sourceValue, Long noteId, Instant updatedAt) {
        JSONArray targetArray = target.getJSONArray(fieldName);
        if (targetArray == null) {
            targetArray = new JSONArray();
            target.put(fieldName, targetArray);
        }

        JSONArray sourceArray = (JSONArray) sourceValue;
        for (int i = 0; i < sourceArray.size(); i++) {
            Object sourceItem = sourceArray.get(i);

            // 检查是否已存在完全相同的内容
            if (!isItemExistsInArray(targetArray, sourceItem)) {
                // 不存在相同内容，添加新项
                JSONObject itemWrapper = new JSONObject();
                itemWrapper.put("value", sourceItem);
                itemWrapper.put("noteId", noteId);
                setFieldUpdatedAt(itemWrapper, updatedAt);
                targetArray.add(itemWrapper);
            }
        }
    }

    // 检查数组中是否已存在相同内容的项
    private boolean isItemExistsInArray(JSONArray targetArray, Object sourceItem) {
        for (int i = 0; i < targetArray.size(); i++) {
            JSONObject targetItem = targetArray.getJSONObject(i);
            Object targetValue = targetItem.get("value");

            // 深度比较内容是否完全一致
            if (isContentEqual(targetValue, sourceItem)) {
                return true;
            }
        }
        return false;
    }

    // 深度比较两个对象内容是否相等
    private boolean isContentEqual(Object obj1, Object obj2) {
        if (obj1 == obj2) return true;
        if (obj1 == null || obj2 == null) return false;

        // 如果都是JSONObject，递归比较每个字段
        if (obj1 instanceof JSONObject && obj2 instanceof JSONObject) {
            JSONObject json1 = (JSONObject) obj1;
            JSONObject json2 = (JSONObject) obj2;

            if (json1.size() != json2.size()) return false;

            for (String key : json1.keySet()) {
                if (!json2.containsKey(key)) return false;
                if (!isContentEqual(json1.get(key), json2.get(key))) return false;
            }
            return true;
        }

        // 如果都是JSONArray，递归比较每个元素
        if (obj1 instanceof JSONArray && obj2 instanceof JSONArray) {
            JSONArray arr1 = (JSONArray) obj1;
            JSONArray arr2 = (JSONArray) obj2;

            if (arr1.size() != arr2.size()) return false;

            for (int i = 0; i < arr1.size(); i++) {
                if (!isContentEqual(arr1.get(i), arr2.get(i))) return false;
            }
            return true;
        }

        // 其他情况直接比较
        return Objects.equals(obj1, obj2);
    }

    // 递归合并复杂对象（experiences/educations/projects）
    private void mergeComplexObjectRecursively(JSONObject target, JSONObject source, Long noteId, Instant updatedAt) {
        for (String key : source.keySet()) {
            if ("id".equals(key)) {
                // id字段跳过
                continue;
            }

            Object sourceValue = source.get(key);
            if (sourceValue == null) {
                continue;
            }

            JSONObject targetField = target.getJSONObject(key);
            if (targetField == null) {
                // 目标中没有这个字段，直接添加
                JSONObject newField = createFieldObject(sourceValue, noteId);
                target.put(key, newField);
                setFieldUpdatedAt(newField, updatedAt);
            } else {
                // 根据时间判断是否覆盖
                if (shouldOverride(targetField, updatedAt)) {
                    JSONObject newField = createFieldObject(sourceValue, noteId);
                    target.put(key, newField);
                    setFieldUpdatedAt(newField, updatedAt);
                }
            }
        }
    }

    // 查找重复的experience
    private JSONObject findDuplicateExperience(JSONArray experiences, JSONObject newExp) {
        String newCompanyName = newExp.getStr("companyName");
        String newStartDate = newExp.getStr("startDate");
        String newEndDate = newExp.getStr("endDate");
        Long newId = newExp.getLong("id");

        for (int i = 0; i < experiences.size(); i++) {
            JSONObject exp = experiences.getJSONObject(i);
            Long id = exp.getLong("id");
            String companyName = getWrappedValue(exp, "companyName", String.class);
            String startDate = getWrappedValue(exp, "startDate", String.class);
            String endDate = getWrappedValue(exp, "endDate", String.class);

            if(id != null && id.equals(newId)) {
                return exp;
            }
            if (Objects.equals(newCompanyName, companyName) &&
                    (Objects.equals(newStartDate, startDate) || Objects.equals(newEndDate, endDate))) {
                return exp;
            }
        }
        return null;
    }

    // 查找重复的education
    private JSONObject findDuplicateEducation(JSONArray educations, JSONObject newEdu) {
        String newCollegeName = newEdu.getStr("collegeName");
        String newStartDate = newEdu.getStr("startDate");
        String newEndDate = newEdu.getStr("endDate");
        String newDegreeLevel = newEdu.getStr("degreeLevel");
        Long newId = newEdu.getLong("id");

        for (int i = 0; i < educations.size(); i++) {
            JSONObject edu = educations.getJSONObject(i);
            String collegeName = getWrappedValue(edu, "collegeName", String.class);
            String startDate = getWrappedValue(edu, "startDate", String.class);
            String endDate = getWrappedValue(edu, "endDate", String.class);
            String degreeLevel = getWrappedValue(edu, "degreeLevel", String.class);
            Long id = edu.getLong("id");
            if(newId != null && newId.equals(id)) {
                return edu;
            }

            if (Objects.equals(newCollegeName, collegeName) &&
                    (Objects.equals(newStartDate, startDate) ||
                            Objects.equals(newEndDate, endDate) ||
                            Objects.equals(newDegreeLevel, degreeLevel))) {
                return edu;
            }
        }
        return null;
    }

    // 查找重复的project
    private JSONObject findDuplicateProject(JSONArray projects, JSONObject newProject) {
        String newProjectName = newProject.getStr("projectName");
        Long newId = newProject.getLong("id");

        for (int i = 0; i < projects.size(); i++) {
            JSONObject project = projects.getJSONObject(i);
            String projectName = getWrappedValue(project, "projectName", String.class);
            Long id = project.getLong("id");

            if (Objects.equals(newId, id)) {
                return project;
            } else if (Objects.equals(newProjectName, projectName)) {
                return project;
            }
        }
        return null;
    }

    // 包装复杂对象（为每个字段添加noteId）
    private JSONObject wrapComplexObject(JSONObject source, Long noteId, Instant updatedAt) {
        JSONObject wrapped = new JSONObject();

        for (String key : source.keySet()) {
            Object value = source.get(key);
            if ("id".equals(key)) {
                // id字段直接保留
                wrapped.put(key, value);
            } else {
                // 其他字段包装
                JSONObject fieldObj = createFieldObject(value, noteId);
                wrapped.put(key, fieldObj);
                setFieldUpdatedAt(fieldObj, updatedAt);
            }
        }

        return wrapped;
    }

    // 创建带有noteId的字段对象（不包含updatedAt）
    private JSONObject createFieldObject(Object value, Long noteId) {
        JSONObject fieldObj = new JSONObject();
        fieldObj.put("value", value);
        fieldObj.put("noteId", noteId);
        return fieldObj;
    }

    // 内部使用的时间设置方法（用于比较，不在最终JSON中显示）
    private void setFieldUpdatedAt(JSONObject fieldObj, Instant updatedAt) {
        // 使用特殊前缀标记内部字段，在输出前移除
        fieldObj.put("_internal_updatedAt", updatedAt.toString());
    }

    // 内部使用的时间获取方法
    private Instant getFieldUpdatedAt(JSONObject fieldObj) {
        String updatedAtStr = fieldObj.getStr("_internal_updatedAt");
        if (updatedAtStr == null) {
            return null;
        }
        try {
            return Instant.parse(updatedAtStr);
        } catch (Exception e) {
            return null;
        }
    }

    // 判断是否应该覆盖（基于时间）
    private boolean shouldOverride(JSONObject existingField, Instant newUpdatedAt) {
        if (existingField == null) {
            return true;
        }

        Instant existingUpdatedAt = getFieldUpdatedAt(existingField);
        if (existingUpdatedAt == null) {
            return true;
        }

        return newUpdatedAt != null && newUpdatedAt.isBefore(existingUpdatedAt);
    }

    // 从包装的字段中获取实际值
    private <T> T getWrappedValue(JSONObject obj, String fieldName, Class<T> clazz) {
        JSONObject fieldObj = obj.getJSONObject(fieldName);
        if (fieldObj == null) {
            return null;
        }
        return fieldObj.get("value", clazz);
    }


    private Set<String> resolveTopIds(Set<String> topIds, Long noteId, Boolean top) {
        if(topIds == null) {
            topIds = new HashSet<>();
        }
        if(top) {
            topIds.add(noteId.toString());
        } else {
            topIds.remove(noteId.toString());
        }
        return topIds;
    }

    private List<String> filterTypeList(List<String> typeList) {
        Set<String> typeSet = typeList.stream().collect(Collectors.toSet());
        for(String type : typeSet) {
            if(TalentNoteSearchType.ALL.equalsIgnoreCase(type)) {
                return List.of(TalentNoteSearchType.ALL);
            }
        }
        return typeSet.stream().toList();
    }

    private void updatePersonalizationConfig(List<String> type) {
        UserPersonalizationConfig userPersonalizationConfig = userService.getPersonalizationConfig().getBody();
        if(userPersonalizationConfig == null) {
            userPersonalizationConfig = new UserPersonalizationConfig();
        }
        userPersonalizationConfig.setNoteSearchType(type);
        userService.updatePersonalizationConfig(userPersonalizationConfig);
    }

    @Resource
    private ApplicationService applicationService;

    private List<Map<String, Object>> getPipelineNote(Long talentId) {
        List<TalentRecruitmentProcessForTalentVOSimple> processList = applicationService.getTalentRecruitmentProcessListVOByTalentId(talentId).getBody();
        if(processList != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
            objectMapper.registerModule(new JavaTimeModule());
            List<Map<String, Object>> list = new ArrayList<>();
            processList.forEach(p -> {
                Long id = p.getId();
                AllNotesVO allNotesVO = applicationService.getAllNotesByTalentRecruitmentProcessId(id).getBody();
                List<NodeAndNote> notes = allNotesVO.getNotes();
                if(notes != null) {
                    notes.forEach(n -> {
                        Map<String, Object> map = objectMapper.convertValue(n, Map.class);
                        map.put("type", TalentNoteSearchType.PIPE_LINE_NOTE);
                        map.put("companyName", p.getCompanyName());
                        map.put("companyId", p.getCompanyId());
                        map.put("jobId", p.getJobId());
                        map.put("privateJob", p.getPrivateJob());
                        map.put("jobTitle", p.getJobTitle());
                        map.put("talentRecruitmentProcessId", allNotesVO.getTalentRecruitmentProcessId());
                        Instant createdDate = n.getCreatedDate();
                        if(createdDate != null) {
                            map.put("createdDate", createdDate.toEpochMilli());
                        }
                        Instant lastModifiedDate = n.getLastModifiedDate();
                        if(lastModifiedDate != null) {
                            map.put("lastModifiedDate", lastModifiedDate.toEpochMilli());
                        }
                        if(StringUtils.isNotEmpty(n.getNote())) {
                            list.add(map);
                        }
                    });
                }
            });

            return list;
        }
        return new ArrayList<>();
    }

    private List<Map<String, Object>> getNote(Long talentId, String type) {
        List<TalentNote> noteList = talentNoteRepository.findAllByTalentId(talentId);
        //for call candidate note only
        Set<String> phoneCallIds = noteList.stream().filter(note -> TalentNoteType.CALL_CANDIDATES.equals(note.getNoteType()) && note.getAdditionalInfo() != null && (note.getAdditionalInfo().get("phoneCallId") != null)).map(note -> note.getAdditionalInfo().get("phoneCallId").toString()).collect(Collectors.toSet());
        Map<String, VoipContactDetailVO> contactDetailMap = new HashMap<>();
        try {
            if(!phoneCallIds.isEmpty()) {
                List<VoipContactDetailVO> res = voipService.getVoipContactDetailByPhoneCallIdIn(phoneCallIds).getBody();
                if(res != null) contactDetailMap = res.stream().collect(Collectors.toMap(VoipContactDetailVO::getPhoneCallId, voipContactDetailVO -> voipContactDetailVO));
            }
        }
        catch (Exception e) {
            log.error("search voip contact detail error: {}", e.getMessage());
        }
        Map<String, VoipContactDetailVO> finalContactDetailMap = contactDetailMap;
        List<TalentNoteDTO> noteListDTO = Convert.toList(TalentNoteDTO.class, noteList);
        setNoteAttachUserInfo(noteListDTO);
        suppleNotesUserInfo(noteListDTO);
        if(noteListDTO != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
            objectMapper.registerModule(new JavaTimeModule());
            List<Map<String, Object>> notes = noteListDTO.stream().filter(p -> {
                Map<String, Object> additionalInfo = p.getAdditionalInfo();
                if(TalentNoteSearchType.CALL_NOTE.equalsIgnoreCase(type)) {
                    if(additionalInfo != null) {
                        Object o = additionalInfo.get("phoneCallId");
                        return o != null;
                    }
                    return false;
                } else {
                    if(additionalInfo != null) {
                        Object o = additionalInfo.get("phoneCallId");
                        return o == null;
                    }
                    return true;
                }
            }).map(n -> {
                Map<String, Object> map = objectMapper.convertValue(n, Map.class);
                map.put("type", type);
                Instant createdDate = n.getCreatedDate();
                if(createdDate != null) {
                    map.put("createdDate", createdDate.toEpochMilli());
                }
                Instant lastModifiedDate = n.getLastModifiedDate();
                if(lastModifiedDate != null) {
                    map.put("lastModifiedDate", lastModifiedDate.toEpochMilli());
                }
                //if the note is CALL_CANDIDATE, then add call type/result and related job id/title into return
                if(TalentNoteType.CALL_CANDIDATES.equals(n.getNoteType())) {
                    if(n.getAdditionalInfo() != null && n.getAdditionalInfo().get("phoneCallId") != null) {
                        VoipContactDetailVO contact = finalContactDetailMap.get(n.getAdditionalInfo().get("phoneCallId"));
                        if(contact != null) {
                            map.put("callTypeId", contact.getCallTypeId());
                            map.put("callResultId", contact.getCallResultId());
                            map.put("number", contact.getPhoneNumber());
                            map.put("jobId", contact.getJobId());
                        }
                    }
                }
                String enrichResult = n.getEnrichResult();
                String parsedResult = n.getParsedResult();
                if(parsedResult != null) {
                    if(enrichResult == null) {
                        map.put("enrichStatus", EnrichStatusEnum.WAITING_ENRICH);
                    } else {
                        JSONObject enrichResultJson = JSONUtil.parseObj(enrichResult);
                        map.put("enrichStatus", enrichResultJson.isEmpty() ? EnrichStatusEnum.DO_NOT_ENRICH : EnrichStatusEnum.ENRICHED);
                    }
                }
                return map;
            }).collect(Collectors.toList());
            List<Long> jobIds = notes.stream().filter(note -> note.get("jobId") != null).map(note -> (long)note.get("jobId")).toList();
            Map<Long, JobBriefDTO> allJobs = jobBriefRepository.findBriefJobsByIdIn(jobIds).stream().collect(Collectors.toMap(JobBriefDTO::getId, jobBriefDTO -> jobBriefDTO));
            Map<Long, JobBriefDTO> permittedJobs = jobService.getBriefJobListByIds(jobIds).getBody().stream().collect(Collectors.toMap(JobBriefDTO::getId, jobBriefDTO -> jobBriefDTO));
            Set<Long> privateJobIds = !jobIds.isEmpty() ? jobService.findPrivateJobIds(jobIds):null;

            for (Map<String, Object> note : notes) {
                if (note.get("jobId") != null) {
                    note.put("jobTitle", allJobs.get(note.get("jobId")).getTitle());
                    note.put("jobPermission", permittedJobs.get(note.get("jobId")) != null);
                    if (CollUtil.isNotEmpty(privateJobIds) && privateJobIds.contains(Long.parseLong(note.get("jobId").toString()))) {
                        note.put("privateJob", true);
                    } else {
                        note.put("privateJob", false);
                    }
                }
            }
            return notes;
        } else {
            return new ArrayList<>();
        }
    }

    private void suppleNotesUserInfo(List<TalentNoteDTO> notes) {
        if(notes == null) {
            return;
        }
        Map<Long, SimpleUser> simpleUsers = getSimpleUsers(notes);
        notes.forEach(n -> {
            n.setCreatedUser(simpleUsers.get(extractUserId(n.getCreatedBy())));
            if(n.getLastModifiedBy() != null) n.setLastModifiedUser(simpleUsers.get(extractUserId(n.getLastModifiedBy())));
        });

    }

    private void setNoteAttachUserInfo(List<TalentNoteDTO> noteList) {
        if(noteList == null) {
            return;
        }
        Map<Long, SimpleUser> simpleUsers = getSimpleUsers(noteList);
        for(TalentNoteDTO dto : noteList) {
            String createdBy = dto.getCreatedBy();
            if(createdBy != null && dto.getCreatedUser() == null) {
                dto.setCreatedUser(simpleUsers.get(extractUserId(createdBy)));
            }
            String lastModifiedBy = dto.getLastModifiedBy();
            if(lastModifiedBy != null && dto.getLastModifiedUser() == null) {
                dto.setLastModifiedUser(simpleUsers.get(extractUserId(lastModifiedBy)));
            }
        }
    }

    private Long extractUserId(String str) {
        return Long.parseLong(str.split(",")[0]);
    }

    @Resource
    private SimpleUserRepository simpleUserRepository;


    private Map<Long, SimpleUser> getSimpleUsers(List<? extends AbstractAuditingEntity> entities) {
        List<Long> userIds = entities.stream().filter(Objects::nonNull).flatMap(entity -> {
            Optional<Long> creatorId = Optional.ofNullable(entity.getCreatedBy()).map(createBy -> Long.parseLong(createBy.split(",")[0]));
            Optional<Long> modifierId = Optional.ofNullable(entity.getLastModifiedBy()).map(modifyBy -> Long.parseLong(modifyBy.split(",")[0]));
            return Stream.of(creatorId, modifierId).filter(Optional::isPresent).map(Optional::get);
        }).toList();
        return simpleUserRepository.findAllById(userIds).stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));

    }

    private List<Map<String, Object>> getReviewNote(Long talentId) {
        List<TalentReviewNote> reviewNoteList = reviewNoteRepository.findAllByTalentIdIs(talentId);
        List<TalentReviewNoteDTO> noteListDTO = Convert.toList(TalentReviewNoteDTO.class, reviewNoteList);
        talentService.setReviewNoteAttachUserInfo(noteListDTO);
        if(noteListDTO != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
            objectMapper.registerModule(new JavaTimeModule());
            return noteListDTO.stream().map(n -> {
                Map<String, Object> map = objectMapper.convertValue(n, Map.class);
                map.put("type", TalentNoteSearchType.REVIEW_NOTE);
                Instant createdDate = n.getCreatedDate();
                if(createdDate != null) {
                    map.put("createdDate", createdDate.toEpochMilli());
                }
                Instant lastModifiedDate = n.getLastModifiedDate();
                if(lastModifiedDate != null) {
                    map.put("lastModifiedDate", lastModifiedDate.toEpochMilli());
                }
                return map;
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    @Resource
    private UserService userService;

    private List<Map<String, Object>> getApnProNoteList(Long talentId) {
        List<TalentTrackingNote> trackingNoteList = talentTrackingNoteRepository.findAllBySyncedTalentIdIsOrderByCreatedDateDesc(talentId);
        List<Long> userIdList = trackingNoteList.stream().map(TalentTrackingNote::getUserId).toList();
        List<User> userList = userService.findByIds(userIdList).getBody();
        return trackingNoteList.stream().map(n -> {
            Map<String, Object> map = new HashMap<>();
            map.put("type", TalentNoteSearchType.APN_PRO_NOTE);
            TrackingPlatform trackingPlatform = n.getTrackingPlatform();
            if (trackingPlatform != null) {
                map.put("source", trackingPlatform.name());
            }
            map.put("note", n.getNote());
            String name = TalentRelateJobFolderServiceImpl.getNameByUserListAndUserId(userList, n.getUserId());
            map.put("createdBy", n.getUserId());
            map.put("createdByUserName", name);
            Instant createdDate = n.getCreatedDate();
            if(createdDate != null) {
                map.put("createdDate", createdDate.toEpochMilli());
            }
            return map;
        }).collect(Collectors.toList());
    }

    private void sortMapListByCreatedDate(List<Map<String, Object>> list) {
        Comparator<Map<String, Object>> comparator = (map1, map2) -> {
            Object createdDate1 = map1.get("createdDate");
//            Object lastModifiedDate1 = map1.get("lastModifiedDate");
//            Instant date1 = getInstantFromDate(createdDate1, lastModifiedDate1);
            Object createdDate2 = map2.get("createdDate");
//            Object lastModifiedDate2 = map2.get("lastModifiedDate");
//            Instant date2 = getInstantFromDate(createdDate2, lastModifiedDate2);
            Long time1 = createdDate1 != null ? (Long)createdDate1 : 0L;
            Long time2 = createdDate2 != null ? (Long)createdDate2 : 0L;
            return time2.compareTo(time1); // 降序排序
        };
        Collections.sort(list, comparator);
    }

    private Instant getInstantFromDate(Object createdDate, Object lastModifiedDate) {
        if(createdDate != null) {
            if(createdDate instanceof BigDecimal) {
                return Instant.ofEpochSecond(((BigDecimal) createdDate).longValue());
            } else {
                return (Instant) createdDate;
            }
        } else {
            if(lastModifiedDate != null) {
                if(lastModifiedDate instanceof BigDecimal) {
                    return Instant.ofEpochSecond(((BigDecimal) lastModifiedDate).longValue());
                } else {
                    return (Instant) lastModifiedDate;
                }
            }
        }
        return Instant.ofEpochSecond(0L);
    }

    @Resource
    private PermissionTeamRepository permissionTeamRepository;

    @Resource
    private PermissionTeamLeaderRepository permissionTeamLeaderRepository;

    private boolean checkUpdateReviewNotePermission(TalentReviewNote talentReviewNote) {
        Long reviewedBy = talentReviewNote.getReviewedBy();
        ReviewedByType reviewedByType = talentReviewNote.getReviewedByType();
        //add admin authority
        if(SecurityUtils.isAdmin()) return true;
        if(ReviewedByType.USER.equals(reviewedByType)) {
            return SecurityUtils.getUserId().equals(reviewedBy);
        } else if (ReviewedByType.TEAM.equals(reviewedByType)) {
            if(SecurityUtils.getTeamId().equals(reviewedBy)) {
                return true;
            } else {
                //主团队的普通成员，不应该可以编辑子团队的review note
                List<Long> parentTeamId = permissionTeamRepository.getAncestorTeams(List.of(reviewedBy)).stream().map(PermissionTeam::getId).toList();
                return parentTeamId.contains(SecurityUtils.getTeamId()) && permissionTeamLeaderRepository.existsByTeamIdAndUserId(SecurityUtils.getTeamId(), SecurityUtils.getUserId());
            }
        }
        return false;
    }
    private TalentReviewNoteDTO getTalentReviewNoteDTO(TalentReviewNote talentReviewNote) {
        TalentReviewNoteDTO ret = new TalentReviewNoteDTO();
        BeanUtil.copyProperties(talentReviewNote, ret);
        talentService.setReviewNoteAttachUserInfo(List.of(ret));
        return ret;
    }

    private boolean existRepeatReviewStatus(TalentReviewNoteDTO talentReviewNoteDTO, Long excludeId) {
        List<TalentReviewNote> noteList = reviewNoteRepository.findAllByTalentIdIsAndReviewedByIsAndReviewedByTypeIs(talentReviewNoteDTO.getTalentId(), talentReviewNoteDTO.getReviewedBy(), talentReviewNoteDTO.getReviewedByType());
        List<TalentReviewNote> collect = noteList.stream().filter(p -> !p.getId().equals(excludeId)).toList();
        return !collect.isEmpty();
    }
}
