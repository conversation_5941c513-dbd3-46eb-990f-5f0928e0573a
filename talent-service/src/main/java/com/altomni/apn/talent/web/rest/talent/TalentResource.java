package com.altomni.apn.talent.web.rest.talent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.talent.CreationTalentType;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.talent.TelephoneChatScript;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.company.ClientContactCompany;
import com.altomni.apn.common.dto.search.TalentSearchConditionDTO;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.common.vo.talent.TalentEmailContactVO;
import com.altomni.apn.common.vo.talent.TalentToHrVO;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.job.domain.report.ReportUserJobTalent;
import com.altomni.apn.talent.domain.talent.ExcelTaskDto;
import com.altomni.apn.talent.domain.talent.UpdateExcelTaskDto;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.dto.activitylog.TalentActivityDTO;
import com.altomni.apn.talent.service.dto.start.TalentExcelUploadUrlDto;
import com.altomni.apn.talent.service.dto.talent.*;
import com.altomni.apn.talent.service.elastic.EsCommonService;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.job.JobService;
import com.altomni.apn.talent.service.talent.RecommendationReportService;
import com.altomni.apn.talent.service.talent.TalentActivityService;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.service.talent.TalentServiceV3;
import com.altomni.apn.talent.service.vo.talent.ExcelTalentProcessVo;
import com.altomni.apn.talent.web.rest.talent.dto.*;
import com.altomni.apn.talent.web.rest.vm.TalentContactSearchVM;
import com.altomni.apn.talent.web.rest.vm.TalentContactVM;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedReader;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * REST controller for managing Talent.
 */
@Api(tags = {"Talent"})
@RestController
@RequestMapping("/api/v3")
public class TalentResource {

    private final Logger log = LoggerFactory.getLogger(TalentResource.class);

    private static final String ENTITY_NAME = "talent";

    @Resource
    private TalentService talentService;

    @Resource
    private TalentServiceV3 talentServiceV3;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    private EsCommonService esCommonService;

    @Resource
    private JobService jobService;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private TalentActivityService talentActivityService;

    @Resource
    private RecommendationReportService recommendationReportService;


    @PostMapping(value = "/talents/search/source", consumes = "application/json; charset=utf-8")
    @Timed
    public ResponseEntity<String> searchTalentSource(@RequestBody SearchTalentSourceInput condition, Pageable pageable) throws IOException {
        log.info("[APN: Talent @{}] REST request to search Talent : {} ", SecurityUtils.getUserId(), condition);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        String json = talentServiceV3.searchTalentSourceFromES(condition, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(json);
    }

    @PostMapping(value = "/talents/search", consumes = "application/json; charset=utf-8")
    @Timed
    public ResponseEntity<String> searchTalent(@RequestBody TalentSearchConditionDTO condition, Pageable pageable) throws IOException {
        log.info("[APN: Talent @{}] REST request to search Talent : {} ", SecurityUtils.getUserId(), condition);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count", "Pagination-Owned-Data-Count", "Pagination-Not-Owned-Data-Count"));
        String json = talentServiceV3.searchTalentFromES(condition, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(json);
    }

    @GetMapping(value = "/talents/{id}/contacts")
    public ResponseEntity<List<TalentContactDTO>> getTalentContacts(@PathVariable String id, @RequestParam(value= "jobId", required = false) Long jobId) {
        return ResponseEntity.ok().body(talentService.getTalentContacts(id, jobId));
    }

    @GetMapping(value = "/talents/{id}/all-verification-status-contacts")
    public ResponseEntity<List<TalentContactDTO>> getTalentAllVerificationStatusContacts(@PathVariable Long id) {
        return ResponseEntity.ok().body(talentService.getTalentAllVerificationStatusContacts(id));
    }

    @PostMapping(value = "/talents/contacts")
    public ResponseEntity<List<TalentContactDTO>> getTalentContacts(@RequestBody GetTalentResumeByTalentIdDTO dto) {
        return ResponseEntity.ok().body(talentService.getTalentContacts(dto.getTalentId(), dto.getJobId()));
    }

    @GetMapping(value = "/talents/browse-quota")
    @Timed
    public ResponseEntity<BrowseQuotaDTO> getBrowseQuota() {
        return ResponseEntity.ok().body(talentService.getBrowseQuota());
    }

    /**
     * POST  /talents-v3 : Create a new talent.
     *
     * @param talentDTO the talent to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talent, or with status 400 (Bad Request) if the talent has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Create talent", notes = "Create can be APN-Pro crawling or parser parsed resume and create through ATS. When create talent for first time, all " +
            "related entities (educations, experiences etc.) will also be created. Create talent will also create " +
            "refer activity and add the talent to my watch list.", tags = {"ATS-Candidates", "APN-Pro"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/talents")
    @AttachSimpleUser
    @Timed
    @NoRepeatSubmit(expire = 3600000)
    public ResponseEntity<TalentDTOV3> createTalent(HttpServletRequest request) throws Exception {
        String requestBody = getRequestBody(request);
        log.info("[APN: Talent @{}] REST request to save Talent: {}", SecurityUtils.getUserId(), requestBody);
        SecurityContext context = SecurityContextHolder.getContext();
        TalentDTOV3 result = talentService.createAndResult(requestBody);
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            jobService.updateColumnPreferenceByUserId(SecurityUtils.getUserId(), result.getCreationTalentType(), ModuleType.CANDIDATE);
        });
        return ResponseEntity.created(new URI("/api/v3/talents/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    private String getRequestBody(HttpServletRequest request) throws IOException {
        // 首先检查是否已经缓存了请求体
        String cachedBody = (String) request.getAttribute("cachedRequestBody");
        if (cachedBody != null) {
            return cachedBody;
        }

        // 否则读取请求体
        StringBuilder buffer = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            buffer.append(line);
        }

        // 缓存请求体
        cachedBody = buffer.toString();
        request.setAttribute("cachedRequestBody", cachedBody);

        return cachedBody;
    }

    /**
     * Get recommended report template.
     *
     * @return A list of recommended report templates that can be generated
     */
    @ApiOperation(value = "Get recommended report template.", notes = "Get a list of recommended report templates that can be generated.")
    @GetMapping("/talents/recommended-report-template/{talentId}")
    @Timed
    public ResponseEntity<List<RecommendedTemplateDTO>> getRecommendedReportTemplate(@PathVariable Long talentId) {
        List<RecommendedTemplateDTO> result = talentService.getRecommendedReportTemplate(talentId);
        return ResponseEntity.ok(result);
    }

    /**
     * Get ai recommended reason.
     *
     * @param input the talent/job/template id.
     * @return recommended reason
     */
    @ApiOperation(value = "Get ai recommended reason.", notes = "Get corresponding AI recommendation reasons based on candidate information and job information applied for.")
    @PostMapping("/talents/recommendation-reason")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> getRecommendedReason(@RequestBody GetRecommendedReasonInput input) {
        String reason = recommendationReportService.getRecommendedReason(input);
        return ResponseEntity.ok(reason);
    }

    /**
     * Get recommended report file.
     *
     * @param input Data required in the report
     * @return report file
     */
    @ApiOperation(value = "Get recommended report file(pdf/docx).", notes = "Generate recommendation report files based on talent, user, and job information.")
    @PostMapping("/talents/recommendation-report")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ByteArrayResource> getRecommendationReport(@RequestBody GetRecommendationReportInput input) throws Exception {
        return recommendationReportService.getRecommendationReport(input);
    }

    @PostMapping("/talents/sync/{id}")
    @Timed
    public ResponseEntity<TalentDTOV3> syncTalent(@PathVariable Long id) throws URISyntaxException {
        log.info("[APN: Talent @{}] REST request to save Talent: {}", SecurityUtils.getUserId(), id);
        TalentDTOV3 result = talentService.findTalentById(id, false);
        commonRedisService.saveTalentId(Arrays.asList(id));
        return ResponseEntity.created(new URI("/api/v3/talents/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * Check if candidates can be added to company contacts.
     *
     * @param id talent id
     * @return The company matched in the candidate's resume in the company database
     */
    @ApiOperation(value = "Check if candidates can be added to company contacts.")
    @GetMapping("/talents/{id}/check/added-to-company-contacts")
    @Timed
    public ResponseEntity<List<ClientContactCompany>> checkAddedToCompanyContacts(@PathVariable Long id) {
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, id.toString()))
                .body(talentService.checkAddedToCompanyContacts(id));
    }

    @PutMapping("/talents/{id}")
    @AttachSimpleUser
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentDTOV3> updateTalent(@ApiParam(value = "talent id", required = true) @PathVariable Long id, HttpServletRequest request) throws IOException {
        String requestBody = getRequestBody(request);
        log.info("[APN: Talent @{}] REST request to update Talent: {}", SecurityUtils.getUserId(), requestBody);
        TalentDTOV3 result = talentService.update(id, requestBody);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, id.toString()))
                .body(result);
    }

    @PutMapping("/talents/info/{id}")
    @AttachSimpleUser
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentDTOV3> updateTalentInfo(@ApiParam(value = "talent id", required = true) @PathVariable Long id, @Valid @RequestBody TalentDTOV3 talent) throws IOException {
        log.info("[APN: Talent @{}] REST request to updateInfo Talent: {}", SecurityUtils.getUserId(), talent);
        talent.setId(id);
        TalentDTOV3 result = talentService.updateTalentInfo(talent);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, talent.getId().toString()))
                .body(result);
    }

    /**
     * GET  /talents/:id : get the "id" talent.
     *
     * @param id the id of the talent to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the talent, or with status 404 (Not Found)
     */
    @ApiOperation(value = "Get talent by id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/talents/{id}")
    @Timed
    @AttachSimpleUser
    public ResponseEntity<GetTalentDTO> getTalent(@ApiParam(value = "talent id", required = true) @PathVariable Long id) {
        log.info("[APN: Talent @{}] REST request to get Talent: {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentService.getTalentInfo(id)));
    }

    @PostMapping("/talents/talent-retrieving-audit/search")
    public ResponseEntity<List<TalentDetailRetrievingRecordDTO>> searchTalentDetailRetrievingRecord(@RequestBody TalentDetailRetrievingRecordSearchDTO talentDetailRetrievingRecordSearchDTO,
                                                                                                    @PageableDefault(value = 20, sort = {"retrievingTime"}, direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[APN: Talent @{}] REST request to search talent detail retrieving record : {}", SecurityUtils.getUserId(), talentDetailRetrievingRecordSearchDTO);
        String talentDetailRetrievingRecords = talentService.searchTalentDetailRetrievingRecord(talentDetailRetrievingRecordSearchDTO, pageable);
        JSONObject pageJson = new JSONObject(talentDetailRetrievingRecords);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count", "Pagination-Page", "Pagination-Limit"));
        headers.add("Pagination-Count", Long.toString(pageJson.getInt("totalElements")));
        headers.add("Pagination-Page", Long.toString(pageJson.getInt("number")));
        headers.add("Pagination-Limit", Long.toString(pageJson.getInt("size")));
        JSONArray content = pageJson.getJSONArray("content");
//        return new ResponseEntity<>(content.toList(TalentDetailRetrievingRecordDTO.class), headers, HttpStatus.OK);
        return ResponseEntity.ok().headers(headers).body(content.toList(TalentDetailRetrievingRecordDTO.class));

    }

    @ProcessConfidentialTalent(operation = ProcessConfidentialTalent.operation.FILTER)
    @PostMapping("/talents/recent-ten/search")
    public ResponseEntity<List<TalentDetailRecentTenDTO>> searchTalentDetailRecentTen(@RequestBody TalentDetailRetrievingRecordSearchDTO talentDetailRetrievingRecordSearchDTO,
                                                                                      @PageableDefault(page = 0, value = 30, sort = {"retrievingTime"}, direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[APN: Talent @{}] REST request to search talent detail retrieving record : {}", SecurityUtils.getUserId(), talentDetailRetrievingRecordSearchDTO);
        String talentDetailRetrievingRecords = talentService.searchTalentDetailRetrievingRecord(talentDetailRetrievingRecordSearchDTO, pageable);
        JSONObject pageJson = new JSONObject(talentDetailRetrievingRecords);
        JSONArray content = pageJson.getJSONArray("content");
        List<TalentDetailRetrievingRecordDTO> recordDTOS = content.toList(TalentDetailRetrievingRecordDTO.class);
        List<TalentDetailRecentTenDTO> vo = new ArrayList<>();
        if (!recordDTOS.isEmpty()) {
            List<Long> talentIds = recordDTOS.stream().map(TalentDetailRetrievingRecordDTO::getTalentId).collect(Collectors.toList());
            List<ResignUserReportTalentDTO> talentDTOS = talentService.findTalentsByIds(talentIds);
            Set<Long> ids = new HashSet<>();
            if (!talentDTOS.isEmpty()) {
                Map<Long,ResignUserReportTalentDTO> talentDTOMap = talentDTOS.stream().collect(Collectors.toMap(ResignUserReportTalentDTO::getId,a->a));
                for (int i = 0; i < recordDTOS.size(); i++) {
                    TalentDetailRetrievingRecordDTO dto = recordDTOS.get(i);
                    if (talentDTOMap.containsKey(dto.getTalentId()) && !ids.contains(dto.getTalentId()) && ids.size() < 10) {
                        ResignUserReportTalentDTO v = talentDTOMap.get(dto.getTalentId());
                        TalentDetailRecentTenDTO bean = new TalentDetailRecentTenDTO();
                        bean.setId(v.getId());
                        bean.setName(v.getFullName());
                        vo.add(bean);
                        ids.add(dto.getTalentId());
                    }
                }
            }
        }
        return ResponseEntity.ok(vo);

    }

    /**
     * Get talent telephone chat scripts.
     *
     * @return Telephone scripts string.
     */
    @ApiOperation(value = "Get talent telephone chat scripts.")
    @GetMapping("/talents/telephone-chat-scripts")
    @Timed
    @AttachSimpleUser
    public ResponseEntity<List<TelephoneChatScript>> getTalentTelephoneChatScripts() {
        log.info("[APN: Talent @{}] REST request to get Talent telephone chat scripts", SecurityUtils.getUserId());
        List<TelephoneChatScript> telephoneChatScripts = talentService.getTelephoneChatScripts();
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(telephoneChatScripts));
    }

    @GetMapping("/talents/without-entity/{id}")
    @Timed
    @AttachSimpleUser
    public ResponseEntity<TalentDTOV3> getTalentWithoutEntity(@ApiParam(value = "talent id", required = true) @PathVariable("id") Long id) {
        log.info("[APN: Talent @{}] REST request to get Talent without entity: {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentService.findTalentByIdWithoutEntity(id)));
    }

    /**
     * /talents/search_by_contactlist
     */
    @PostMapping("/talents/search-by-contacts")
    @Timed
    public ResponseEntity<List<TalentDTOV3>> searchTalentsByContactList(@RequestBody @Valid TalentContactSearchVM talentContactSearchVM) {
        log.info("[APN: Talent @{}] REST request to search talent by contact list: {}", SecurityUtils.getUserId(), talentContactSearchVM);
        List<TalentDTOV3> talents = talentService.getTalentsByContacts(talentContactSearchVM);
        return ResponseEntity.ok(talents);
    }

    /**
     * /talents/search_by_contactlist
     * APN PRO专用
     */
    @PostMapping("/talents/search-by-contacts-and-similarity")
    @Timed
    @Deprecated
    public ResponseEntity<List<TalentDTOV3>> searchTalentsByContactAndSimilarity(@RequestBody TalentSimilarityDto talentSimilarityDto) {
        log.info("[APN: Talent @{}] REST request to search talent by contact list: {}", SecurityUtils.getUserId(), talentSimilarityDto);
        List<TalentDTOV3> talents = talentService.searchTalentsByContactAndSimilarityAPNPro(talentSimilarityDto);
        return ResponseEntity.ok(talents);
    }

    @PostMapping("/talents/id/search-by-contacts-and-similarity")
    @Timed
    public ResponseEntity<List<Long>> searchTalentsIdByContactAndSimilarity(@RequestBody TalentDTOV3 talentDTOV3) {
        log.info("[APN: Talent @{}] REST request to search talent id by contact list: {}", SecurityUtils.getUserId(), talentDTOV3);
        List<Long> talentIds = talentService.searchTalentsByContactAndSimilarity(talentDTOV3);
        return ResponseEntity.ok(talentIds);
    }


    @PostMapping("/talents-basic/search-by-contacts")
    @Timed
    public ResponseEntity<List<TalentDTOV3>> searchTalentsByContact(@RequestBody TalentDTOV3 talentDTOV3) {
        log.info("[APN: Talent @{}] REST request to search talent by contact list: {}", SecurityUtils.getUserId(), talentDTOV3);
        return ResponseEntity.ok(talentService.searchTalentsByContact(talentDTOV3));
    }

    @PostMapping("/talents-basic/find-by-ids")
    @Timed
    public ResponseEntity<List<TalentDTOV3>> findTalentsBasicByIds(@RequestBody List<Long> ids) {
        log.info("[APN: Talent @{}] REST request to search talent by ids: {}", SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(talentService.findTalentsBasicByIds(ids));
    }

    @PostMapping("/talents/search_by_contactlist")
    @Timed
    @AttachSimpleUser
    public ResponseEntity<List<TalentDTOV3>> searchTalentsByContactList(@RequestBody List<TalentContact> talentContacts) {
        log.info("[APN: Talent @{}] REST request to search talent by contact list: {}", SecurityUtils.getUserId(), talentContacts);
        TalentContactSearchVM talentContactSearchVM = new TalentContactSearchVM();
        talentContactSearchVM.setContacts(Convert.toList(TalentContactVM.class, talentContacts));
        List<TalentDTOV3> talents = talentService.getTalentsByContacts(talentContactSearchVM);
        return ResponseEntity.ok(talents);
    }

    /**
     * GET  /talents/talentIds : get all talents created by ids.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talents in body
     */
    @PostMapping("/talents/get-by-ids")
    @Timed
    public ResponseEntity<List<ResignUserReportTalentDTO>> getTalentsByIds(@RequestBody ReportUserJobTalent userJobTalent) {
        log.info("[APN: Talent @{}] REST request to get talents by ids: {}", SecurityUtils.getUserId(), userJobTalent);
        List<ResignUserReportTalentDTO> result = talentService.findTalentsByIds(Stream.of(userJobTalent.getTalentIds().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/talents/get-by-ids-without-entity")
    public ResponseEntity<List<TalentBriefDTO>> getTalentsByIdsWithoutEntity(@RequestBody Set<Long> ids) {
        log.info("[APN: Talent @{}] REST request to get talents by ids: {}", SecurityUtils.getUserId(), ids);
        List<TalentBriefDTO> result = talentService.getTalentsByIdsWithoutEntity(ids);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/talents/ai-recommendation/ids")
    @Timed
    public ResponseEntity<String> getTalentsForAiRecommendationByIds(@RequestBody GetTalentsForAiRecommendationDTO dto){
        log.info("[APN: Talent @{}] REST request to get talents by id list: {}", SecurityUtils.getUserId(), dto);
        JSONArray result = talentServiceV3.findTalentsForAiRecommendationByIds(dto);
        return new ResponseEntity<>(result.toString(), HttpStatus.OK);
    }

    //记录job-talent推荐进入的数据使用
    @PostMapping("/record/talent-job-recommend")
    @Timed
    public ResponseEntity<Void> recordTalentJobRecommend(@RequestBody RecommendFeedback dto){
        log.info("[APN: Talent @{}] REST request to record talent job recommend: {}", SecurityUtils.getUserId(), dto);
        talentServiceV3.recordTalentJobRecommend(dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @GetMapping("/talents/findTalentPhotoUrl/{id}")
    @Timed
    public ResponseEntity<String> findTalentPhotoUrl(@PathVariable Long id) {
        return new ResponseEntity<>(talentRepository.findTalentPhotoUrl(id), HttpStatus.OK);
    }

    /**-------------------------------------------- For APN Pro-------------------------------------------------------*/

    /**
     * PUT  /talents/:id : Updates an existing talent.
     *
     * @param talent the talent to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated talent,
     * or with status 400 (Bad Request) if the talent is not valid,
     * or with status 500 (Internal Server Error) if the talent couldn't be updated
     */
    @ApiOperation(value = "Update talent by id", notes = "Only allows updating basic info, can not update related entities (educations, contacts, experiences)", tags = {"ATS-Candidates"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/pro/talents/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentDTOV3> updateTalentForPro(@PathVariable Long id, @Valid @RequestBody TalentInfoInput talent) throws IOException {
        log.info("[APN: ProResource @{}] REST request to update Talent: {}", SecurityUtils.getUserId(), talent);
        talent.setOriginPreferences(talent.getPreferences());
        TalentDTOV3 result = talentService.updateForPro(id, talent);
        //esFillerTalentService.syncTalentToEs(result.getId());
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert("talent", id.toString()))
                .body(result);
    }

    @GetMapping("/talents/{id}/tenant-id")
    @Timed
    public ResponseEntity<Long> findTenantIdByTalentId(@ApiParam(value = "talent id", required = true) @PathVariable("id") Long id) {
        log.info("[APN: Talent @{}] REST request to get tenant id by Talent id: {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentService.findTalentById(id, false).getTenantId()));
    }

    @GetMapping("/talents/{id}/owner")
    @Timed
    public ResponseEntity<TalentOwnershipDTO> findTalentOwnerByTalentId(@ApiParam(value = "talent id", required = true) @PathVariable("id") Long id) {
        log.info("[APN: Talent @{}] REST request to get owner by Talent id: {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentService.findTalentOwnerShip(id)));
    }


    /**
     * POST  /talents : Create a new talent form commonPool.
     *
     * @param talentDTO the talent to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talent, or with status 400 (Bad Request) if the talent has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Create talent", tags = {"ATS-Candidates", "APN-Pro"})
    @PostMapping("/talents/commonPool")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentDTOV3> createTalentFromCommonPool(@Valid @RequestBody TalentDTOV3 talentDTO)  {
        log.info("[APN: Talent @{}] REST Create a new talent form commonPool: {}", SecurityUtils.getUserId(), talentDTO);
        TalentDTOV3 result = talentService.createAndUpdateCreditTransaction(talentDTO);
        return ResponseEntity.ok(result);

    }

    /**
     * common pool解锁候选人查重使用
     * @param talentDTO
     * @return
     */
    @ApiOperation(value = "check talent contact exist", tags = {"ATS-Candidates", "APN-Pro"})
    @PostMapping("/talents/checkContactExist")
    @Timed
    public ResponseEntity<List<SuspectedDuplications>> checkContactExist(@RequestBody TalentDTOV3 talentDTO)  {
        log.info("[APN: Talent @{}] REST Create a new talent form commonPool: {}", SecurityUtils.getUserId(), talentDTO);
        List<SuspectedDuplications> talentDTOV3s = talentService.checkContactExist(talentDTO.getContacts());
        return ResponseEntity.ok(talentDTOV3s);

    }

    /**
     * POST  用于在保存、更新人才信息时，检查是否有疑似重复的电话分机号
     * @param request
     * @return
     */
    @PostMapping("/talents/suspected-duplicate-phones-check")
    @Timed
    public ResponseEntity<List<SuspectedDuplications>> suspectedDuplicatePhonesCheck(HttpServletRequest request) throws Exception {
        log.info("[APN: Talent @{}] REST suspected duplicate phones check: {}", SecurityUtils.getUserId(), request);
        String requestBody = getRequestBody(request);
        return ResponseEntity.ok(talentService.suspectedDuplicatePhonesCheck(requestBody));

    }


    @ApiOperation(value = "check talent contact exist", tags = {"ATS-Candidates", "APN-Pro"})
    @PostMapping("/talents/apn-pro/duplicate/check")
    @Timed
    public ResponseEntity<List<SuspectedDuplications>> apnProDuplicateCheck(@RequestBody TalentInfoInput input)  {
        log.info("[APN: Talent @{}] REST apn pro duplicate check: {}", SecurityUtils.getUserId(), input);
        return ResponseEntity.ok(talentService.apnProDuplicateCheck(input));

    }

    @GetMapping("/talents/commonPool/contacts")
    @Timed
    public ResponseEntity<String> getContactsFromCommonPool(@RequestParam("esId") String esId) throws IOException {
        log.info("[APN: Talent @{}] REST get contacts from commonPool: {}", SecurityUtils.getUserId(), esId);
        String contacts = esCommonService.searchContactsFromCommonPool(esId);
        return ResponseEntity.ok(contacts);
    }

    /**
     * for common pool only
     * @param condition
     * @return
     * @throws IOException
     */
    @PostMapping("/talents/commonPool/talent/document")
    @Timed
    public ResponseEntity<TalentESDocument> getTalentESDocument(@RequestBody TalentESConditionDTO condition) throws IOException {
        log.info("[APN: Talent @{}] REST get talent ES document from commonPool: {}", SecurityUtils.getUserId(), condition);
        TalentESDocument document = esCommonService.getTalentESDocument(condition);
        return ResponseEntity.ok(document);
    }

    @PostMapping("/talents/commonPool/talent/contacts")
    @Timed
    public ResponseEntity<String> searchContactsFromCommonPool(@RequestBody TalentESConditionDTO condition) throws IOException {
        log.info("[APN: Talent @{}] REST get talent ES document from commonPool: {}", SecurityUtils.getUserId(), condition);
        String contacts = esFillerTalentService.searchContactsFromCommonPool(condition);
        return ResponseEntity.ok(contacts);
    }

    /**
     * ------------------------------------------- For Parser Use ONLY -------------------------------------------
     */
    @PostMapping(value = "/talents/contacts/search", consumes = "application/json; charset=utf-8")
    @Timed
    public ResponseEntity<List<TalentEmailContactVO>> searchTalentEmailContacts(@RequestBody List<Long> talentIdList) throws IOException {
        log.info("[APN: Talent @{}] REST request to search Talent email contacts : {}", SecurityUtils.getUserId(), talentIdList);
        List<TalentEmailContactVO> contactVOList = talentService.searchTalentEmailContacts(talentIdList);
        return new ResponseEntity<>(contactVOList, HttpStatus.OK);
    }

    @GetMapping("/talents/search-by-tenantId/{tenantId}")
    @Timed
    public ResponseEntity<List<Long>> findIdsByTenant(@PathVariable("tenantId") Long tenantId) {
        log.info("[APN: Talent ] REST findIdsByTenant: {}", tenantId);
        List<Long> idList = talentService.findIdsByTenant(tenantId);
        return ResponseEntity.ok(idList);
    }

    /***
     * Job V3 statistics and search history
     * @return
     * @throws Throwable es error
     */
    @ApiOperation(value = "Count talent")
    @GetMapping(value = "/talents/statistics")
    public ResponseEntity<List<CategoryCountDTO>> getTalentStatistics() throws Throwable {
        log.info("({},{}) REST request to get Talent Statistics:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        List<CategoryCountDTO> talentStatistics= talentServiceV3.getTalentSearchCategoryStatistics();
        return ResponseEntity.ok(talentStatistics);
    }

    /***
     * Query Talent search history: current not use in front end
     * @return List<String> keyword</String>
     */
    @GetMapping("/talents/search/history")
    @ApiOperation(value = "query talent search history")
    public ResponseEntity<List<String>> querySearchHistory() {
        log.info("[APN: Talent Search History @{}] REST request to query talent search history.", SecurityUtils.getUserId());
        List<String> searchHistoryList = talentServiceV3.querySearchHistory();
        return new ResponseEntity<>(searchHistoryList, HttpStatus.OK);
    }

    /***
     * get talent update activity log
     * @param talentId
     * @param pageable
     * @return
     * @throws Throwable
     */
    @PostMapping("/talents/{talentId}/talent-activities")
    @ApiOperation(value = "query talent activities log")
    public ResponseEntity<List<TalentActivityDTO>> getTalentActivities(@PathVariable("talentId") Long talentId, Pageable pageable) throws Throwable {
        log.info("[APN: Talent Activity @{}] REST request to query talent activity.", SecurityUtils.getUserId());
        Page<TalentActivityDTO> talentActivities = talentActivityService.getTalentActivities(talentId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(talentActivities, "/talents/" + talentId +"/talent-activities");
        return new ResponseEntity<>(talentActivities.getContent(), headers, HttpStatus.OK);
    }

    /**
     * unlock common talent
     * map => {"id1":["tenant1","tenant2]}
     * @return
     */
    @PutMapping("/talents/common-pool/unlock")
    public ResponseEntity<Void> unlockCommonTalent(@RequestBody TalentUnlockListDto talentUnlockListDto) {
        Map<String, List<String>> map = new HashMap<>();
        talentUnlockListDto.getTalentUnlockDtoList().forEach(talentUnlockDto -> map.put(talentUnlockDto.getId(), talentUnlockDto.getTenantIds()));
        esFillerTalentService.unlockCommonTalent(map, false);
        return ResponseEntity.ok().build();
    }

    /**
     * ------------------------------------------- For application Use ONLY -------------------------------------------
     */
    @GetMapping("/talents/fullName/{talentId}")
    @Timed
    public ResponseEntity<String> findFullNameByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("[APN: Talent ] REST find FullName By TalentId: {}", talentId);
        String fullName = talentService.getTalentName(talentId);
        return ResponseEntity.ok(fullName);
    }

    @GetMapping("/talents/resumes/fill-resume-infos")
    public ResponseEntity<Object> fillResumeInfos(){
        return new ResponseEntity<>(talentService.fillResumeInfos(), HttpStatus.OK);
    }

    /**
     *
     * @param excelTaskDto
     * @return
     */
    @PostMapping("/talents/find-create-talent-progress-by-taskIds")
    public ResponseEntity<List<ExcelTalentProcessVo>> findTalentProgressByTaskIds(@RequestBody ExcelTaskDto excelTaskDto) {
        return new ResponseEntity<>(talentService.findTalentProgressByTaskIds(excelTaskDto.getTaskIdList()), HttpStatus.OK);
    }

    @PostMapping("/talents/update-task-status")
    public ResponseEntity<Void> updateTaskStatus(@RequestBody UpdateExcelTaskDto excelTaskDto) {
        talentService.updateTaskStatus(excelTaskDto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/talents/create-talent-by-excel/upload-url")
    public ResponseEntity<StoreGetUploadUrlVO> getUploadUrlForCreateTalentByExcel(@RequestBody TalentExcelUploadUrlDto uploadUrlDto) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            ModuleType moduleType = ModuleType.CANDIDATE;
            if (ObjectUtil.isNotEmpty(uploadUrlDto.getHotListId())) {
                moduleType = ModuleType.BIO_COMMERCIAL_TALENT_POOL;
            }
            jobService.updateColumnPreferenceByUserId(SecurityUtils.getUserId(), CreationTalentType.BULK_CREATE_WITH_EXCEL, moduleType);
        });
        return new ResponseEntity<>(talentService.getUploadUrlForCreateTalentByExcel(uploadUrlDto), HttpStatus.OK);
    }

    @PostMapping("/talents/create-talent-by-excel-result/download")
    public void downloadCreateTalentResultByExcel(@RequestBody ExcelTaskDto excelTaskDto, HttpServletResponse httpServletResponse) {
        talentService.downloadCreateTalentResultByExcel(excelTaskDto, httpServletResponse);
    }

    /**
     * 更新候选人工作经历，如果之前没有该工作记录，则新增
     * @param talentId 候选人ID
     * @param talentExperienceDTO 工作经历明细
     * @return Void
     */
    @PutMapping("/talents/{talentId}/experience")
    public ResponseEntity<Void> updateTalentExperience(@PathVariable("talentId") Long talentId, @RequestBody TalentExperienceDTO talentExperienceDTO){
        log.info("[APN: TalentResource @{}] REST request to upsert talent experience. talent ID: {}, TalentExperienceDTO: {}", SecurityUtils.getUserId(), talentId, talentExperienceDTO);
        talentService.upsertExperience(talentId, talentExperienceDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 根据流程ID删除候选人对应的工作经历
     * @param talentRecruitmentProcessId 流程ID
     * @return Void
     */
    @DeleteMapping("/talents/{talentId}/experience/talent-recruitment-process/{talentRecruitmentProcessId}")
    public ResponseEntity<Void> deleteTalentExperience(@PathVariable("talentId") Long talentId, @PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId){
        log.info("[APN: TalentResource @{}] REST request to delete talent experience by talentRecruitmentProcessId. Talent ID: {}, talentRecruitmentProcessId: {}", SecurityUtils.getUserId(), talentId, talentRecruitmentProcessId);
        talentService.deleteExperienceByTalentRecruitmentProcessId(talentId, talentRecruitmentProcessId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }

    @PutMapping("/talent/sync-to-hr/{talentId}")
    public void updateTalentNeedSyncToHr(@PathVariable("talentId") Long talentId) {
        talentRepository.updateTalentNeedSyncToHr(talentId);
    }

    @GetMapping("/talent/sync-to-hr/{talentId}")
    public ResponseEntity<TalentToHrVO> getTalentDataSyncToHr(@PathVariable("talentId") Long talentId) {
        return ResponseEntity.ok(talentRepository.getTalentDataSyncToHr(List.of(talentId)).get(0));
    }

    @PostMapping("/brief-talents")
    @Timed
    public ResponseEntity<Set<TalentBriefVO>> findBriefTalentsByTalentIds(@RequestBody Set<Long> talentIds) {
        log.info("[APN: Talent @{}] REST find FullName By TalentId: {}", SecurityUtils.getUserId(), talentIds);
        Set<TalentBriefVO> briefTalents = talentService.getAllBriefTalentsByTalentIds(talentIds);
        return ResponseEntity.ok(briefTalents);
    }
}
