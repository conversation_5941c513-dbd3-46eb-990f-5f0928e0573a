package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormater;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.dict.*;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.job.JobAdditionalInfo;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.Resume;
import com.altomni.apn.common.domain.talent.TalentAdditionalInfo;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalInterfaceException;
import com.altomni.apn.common.repository.enums.EnumWorkAuthorizationRepository;
import com.altomni.apn.common.repository.talent.ResumeRepository;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.CnReportPDFFormat;
import com.altomni.apn.talent.constants.EnReportPDFFormat;
import com.altomni.apn.talent.constants.ReportPDFFormat;
import com.altomni.apn.talent.constants.ReportTemplateConstants;
import com.altomni.apn.talent.domain.enumeration.LanguageEnum;
import com.altomni.apn.talent.domain.enumeration.RateUnitType;
import com.altomni.apn.talent.domain.enumeration.ReportTypeEnum;
import com.altomni.apn.talent.domain.enumeration.TemplateTypeEnum;
import com.altomni.apn.talent.domain.enumeration.talent.RecommendTemplateType;
import com.altomni.apn.talent.domain.talent.RecommendedReportTemplate;
import com.altomni.apn.talent.domain.talent.TalentCurrentLocation;
import com.altomni.apn.talent.domain.template.*;
import com.altomni.apn.talent.repository.talent.RecommendedReportTemplateRepository;
import com.altomni.apn.talent.repository.talent.TalentContactRepository;
import com.altomni.apn.talent.repository.talent.TalentLocationRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.CompanyService;
import com.altomni.apn.talent.service.dto.company.CompanyDTO;
import com.altomni.apn.talent.service.parser.ParserService;
import com.altomni.apn.talent.service.store.StoreService;
import com.altomni.apn.talent.service.talent.RecommendationReportService;
import com.altomni.apn.talent.service.talent.TalentResumeService;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.web.rest.talent.dto.GetRecommendationReportInput;
import com.altomni.apn.talent.web.rest.talent.dto.GetRecommendedReasonInput;
import com.altomni.apn.user.repository.user.UserRepository;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.xwpf.NumFormat;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.itextpdf.text.*;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import com.spire.doc.FileFormat;
import com.spire.doc.collections.SectionCollection;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.altomni.apn.talent.constants.ReportTemplateConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendationReportServiceImpl implements RecommendationReportService {

    private static final Font normalFont = FontFactory.getFont(ReportTemplateConstants.PINGFANG_FONT, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 10, -1, BaseColor.BLACK);
    private static final Font boldFont = new Font(normalFont.getBaseFont(), 10, Font.BOLD, BaseColor.BLACK);
    private static final Font greyNormalFont = new Font(normalFont.getBaseFont(), 10, Font.NORMAL, BaseColor.GRAY);

    private static int RESUME_MAX_LENGTH = 2600;
    private static int JD_MAX_LENGTH = 1200;
    private static List<String> removeResumeKeyOrder = List.of("projects", "experiences", "educations", "skills");
    private static final String paraSplit = "⦁";
    private static final String enterSplit = "\n";

    private final JobRepository jobRepository;
    private final TalentRepository talentRepository;
    private final UserRepository userRepository;
    private final ResumeRepository resumeRepository;
    private final TalentLocationRepository talentLocationRepository;
    private final TalentContactRepository talentContactRepository;
    private final ParserService parserService;
    private final EnumCommonService enumCommonService;
    private final TalentService talentService;
    private final CompanyService companyService;
    private final StoreService storeService;
    private final TalentResumeService talentResumeService;
    private final ApplicationProperties applicationProperties;
    private final EnumWorkAuthorizationRepository enumWorkAuthorizationRepository;
    private final RecommendedReportTemplateRepository recommendedReportTemplateRepository;
    private final CommonApiMultilingualConfig commonApiMultilingualConfig;
    private final TalentApiPromptProperties talentApiPromptProperties;

    @Override
    public String getRecommendedReason(GetRecommendedReasonInput input) {
        TalentServiceImpl.RecommendTemplate recommendTemplate = parseRecommendTemplate(input.getTemplateId());
        Optional<JobV3> job = jobRepository.findById(input.getRecommendationJobId());
        if(job.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDEDREASON_JOBNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        Optional<TalentV3> talent = talentRepository.findById(input.getTalentId());
        if(talent.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDEDREASON_TALENTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        return requestRecommendedReason(job.get(), recommendTemplate.getLanguageEnum(), talent.get());
    }

    private String requestRecommendedReason(JobV3 jobV3, LanguageEnum languageEnum, TalentV3 talentV3) {
        JSONObject body = getRequestRecommendedReasonParam(jobV3, languageEnum, talentV3);
        String authorization =  getRequestRecommendedReasonAuthorization();
        String recommendReasonUrl = applicationProperties.getRecommendReasonUrl();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", authorization);
        HttpRequest httpRequest = cn.hutool.http.HttpUtil.createPost(recommendReasonUrl).body(body).addHeaders(headers).setConnectionTimeout(30000).setReadTimeout(30000);
        try (cn.hutool.http.HttpResponse response = httpRequest.execute()) {
            if (response.getStatus() == cn.hutool.http.HttpStatus.HTTP_OK) {
                String result = response.body();
                JSONObject jsonObject = JSONUtil.parseObj(result);
                return jsonObject.getStr("message");
            } else {
                log.error("Request recommended reason error, status: {}", response.getStatus());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_REQUESTRECOMMENDEDREASON_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
            }
        } catch (Exception e) {
            log.error("Request recommended reason error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_REQUESTRECOMMENDEDREASON_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private String getRequestRecommendedReasonAuthorization() {
        String authKey = applicationProperties.getAuthKey();
        String dateString = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        return DigestUtil.md5Hex(authKey + dateString);

    }

    private JSONObject getRequestRecommendedReasonParam(JobV3 jobV3, LanguageEnum languageEnum, TalentV3 talentV3) {
        JobAdditionalInfo jobAdditionalInfo = jobV3.getJobAdditionalInfo();
        if(jobAdditionalInfo == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETREQUESTRECOMMENDEDREASONPARAM_PARAMNULLVALUE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        String requirements = jobAdditionalInfo.getRequirements() != null ? cn.hutool.http.HtmlUtil.cleanHtmlTag(jobAdditionalInfo.getRequirements()) : "";
        String summary = jobAdditionalInfo.getSummary() != null ? cn.hutool.http.HtmlUtil.cleanHtmlTag(jobAdditionalInfo.getSummary()) : "";
        String responsibilities = jobAdditionalInfo.getResponsibilities() != null ? cn.hutool.http.HtmlUtil.cleanHtmlTag(jobAdditionalInfo.getResponsibilities()) : "";
        if(requirements.length() + summary.length() + responsibilities.length() > JD_MAX_LENGTH) {
            summary = "";
        }
        if(requirements.length() + summary.length() + responsibilities.length() > JD_MAX_LENGTH) {
            responsibilities = "";
        }
        if(requirements.length() + summary.length() + responsibilities.length() > JD_MAX_LENGTH) {
            requirements = "";
        }
        String jd =  StrUtil.format("requirements: {}, summary: {}, responsibilities: {}", requirements, summary, responsibilities);
        String resume = getTalentResume(talentV3);
        if(languageEnum == null|| resume == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETREQUESTRECOMMENDEDREASONPARAM_PARAMNULLVALUE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        JSONObject request = new JSONObject();
        request.put("lang", languageEnum.getDesc());
        request.put("jd", jd);
        request.put("resume", resume);

        return request;
    }

    private String getTalentResume(TalentV3 talentV3) {
        TalentAdditionalInfo talentAdditionalInfo = talentV3.getTalentAdditionalInfo();
        if(talentAdditionalInfo != null) {
            return compressResume(talentAdditionalInfo.getExtendedInfo());

        }
        return null;
    }


    private String compressResume(String extendedInfo) {
        JSONObject origin = new JSONObject(extendedInfo);
        JSONObject compress = new JSONObject();
        if(origin.containsKey("skills")) {
            compress.put("skills", origin.get("skills"));
        }
        if(origin.containsKey("educations")) {
            compress.put("educations", origin.get("educations"));
        }
        if(origin.containsKey("experiences")) {
            cn.hutool.json.JSONArray experiences = origin.getJSONArray("experiences");
            compress.put("experiences", experiences.size() < 3 ? experiences : experiences.subList(0, 3));
        }
        if(origin.containsKey("projects")) {
            cn.hutool.json.JSONArray projects = origin.getJSONArray("projects");
            compress.put("projects", projects.size() < 3 ? projects : projects.subList(0, 3));
        }
        int i = 0;
        while(compress.toString().length() > RESUME_MAX_LENGTH && i < removeResumeKeyOrder.size()) {
            compress.remove(removeResumeKeyOrder.get(i++));
        }
        return compress.toString();
    }


    @Override
    public ResponseEntity<ByteArrayResource> getRecommendationReport(GetRecommendationReportInput input) {
        TalentServiceImpl.RecommendTemplate recommendTemplate = parseRecommendTemplate(input.getTemplateId());
        Optional<JobV3> job = jobRepository.findById(input.getRecommendationJobId());
        if (job.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDEDREASON_JOBNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        Optional<TalentV3> talent = talentRepository.findById(input.getTalentId());
        if(talent.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDEDREASON_TALENTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        Optional<User> user = userRepository.findById(input.getUserId());
        if(user.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDATIONREPORT_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
            HttpHeaders headers = new HttpHeaders();
            switch (recommendTemplate.getRecommendTemplateType()) {
                case RESUME:
                    generateFileByResume(headers, recommendTemplate, job.get(), talent.get(), user.get(), input, outputStream);
                    break;
                case TEMPLATE:
                    Optional<RecommendedReportTemplate> templateOpt = recommendedReportTemplateRepository.findById(recommendTemplate.getId());
                    if(templateOpt.isEmpty()) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDEDREASON_TEMPLATENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
                    }
                    RecommendedReportTemplate template = templateOpt.get();
                    ReportTypeEnum reportTypeEnum = ReportTypeEnum.fromDbValue(template.getReportType());
                    if(reportTypeEnum == null) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDEDREASON_TEMPLATENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
                    }
                    TemplateTypeEnum type = template.getType();
                    if(type == null) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDEDREASON_TEMPLATENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
                    }
                    if(type == TemplateTypeEnum.STANDARD) {
                        generateFileByTemplate(headers, reportTypeEnum, job.get(), template, talent.get(), user.get(), input, outputStream);
                    } else if(type == TemplateTypeEnum.CUSTOMIZED) {
                        generateFileByOutsourcingConsultantTemplate(headers, reportTypeEnum, job.get(), template, talent.get(), user.get(), input, outputStream);
                    }
                    break;
            }
            ByteArrayResource byteArrayResource = new ByteArrayResource(outputStream.toByteArray());
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(byteArrayResource);
        } catch (Exception e) {
            log.error("Export recommendation report exception.", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_GETRECOMMENDATIONREPORT_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private void generateFileByOutsourcingConsultantTemplate(HttpHeaders headers, ReportTypeEnum reportTypeEnum, JobV3 jobV3, RecommendedReportTemplate template, TalentV3 talentV3, User user, GetRecommendationReportInput input, ByteArrayOutputStream outputStream) throws Exception {
        String config = template.getConfig();
        if(StringUtils.isEmpty(config)) {
            throw new CustomParameterizedException("Customized template need config.");
        }
        CustomizedTemplateConfig templateConfig = JSON.parseObject(config, CustomizedTemplateConfig.class);
        if (ReportTypeEnum.PDF.equals(reportTypeEnum)) {
            // 设置下载头
            setDownloadHeader(headers, org.springframework.http.MediaType.APPLICATION_PDF, getExportFileName(templateConfig.getFileName(), jobV3, talentV3));

            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(templateConfig.getTemplateFilePath());
            PdfReader reader = new PdfReader(inputStream);

            // 创建 PdfStamper 用于写入输出流
            ByteArrayOutputStream reportOutputStream = new ByteArrayOutputStream();
            PdfStamper stamper = new PdfStamper(reader, reportOutputStream);
            AcroFields form = stamper.getAcroFields();

            Map<String, Object> param = getCustomizedParamMap(templateConfig.getTemplateParam(), jobV3, talentV3, input,  LanguageEnum.fromDbValue(template.getReportLanguage()), reportTypeEnum);

            // 根据参数中的内容来填充表单域
            for (String key : param.keySet()) {
                if (ObjectUtil.isNotNull(param.get(key))) {
                    if (key.equals(TEMPLATE_PARAM_CLIENT_COMPANY_LOGO)) {
                        String imageUrl = StrUtil.toString(param.get(key));
                        Image image = Image.getInstance(imageUrl);
                        insertImageAtPage(stamper, form.getFieldPositions(TEMPLATE_PARAM_CLIENT_COMPANY_LOGO).get(0).page, image, 70, 70, 100, 50);
                    } else {
                        // 填充文本表单域
                        form.setField(key, StrUtil.toString(param.get(key)));
                    }
                }
            }
            // 提交修改
            stamper.close();
            if (param.containsKey(TEMPLATE_PARAM_RESUME_DISPLAY) && param.get(TEMPLATE_PARAM_RESUME_DISPLAY) != null && param.get(TEMPLATE_PARAM_RESUME_DISPLAY) instanceof List) {
                List<byte[]> imageResumeList = (List<byte[]>) param.get(TEMPLATE_PARAM_RESUME_DISPLAY);
                // 合并简历图片到 pdf
                insertResumeImageToPdf(imageResumeList, reportOutputStream, outputStream);
            } else {
                reportOutputStream.writeTo(outputStream);
            }
        } else {
            setDownloadHeader(headers, org.springframework.http.MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"), getExportFileName(templateConfig.getFileName(), jobV3, talentV3));
            try (
                    InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(templateConfig.getTemplateFilePath());
                    XWPFDocument document = new XWPFDocument(inputStream);
                    XWPFTemplate xwpfTemplate = XWPFTemplate.compile(document)) {
                Map<String, Object> param = getCustomizedParamMap(templateConfig.getTemplateParam(), jobV3, talentV3, input,  LanguageEnum.fromDbValue(template.getReportLanguage()), reportTypeEnum);
                XWPFTemplate render = xwpfTemplate.render(param);
                render.writeAndClose(outputStream);
            } catch (IOException e) {
                throw new CustomParameterizedException("generate sourcing consultant template failure.");
            }
        }
    }

    /**
     * Inserts resume images into the PDF and merges them with the original template.
     * The process involves:
     * 1. Creating a template with multiple copies of the resume page
     * 2. Adding resume images to each page of the template
     * 3. Merging the original PDF with the resume PDF
     * 4. Adding page numbers to the final PDF
     *
     * @param resumeImages List of resume images as byte arrays
     * @param originalPdfStream The original PDF template stream
     * @param finalOutputStream The output stream for the final PDF
     * @throws Exception If there's an error processing the PDF
     */
    private void insertResumeImageToPdf(List<byte[]> resumeImages, ByteArrayOutputStream originalPdfStream, 
            ByteArrayOutputStream finalOutputStream) throws Exception {
        // Get the resume page number from the original PDF
        int resumePageNum;
        PdfReader originalReader = null;
        try {
            originalReader = new PdfReader(new ByteArrayInputStream(originalPdfStream.toByteArray()));
            AcroFields form = originalReader.getAcroFields();
            AcroFields.FieldPosition resumePosition = form.getFieldPositions(TEMPLATE_PARAM_RESUME_DISPLAY).get(0);
            resumePageNum = resumePosition.page;
        } finally {
            if (originalReader != null) {
                originalReader.close();
            }
        }

        // Step 1: Create resume template PDF with multiple copies of the resume page
        ByteArrayOutputStream resumeTemplateStream = new ByteArrayOutputStream();
        PdfReader templateReader = null;
        Document templateDocument = null;
        PdfCopy templateCopy = null;
        try {
            templateReader = new PdfReader(new ByteArrayInputStream(originalPdfStream.toByteArray()));
            templateDocument = new Document();
            templateCopy = new PdfCopy(templateDocument, resumeTemplateStream);
            
            templateDocument.open();
            PdfImportedPage resumePage = templateCopy.getImportedPage(templateReader, resumePageNum);
            
            // Add the resume page multiple times (one for each image)
            for (int i = 0; i < resumeImages.size(); i++) {
                templateCopy.addPage(resumePage);
            }
        } finally {
            if (templateCopy != null) {
                templateCopy.close();
            }
            if (templateDocument != null && templateDocument.isOpen()) {
                templateDocument.close();
            }
            if (templateReader != null) {
                templateReader.close();
            }
        }
        
        // Step 2: Add resume images to the template
        ByteArrayOutputStream resumeWithImagesStream = new ByteArrayOutputStream();
        PdfReader imageReader = null;
        PdfStamper imageStamper = null;
        try {
            imageReader = new PdfReader(new ByteArrayInputStream(resumeTemplateStream.toByteArray()));
            imageStamper = new PdfStamper(imageReader, resumeWithImagesStream);
            
            // Add each resume image to its corresponding page
            for (int i = 0; i < resumeImages.size(); i++) {
                PdfContentByte overContent = imageStamper.getOverContent(i + 1);
                byte[] imageBytes = resumeImages.get(i);
                Image image = Image.getInstance(imageBytes);
                image.scaleToFit(600, 600);
                image.setAbsolutePosition(80, 100);
                overContent.addImage(image);
            }
        } finally {
            if (imageStamper != null) {
                imageStamper.close();
            }
            if (imageReader != null) {
                imageReader.close();
            }
        }
        
        // Step 3: Merge the original PDF with the resume PDF
        ByteArrayOutputStream mergedPdfStream = new ByteArrayOutputStream();
        PdfReader mergeOriginalReader = null;
        PdfReader mergeResumeReader = null;
        Document mergeDocument = null;
        PdfCopy mergeCopy = null;
        try {
            mergeOriginalReader = new PdfReader(new ByteArrayInputStream(originalPdfStream.toByteArray()));
            mergeResumeReader = new PdfReader(new ByteArrayInputStream(resumeWithImagesStream.toByteArray()));
            mergeDocument = new Document();
            mergeCopy = new PdfCopy(mergeDocument, mergedPdfStream);
            
            mergeDocument.open();
            
            // Copy pages before the resume page from the original PDF
            for (int i = 1; i < resumePageNum; i++) {
                mergeCopy.addPage(mergeCopy.getImportedPage(mergeOriginalReader, i));
            }
            
            // Copy all pages from the resume PDF
            for (int i = 1; i <= mergeResumeReader.getNumberOfPages(); i++) {
                mergeCopy.addPage(mergeCopy.getImportedPage(mergeResumeReader, i));
            }
            
            // Copy pages after the resume page from the original PDF
            for (int i = resumePageNum + 1; i <= mergeOriginalReader.getNumberOfPages(); i++) {
                mergeCopy.addPage(mergeCopy.getImportedPage(mergeOriginalReader, i));
            }
        } finally {
            if (mergeCopy != null) {
                mergeCopy.close();
            }
            if (mergeDocument != null && mergeDocument.isOpen()) {
                mergeDocument.close();
            }
            if (mergeResumeReader != null) {
                mergeResumeReader.close();
            }
            if (mergeOriginalReader != null) {
                mergeOriginalReader.close();
            }
        }
        
        // Step 4: Add page numbers to the final PDF
        addPageNumbers(new ByteArrayInputStream(mergedPdfStream.toByteArray()), finalOutputStream);
    }
    
    /**
     * Adds page numbers to the bottom right corner of each page in the PDF
     * 
     * @param inputPdfStream Input stream of the PDF file
     * @param outputStream Output stream to write the PDF with page numbers
     * @throws Exception If there's an error processing the PDF
     */
    private void addPageNumbers(InputStream inputPdfStream, OutputStream outputStream) throws Exception {
        PdfReader reader = null;
        PdfStamper stamper = null;
        try {
            reader = new PdfReader(inputPdfStream);
            stamper = new PdfStamper(reader, outputStream);
            
            int totalPages = reader.getNumberOfPages();
            
            // Set up the font for page numbers
            Font pageNumberFont = new Font(normalFont.getBaseFont(), 12, Font.NORMAL, BaseColor.BLACK);
            
            // Add page numbers to each page
            for (int i = 1; i <= totalPages; i++) {
                PdfContentByte canvas = stamper.getOverContent(i);
                Rectangle pageSize = reader.getPageSize(i);
                
                // Create the page number text
                String pageNumber = String.valueOf(i);
                
                // Create a phrase with the page number
                Phrase phrase = new Phrase(pageNumber, pageNumberFont);
                
                // Position the page number in the bottom right corner with some margin
                float x = pageSize.getRight() / 2;
                float y = pageSize.getBottom() + 30;
                
                // Add the page number to the page
                ColumnText.showTextAligned(canvas, Element.ALIGN_RIGHT, phrase, x, y, 0);
            }
        } finally {
            if (stamper != null) {
                stamper.close();
            }
            if (reader != null) {
                reader.close();
            }
        }
    }

    // 在指定页面插入图片
    private void insertImageAtPage(PdfStamper stamper, int pageNumber, Image image, float width, float height, float widthIndex, float heightIndex) throws Exception {
        PdfContentByte canvas = stamper.getOverContent(pageNumber);

        image.scaleAbsolute(width, height);
        // 设置图片的位置和大小
        image.setAbsolutePosition(widthIndex, heightIndex);

        // 将图片插入到页面中
        canvas.addImage(image);
    }


    private String getContactTemplateDesc(List<TalentContact> contacts, ContactType contactType) {
        List<String> list = new ArrayList<>();
        for (TalentContact contact : contacts) {
            if (contact.getType().equals(contactType)) {
                list.add(contact.getContact());
            }
        }
        if(list.isEmpty()) {
            return null;
        }

        return String.join(",", list);
    }

    private Integer getTalentAge(String birthday) {
        if(StringUtils.isEmpty(birthday)) {
            return null;
        }
        Date birthDate = DateUtil.parse(birthday); // 将字符串转换为日期
        return DateUtil.ageOfNow(birthDate);
    }

    private NumberingRenderData getRecommendedReason(GetRecommendationReportInput input) {
        NumberingFormat numberingFormat = new NumberingFormat(NumFormat.BULLET, "\uF0D8");
        Numberings.NumberingBuilder numberingBuilder = Numberings.of(numberingFormat);
        String recommendationReason = input.getRecommendationReason();
        if(StringUtils.isEmpty(recommendationReason)) {
            return numberingBuilder.create();
        }
        String[] split = recommendationReason.split(enterSplit);
        for(String str : split) {
            if(StringUtils.isNotEmpty(str.trim())) {
                numberingBuilder.addItem(str);
            }
        }

        return numberingBuilder.create();
    }

    private Map<String, Object> getCustomizedParamMap(List<String> params, JobV3 jobV3, TalentV3 talentV3, GetRecommendationReportInput input, LanguageEnum languageEnum, ReportTypeEnum reportTypeEnum) throws Exception {
        List<EnumGenderIdentity> allEnumGender = enumCommonService.findAllEnumGender();
        Map<String, Object> map = new HashMap<>();
        TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talentV3);
        talentService.sortTalentDTO(talentDTOV3);
        List<TalentContact> contacts = talentContactRepository.findAllByTalentIdAndStatusAndTypeIn(talentV3.getId(), TalentContactStatus.AVAILABLE, List.of(ContactType.PHONE, ContactType.EMAIL));
        for(String param : params) {
            switch (param) {
                case ReportTemplateConstants.TEMPLATE_PARAM_JOB_TITLE -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_JOB_TITLE, jobV3.getTitle());
                case ReportTemplateConstants.TEMPLATE_PARAM_COMPANY_NAME -> {
                    Long companyId = jobV3.getCompanyId();
                    ResponseEntity<CompanyDTO> companyRes = companyService.findById(companyId);
                    CompanyDTO companyDTO = companyRes.getBody();
                    if(companyDTO != null) {
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_COMPANY_NAME, companyDTO.getName());
                    }
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_TALENT_NAME ->
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_TALENT_NAME, talentV3.getFullName());
                case ReportTemplateConstants.TEMPLATE_PARAM_PHONES ->
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_PHONES, getContactTemplateDesc(contacts, ContactType.PHONE));
                case ReportTemplateConstants.TEMPLATE_PARAM_EMAILS ->
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_EMAILS, getContactTemplateDesc(contacts, ContactType.EMAIL));
                case ReportTemplateConstants.TEMPLATE_PARAM_GENDER -> {
                    String gender = getGender(talentV3);
                    Optional<EnumGenderIdentity> genderOptional = allEnumGender.stream().filter(p -> p.getName().equals(gender)).findFirst();
                    genderOptional.ifPresent(c -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_GENDER, getGenderDisplay(c, languageEnum)));
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_AGE ->
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_AGE, getTalentAge(talentV3.getBirthday()));
                case ReportTemplateConstants.TEMPLATE_PARAM_CURRENT_LOCATION -> {
                    TalentCurrentLocation currentLocation = talentLocationRepository.findByTalentId(talentV3.getId());
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_CURRENT_LOCATION, getCurrentLocationDisplay(currentLocation, languageEnum));
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_PERFERRED_LOCATION -> {
                    String extendedInfo = talentV3.getTalentAdditionalInfo().getExtendedInfo();
                    com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extendedInfo);
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_PERFERRED_LOCATION, getPreferredLocations(jsonObject, languageEnum));
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION, getEducationExperienceParamsByEntity(talentDTOV3.getEducations(), languageEnum));
                case ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION_ASC -> {
                    List<TalentEducationDTO> educations = talentDTOV3.getEducations() != null ? talentDTOV3.getEducations() : new ArrayList<>();
                    List<TalentEducationDTO> reversedList = educations.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                                Collections.reverse(list);
                                return list;
                            }));
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION, getEducationExperienceParamsByEntity(reversedList, languageEnum));
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_LANGUAGE -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_LANGUAGE, getLanguageParam(talentV3.getLanguages(), languageEnum));
                case ReportTemplateConstants.TEMPLATE_PARAM_CURRENT_SALARY -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_CURRENT_SALARY, getSalary(talentV3.getTalentExtendedInfo(), languageEnum));
                case ReportTemplateConstants.TEMPLATE_PARAM_PREFERRED_SALARY -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_PREFERRED_SALARY, getPreferredSalary(talentV3.getTalentExtendedInfo(), languageEnum));
                case ReportTemplateConstants.SOURCING_CONSULTANT_TEMPLATE_PARAM_RECOMMENDED_REASON -> map.put(ReportTemplateConstants.SOURCING_CONSULTANT_TEMPLATE_PARAM_RECOMMENDED_REASON, getRecommendedReason(input));
                case ReportTemplateConstants.SOURCING_CONSULTANT_TEMPLATE_PARAM_WORD_EXPERIENCE -> map.put(ReportTemplateConstants.SOURCING_CONSULTANT_TEMPLATE_PARAM_WORD_EXPERIENCE, getSourcingConsultantWorkExperienceParams(talentDTOV3.getExperiences(), languageEnum));
                case ReportTemplateConstants.SOURCING_CONSULTANT_TEMPLATE_PARAM_PROJECT_EXPERIENCE -> map.put(ReportTemplateConstants.SOURCING_CONSULTANT_TEMPLATE_PARAM_PROJECT_EXPERIENCE, getConsultantProjectExperienceParams(talentDTOV3.getProjects(), languageEnum));
                case ReportTemplateConstants.GEELY_TEMPLATE_PARAM_WORD_EXPERIENCE -> map.put(ReportTemplateConstants.GEELY_TEMPLATE_PARAM_WORD_EXPERIENCE, getGeelyWorkExperienceParams(talentDTOV3.getExperiences(), talentDTOV3.getProjects(), languageEnum));
                case ReportTemplateConstants.CV_FORMAT_TEMPLATE_PARAM_EDUCATION -> map.put(ReportTemplateConstants.CV_FORMAT_TEMPLATE_PARAM_EDUCATION, getCVFormatEducationExperienceParamsByEntity(talentDTOV3.getEducations(), languageEnum, "yyyy"));
                case ReportTemplateConstants.MALAYSIA_TEMPLATE_PARAM_EDUCATION -> map.put(ReportTemplateConstants.MALAYSIA_TEMPLATE_PARAM_EDUCATION, getCVFormatEducationExperienceParamsByEntity(talentDTOV3.getEducations(), languageEnum, "MM yyyy"));
                case ReportTemplateConstants.CONSULTANT_PRESENTATION_EDUCATION -> map.put(ReportTemplateConstants.CONSULTANT_PRESENTATION_EDUCATION, getConsultantPresentationEducationExperienceParams(getCVFormatEducationExperienceParamsByEntity(talentDTOV3.getEducations(), languageEnum, "yyyy.MM")));
                case ReportTemplateConstants.CONSULTANT_PRESENTATION_WORK_EXPERIENCE -> map.put(ReportTemplateConstants.CONSULTANT_PRESENTATION_WORK_EXPERIENCE, getConsultantPresentationWorkExperienceParams(talentDTOV3.getExperiences(), languageEnum, "yyyy.MM"));
                case ReportTemplateConstants.CONSULTANT_PRESENTATION_PROJECT_EXPERIENCE -> map.put(ReportTemplateConstants.CONSULTANT_PRESENTATION_PROJECT_EXPERIENCE, getConsultantPresentationProjectExperienceParams(talentDTOV3.getProjects(), languageEnum, "yyyy.MM"));
                case ReportTemplateConstants.CV_FORMAT_TEMPLATE_PARAM_WORD_EXPERIENCE -> map.put(ReportTemplateConstants.CV_FORMAT_TEMPLATE_PARAM_WORD_EXPERIENCE, getCVFormatWorkExperienceParams(talentDTOV3.getExperiences(), languageEnum, "MM yyyy"));
                case ReportTemplateConstants.CV_FORMAT_TH_TEMPLATE_PARAM_WORD_EXPERIENCE -> map.put(ReportTemplateConstants.CV_FORMAT_TH_TEMPLATE_PARAM_WORD_EXPERIENCE, getCVFormatWorkExperienceParams(talentDTOV3.getExperiences(), languageEnum, "MM yyyy"));
                case ReportTemplateConstants.MALAYSIA_TEMPLATE_PARAM_WORD_EXPERIENCE -> map.put(ReportTemplateConstants.MALAYSIA_TEMPLATE_PARAM_WORD_EXPERIENCE, getCVFormatWorkExperienceParams(talentDTOV3.getExperiences(), languageEnum, "MM yyyy"));
                case ReportTemplateConstants.DREAME_TEMPLATE_PARAM_WORD_EXPERIENCE -> map.put(ReportTemplateConstants.DREAME_TEMPLATE_PARAM_WORD_EXPERIENCE, getDreameWorkExperienceParams(talentDTOV3.getExperiences(), languageEnum));
                case ReportTemplateConstants.DREAME_TEMPLATE_PARAM_PROJECT_EXPERIENCE -> map.put(ReportTemplateConstants.DREAME_TEMPLATE_PARAM_PROJECT_EXPERIENCE, getDreameProjectExperienceParams(talentDTOV3.getProjects(), languageEnum));

                case ReportTemplateConstants.TEMPLATE_PARAM_FIRST_NAME -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_FIRST_NAME, talentDTOV3.getFirstName());
                case ReportTemplateConstants.TEMPLATE_PARAM_LAST_NAME -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_LAST_NAME, talentDTOV3.getLastName());
                case ReportTemplateConstants.TEMPLATE_PARAM_REPORT_DATE -> {
                    LocalDate now = LocalDate.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
                    String formattedDate = now.format(formatter);
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_REPORT_DATE, formattedDate);
                }
                case TEMPLATE_PARAM_CLIENT_COMPANY_LOGO -> {
                    Long companyId = jobV3.getCompanyId();
                    ResponseEntity<CompanyDTO> companyRes = companyService.findById(companyId);
                    CompanyDTO companyDTO = companyRes.getBody();
                    if (companyDTO != null && ObjectUtil.isNotEmpty(companyDTO.getLogo())) {
                        // 下载logo图片
//                        UrlPictureRenderData pictureData = new UrlPictureRenderData(companyDTO.getLogo());
//                        PictureRenderData pictureData = Pictures.ofUrl(companyDTO.getLogo()).size(100,100).create();
                        if (ReportTypeEnum.PDF.equals(reportTypeEnum)) {
                            map.put(TEMPLATE_PARAM_CLIENT_COMPANY_LOGO, companyDTO.getLogo());
                        } else {
                            map.put(TEMPLATE_PARAM_CLIENT_COMPANY_LOGO, Pictures.ofUrl(companyDTO.getLogo()).size(100, 100).create());
                        }
                    }
                }
                case TEMPLATE_PARAM_RESUME_DISPLAY -> {
                    TalentResumeDTO talentResumeDTO = talentResumeService.findLastByTalentId(talentDTOV3.getId());
                    if (ObjectUtil.isNotEmpty(talentResumeDTO)) {
                        String resumeUrl = storeService.getUrlFromS3(talentResumeDTO.getUuid(), UploadTypeEnum.RESUME_DISPLAYS.getKey()).getBody();
                        if (ObjectUtil.isNotEmpty(resumeUrl)) {
                            if (ReportTypeEnum.PDF.equals(reportTypeEnum)) {
                                // 从 S3 下载 PDF 文件
                                try (InputStream inputStream = new URL(resumeUrl).openStream()) {
                                    // 加载 PDF 文件
                                    List<byte[]> pages = convertPdfToPagesImages(inputStream);

                                    // 将 PDF 内容作为字节数组存入 map
                                    map.put(TEMPLATE_PARAM_RESUME_DISPLAY, pages);

                                } catch (IOException e) {
                                    // 处理异常
                                    e.printStackTrace();
                                }

                            } else {
                                // 从 S3 下载 PDF 文件
                                InputStream inputStream = new URL(resumeUrl).openStream();

                                List<byte[]> pages = convertPdfToPagesImages(inputStream);

                                List<Map<String,PictureRenderData >> pictureDatas = pages.stream().map(bytes -> {
                                    // 将 ByteArrayOutputStream 转换为 InputStream
                                    InputStream imageInputStream = new ByteArrayInputStream(bytes);

                                    // 使用 InputStream 创建 PictureRenderData
                                    return Map.of(TEMPLATE_PARAM_RESUME_DISPLAY_PAGE, Pictures.ofStream(imageInputStream).size(450, 800).create());
                                }).toList();

                                // 将图片数据放入 map 中
                                map.put(TEMPLATE_PARAM_RESUME_DISPLAY, pictureDatas);
                            }
                        }
                    }
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_APN_USER_NAME -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_APN_USER_NAME, SecurityUtils.getUserName());
                case ReportTemplateConstants.TEMPLATE_PARAM_APN_USER_EMAIL -> map.put(ReportTemplateConstants.TEMPLATE_PARAM_APN_USER_EMAIL, SecurityUtils.getEmail());
                case ReportTemplateConstants.TEMPLATE_PARAM_TALENT_EDUCATION -> {
                    if (ReportTypeEnum.PDF.equals(reportTypeEnum)) {
                        List<EducationExperience> educationExperienceList = getEducationExperienceParamsByEntity(talentDTOV3.getEducations(), languageEnum, "yyyy");
                        StringBuilder educationText = new StringBuilder();
                        educationExperienceList.forEach(item -> educationText.append(item.getEducationDate()).append("\t\t\t\t").append(item.getEducationSchool()).append("\n\t\t\t\t\t\t\t\t").append(item.getEducationDegree()).append(item.getEducationMajor()));
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_TALENT_EDUCATION, educationText);
                    } else {
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_TALENT_EDUCATION, getEducationExperienceParamsByEntity(talentDTOV3.getEducations(), languageEnum, "yyyy"));
                    }
                }
                case TEMPLATE_PARAM_WORK_EXPERIENCE_DATE -> {
                    List<WorkExperience> workExperienceList = getWorkExperienceParamsByEntity(talentDTOV3.getExperiences(), languageEnum, "yyyy");
                    StringBuilder workExperienceDate = new StringBuilder();
                    StringBuilder workExperienceDisplay = new StringBuilder();
                    for (int i = 0 ; i < workExperienceList.size() ; i++) {
                        WorkExperience item = workExperienceList.get(i);
                        if (i == 0) {
                            map.put(TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE_DATE, item.getWorkExperienceDate());
                            map.put(ReportTemplateConstants.TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE_SCHOOL_DISPLAY, item.getCompanyName());
                            map.put(ReportTemplateConstants.TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE_POSITION_DISPLAY, item.getCompanyPosition());
                        } else {
                            workExperienceDate.append(item.getWorkExperienceDate()).append("\n\n");
                            workExperienceDisplay.append(item.getCompanyName()).append(" ").append(item.getCompanyPosition()).append("\n\n");
                        }
                    }
                    map.put(TEMPLATE_PARAM_WORK_EXPERIENCE_DATE, workExperienceDate);
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_WORK_EXPERIENCE_DISPLAY, workExperienceDisplay);
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION_DATE -> {
                    List<EducationExperience> educationExperienceList = getEducationExperienceParamsByEntity(talentDTOV3.getEducations(), languageEnum, "yyyy");
                    StringBuilder educationDate = new StringBuilder();
                    StringBuilder educationSchoolDisplay = new StringBuilder();
                    StringBuilder educationDegreeDisplay = new StringBuilder();
                    educationExperienceList.forEach(item -> {
                        educationDate.append(item.getEducationDate()).append("\n\n\n");
                        educationSchoolDisplay.append(item.getEducationSchool()).append("\n\n\n");
                        educationDegreeDisplay.append("\n").append(item.getEducationDegree()).append("  ").append(item.getEducationMajor()).append("\n\n");
                    });
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION_DATE, educationDate);
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION_SCHOOL_DISPLAY, educationSchoolDisplay.toString().replace("null", ""));
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_EDUCATION_DEGREE_DISPLAY, educationDegreeDisplay.toString().replace("null", ""));
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_WORK_EXPERIENCE -> {
                    List<WorkExperience> workExperienceList = getWorkExperienceParamsByEntity(talentDTOV3.getExperiences(), languageEnum, "yyyy");
                    if (CollUtil.isNotEmpty(workExperienceList)) {
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE, workExperienceList.get(0));
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_WORK_EXPERIENCE, workExperienceList.subList(1, workExperienceList.size()));
                    }
                }
                case ReportTemplateConstants.TEMPLATE_PARAM_ASSESSMENT_DISPLAY -> {
                    Long companyId = jobV3.getCompanyId();
                    ResponseEntity<CompanyDTO> companyRes = companyService.findById(companyId);
                    CompanyDTO companyDTO = companyRes.getBody();
                    StringBuilder asssessmentDisplay = new StringBuilder();
                    asssessmentDisplay.append("The following is an assessment of ").append(talentDTOV3.getFullName()).append(" as a candidate for the position of ").append(jobV3.getTitle())
                            .append(" at ").append(ObjectUtil.isNotEmpty(companyDTO) ? companyDTO.getName() : " ").append(". The evaluation was prepared by ").append(SecurityUtils.getUserName()).append(" of IntelliPro Japan K.K...");
                    map.put(ReportTemplateConstants.TEMPLATE_PARAM_ASSESSMENT_DISPLAY, asssessmentDisplay);
                }
            }
        }
        removeNullValues(map);
        return map;
    }

    private List<EducationExperience> getCVFormatEducationExperienceParamsByEntity(List<TalentEducationDTO> educations, LanguageEnum languageEnum, String dataFormat) {
        List<EducationExperience> ret = new ArrayList<>();
        if(educations == null || educations.isEmpty()) {
            return ret;
        }
        List<EnumDegree> allEnumDegree = enumCommonService.findAllEnumDegree();
        for (TalentEducationDTO educationDTO : educations) {
            EducationExperience educationExperience = new EducationExperience();
            educationExperience.setEducationSchool(educationDTO.getCollegeName());
            String name = educationDTO.getDegreeLevel();
            Optional<EnumDegree> enumDegree = allEnumDegree.stream().filter(o -> o.getName().equalsIgnoreCase(name)).findFirst();
            educationExperience.setEducationDegree(getDegreeDisplay(enumDegree, languageEnum));
            educationExperience.setEducationMajor(educationDTO.getMajorName());
            educationExperience.setEducationDate(getExperienceDate(educationDTO.getStartDate(), educationDTO.getEndDate(), educationDTO.getCurrent(), languageEnum, dataFormat));
            ret.add(educationExperience);
        }

        return ret;
    }

    private List<EducationExperience> getConsultantPresentationEducationExperienceParams(List<EducationExperience> experienceDTOList){
        return experienceDTOList.stream().map(item -> {
            if (StringUtils.isNotEmpty(item.getEducationDegree()) && StringUtils.isNotEmpty(item.getEducationMajor())) {
                item.setEducationDegreeOfMajor(item.getEducationDegree() + " of " + item.getEducationMajor());
            } else if (StringUtils.isNotEmpty(item.getEducationDegree())) {
                item.setEducationDegreeOfMajor(item.getEducationDegree());
            } else if (StringUtils.isNotEmpty(item.getEducationMajor())) {
                item.setEducationDegreeOfMajor(item.getEducationMajor());
            }
            return item;
        }).collect(Collectors.toList());
    }

    private List<DreameWorkExperience> getDreameWorkExperienceParams(List<TalentExperienceDTO> experienceDTOList, LanguageEnum languageEnum) {
        List<DreameWorkExperience> workExperienceList = new ArrayList<>();
        if(experienceDTOList == null || experienceDTOList.isEmpty()) {
            return workExperienceList;
        }
        for (TalentExperienceDTO experienceDTO : experienceDTOList) {
            DreameWorkExperience one = new DreameWorkExperience();
            one.setWorkExperienceFormTitle(getWorkExperienceFormTitle(experienceDTO, languageEnum));
            String description = experienceDTO.getDescription();
            if(StringUtils.isNotEmpty(description)) {
                one.setExperienceDescriptionList(getExperienceDescription(description, NumberingFormat.DECIMAL));
            }
            workExperienceList.add(one);
        }

        return workExperienceList;
    }

    private List<DreameWorkExperience> getConsultantPresentationWorkExperienceParams(List<TalentExperienceDTO> experienceDTOList, LanguageEnum languageEnum, String dataFormat) {
        List<DreameWorkExperience> workExperienceList = new ArrayList<>();
        if(experienceDTOList == null || experienceDTOList.isEmpty()) {
            return workExperienceList;
        }
        for (TalentExperienceDTO experienceDTO : experienceDTOList) {
            DreameWorkExperience one = new DreameWorkExperience();
            one.setWorkExperienceFormTitle(getWorkExperienceFormTitleV2(experienceDTO, languageEnum,dataFormat));
            String description = experienceDTO.getDescription();
            if(StringUtils.isNotEmpty(description)) {
                one.setExperienceDescriptionList(getExperienceDescription(description, NumberingFormat.DECIMAL));
            }
            workExperienceList.add(one);
        }

        return workExperienceList;
    }

    private List<DreameProjectExperience> getConsultantPresentationProjectExperienceParams(List<TalentProjectDTO> projectDTOList, LanguageEnum languageEnum, String dataFormat) {
        List<DreameProjectExperience> ret = new ArrayList<>();
        if(projectDTOList == null || projectDTOList.isEmpty()) {
            return ret;
        }
        for (int i = 0; i < projectDTOList.size(); i++) {
            TalentProjectDTO projectDTO = projectDTOList.get(i);
            DreameProjectExperience projectExperience = new DreameProjectExperience();
            projectExperience.setProjectExperienceName(projectDTO.getProjectName());
            String description = projectDTO.getDescription();
            if(StringUtils.isNotEmpty(description)) {
                projectExperience.setExperienceDescriptionList(getExperienceDescription(description, NumberingFormat.DECIMAL));
            }
            projectExperience.setProjectExperienceDate(getExperienceDateV2(projectDTO.getStartDate(), projectDTO.getEndDate(), projectDTO.getCurrent(), languageEnum,dataFormat));
            projectExperience.setProjectExperiencePosition(projectDTO.getTitle());
            ret.add(projectExperience);
        }

        return ret;
    }

    private List<DreameProjectExperience> getDreameProjectExperienceParams(List<TalentProjectDTO> projectDTOList, LanguageEnum languageEnum) {
        List<DreameProjectExperience> ret = new ArrayList<>();
        if(projectDTOList == null || projectDTOList.isEmpty()) {
            return ret;
        }
        for (int i = 0; i < projectDTOList.size(); i++) {
            TalentProjectDTO projectDTO = projectDTOList.get(i);
            DreameProjectExperience projectExperience = new DreameProjectExperience();
            projectExperience.setProjectExperienceName(projectDTO.getProjectName());
            String description = projectDTO.getDescription();
            if(StringUtils.isNotEmpty(description)) {
                projectExperience.setExperienceDescriptionList(getExperienceDescription(description, NumberingFormat.DECIMAL));
            }
            projectExperience.setProjectExperienceDate(getExperienceDate(projectDTO.getStartDate(), projectDTO.getEndDate(), projectDTO.getCurrent(), languageEnum));
            projectExperience.setProjectExperiencePosition(projectDTO.getTitle());
            ret.add(projectExperience);
        }

        return ret;
    }

    private List<CVFormatExperience> getCVFormatWorkExperienceParams(List<TalentExperienceDTO> experiences, LanguageEnum languageEnum, String dateFormat) {
        List<List<TalentExperienceDTO>> mergeExperience = mergeOverlappingExperience(experiences);
        List<CVFormatExperience> ret = new ArrayList<>();
        for(List<TalentExperienceDTO> merge : mergeExperience) {
            CVFormatExperience cvFormatExperience = new CVFormatExperience();
            TalentExperienceDTO lastExperience = merge.get(0);
            TalentExperienceDTO oldExperience = merge.get(merge.size() - 1);
            cvFormatExperience.setWorkExperienceDate(getExperienceDate(oldExperience.getStartDate(), lastExperience.getEndDate(), lastExperience.getCurrent(), languageEnum, dateFormat));
            cvFormatExperience.setWorkExperienceCompanyName(lastExperience.getCompanyName());
            cvFormatExperience.setPositionList(getCVPosition(merge, languageEnum));

            ret.add(cvFormatExperience);
        }

        return ret;
    }

    private List<CVFormatPosition> getCVPosition(List<TalentExperienceDTO> merge, LanguageEnum languageEnum) {
        List<CVFormatPosition> ret = new ArrayList<>();
        for(TalentExperienceDTO experienceDTO : merge) {
            CVFormatPosition position = new CVFormatPosition();
            position.setPositionName(experienceDTO.getTitle());
            position.setPositionDate(merge.size() == 1 ? null : getExperienceDate(experienceDTO.getStartDate(), experienceDTO.getEndDate(), experienceDTO.getCurrent(), languageEnum, "MM yyyy"));
            position.setExperienceDescription(getExperienceDescription(experienceDTO.getDescription(), new NumberingFormat(NumFormat.BULLET, "\uF06C")));

            ret.add(position);
        }

        return ret;
    }

    private List<List<TalentExperienceDTO>> mergeOverlappingExperience(List<TalentExperienceDTO> experiences) {
        if(experiences == null) {
            return new ArrayList<>();
        }
        List<List<TalentExperienceDTO>> result = new ArrayList<>();
        List<TalentExperienceDTO> currentSubList = new ArrayList<>();

        for (TalentExperienceDTO experience : experiences) {
            // 如果当前子列表为空，或者experience与子列表中的最后一个经验有交集或者能够恰好衔接上
            if (currentSubList.isEmpty() || (ObjectUtil.equal(experience.getCompanyName(), currentSubList.get(currentSubList.size() - 1).getCompanyName()) && isOverlappingOrAdjacent(currentSubList.get(currentSubList.size() - 1), experience))) {
                // 将experience添加到当前子列表中
                currentSubList.add(experience);
            } else {
                // 否则，开始一个新的子列表，并将当前子列表添加到结果中
                result.add(currentSubList);
                currentSubList = new ArrayList<>();
                currentSubList.add(experience);
            }
        }

        // 添加最后一个子列表到结果中
        if (!currentSubList.isEmpty()) {
            result.add(currentSubList);
        }

        return result;
    }

    public boolean isOverlappingOrAdjacent(TalentExperienceDTO dto1, TalentExperienceDTO dto2) {
        if(dto1.getStartDate() == null || judgeEndDateEmpty(dto1)) {
            return false;
        }
        if(dto2.getStartDate() == null || judgeEndDateEmpty(dto2)) {
            return false;
        }

        LocalDate dto1Start = dto1.getStartDate().withDayOfMonth(1);
        LocalDate dto1End = Boolean.TRUE.equals(dto1.getCurrent()) ? LocalDate.parse("9999-12-01") : dto1.getEndDate().withDayOfMonth(1);
        LocalDate dto2Start = dto2.getStartDate().withDayOfMonth(1);
        LocalDate dto2End = Boolean.TRUE.equals(dto2.getCurrent()) ? LocalDate.parse("9999-12-01") :dto2.getEndDate().withDayOfMonth(1);

        // Check for overlap
        if ((dto1Start.isBefore(dto2End) || dto1Start.equals(dto2End)) && (dto1End.isAfter(dto2Start) || dto1End.equals(dto2Start))) {
            return true;
        }

        // Check for exactly one month apart
        if (ChronoUnit.MONTHS.between(dto1End, dto2Start) == 1 || ChronoUnit.MONTHS.between(dto2End, dto1Start) == 1) {
            return true;
        }

        return false;
    }


    private boolean judgeEndDateEmpty(TalentExperienceDTO dto1) {
        if(Boolean.TRUE.equals(dto1.getCurrent())) {
            return false;
        } else {
            return dto1.getEndDate() == null;
        }
    }

    private List<GeelyWorkExperience> getGeelyWorkExperienceParams(List<TalentExperienceDTO> experiences, List<TalentProjectDTO> projects, LanguageEnum languageEnum) {
        List<WorkExperienceWithProject> workExperienceWithProjectList = matchWorkAndProject(experiences, projects);
        List<GeelyWorkExperience> ret = new ArrayList<>();
        for(WorkExperienceWithProject withProject : workExperienceWithProjectList) {
            GeelyWorkExperience geelyWorkExperience = new GeelyWorkExperience();
            TalentExperienceDTO workExperience = withProject.getWorkExperience();
            if(workExperience != null) {
                geelyWorkExperience.setWorkExperienceDate(getExperienceDate(workExperience.getStartDate(), workExperience.getEndDate(), workExperience.getCurrent(), languageEnum));
                geelyWorkExperience.setWorkExperienceTitle(workExperience.getTitle());
                geelyWorkExperience.setWorkExperienceCompanyName(workExperience.getCompanyName());
                String description = workExperience.getDescription();
                if(StringUtils.isNotEmpty(description)) {
                    geelyWorkExperience.setExperienceDescription(getExperienceDescription(description, new NumberingFormat(NumFormat.BULLET, "\uF06C")));
                } else {
                    geelyWorkExperience.setWorkExperienceNull("-");
                }
            } else {
                geelyWorkExperience.setWorkExperienceNull("-");
            }
            List<TalentProjectDTO> projectList = withProject.getProjectList();
            if(projectList != null && !projectList.isEmpty()) {
                List<GeelyProjectExperience> geelyProjectList = new ArrayList<>();
                for (int i = 0; i < projectList.size(); i++) {
                    TalentProjectDTO projectDTO = projectList.get(i);
                    GeelyProjectExperience geelyProject = new GeelyProjectExperience();
                    geelyProject.setProjectOrder(NumberChineseFormater.format(i+1, false) + "、");
                    geelyProject.setProjectExperienceDate(getExperienceDate(projectDTO.getStartDate(), projectDTO.getEndDate(), projectDTO.getCurrent(), languageEnum));
                    String description = projectDTO.getDescription();
                    if(StringUtils.isNotEmpty(description)) {
                        geelyProject.setExperienceDescription(getExperienceDescription(description, new NumberingFormat(NumFormat.BULLET, "\uF06C")));
                    }
                    geelyProject.setProjectExperienceName(projectDTO.getProjectName());
                    geelyProjectList.add(geelyProject);
                }
                geelyWorkExperience.setProjectExperience(geelyProjectList);
            } else {
                geelyWorkExperience.setProjectExperienceNull("-");
            }
            ret.add(geelyWorkExperience);
        }

        return ret;
    }

    private List<WorkExperienceWithProject> matchWorkAndProject(List<TalentExperienceDTO> experiences, List<TalentProjectDTO> projects) {
        List<WorkExperienceWithProject> ret = new ArrayList<>();
        if(experiences == null) {
            if(projects == null) {
                return ret;
            }
            WorkExperienceWithProject workExperienceWithProject = new WorkExperienceWithProject();
            workExperienceWithProject.setProjectList(projects);
            ret.add(workExperienceWithProject);
        } else {
            Set<TalentProjectDTO> matchSuccessProject = new HashSet<>();
            for(TalentExperienceDTO experienceDTO : experiences) {
                WorkExperienceWithProject workExperienceWithProject = new WorkExperienceWithProject();
                workExperienceWithProject.setWorkExperience(experienceDTO);
                if(projects == null) {
                    ret.add(workExperienceWithProject);
                } else {
                    List<TalentProjectDTO> matchProject = new ArrayList<>();
                    for(TalentProjectDTO project : projects) {
                        if(projectInWorkExperience(experienceDTO, project)) {
                            matchProject.add(project);
                            matchSuccessProject.add(project);
                        }
                    }
                    if(!matchProject.isEmpty()) {
                        workExperienceWithProject.setProjectList(matchProject);
                    }
                    ret.add(workExperienceWithProject);
                }
            }
            if(projects != null) {
                projects.removeAll(matchSuccessProject);
                WorkExperienceWithProject workExperienceWithProject = new WorkExperienceWithProject();
                TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO();
                talentExperienceDTO.setCompanyName("未匹配的项目经历");
                workExperienceWithProject.setWorkExperience(talentExperienceDTO);
                workExperienceWithProject.setProjectList(projects);
                ret.add(workExperienceWithProject);
            }
        }

        return ret;
    }

    private boolean projectInWorkExperience(TalentExperienceDTO experience, TalentProjectDTO project) {
        if (project.getStartDate() == null || experience.getStartDate() == null) {
            return false;
        }
        Boolean projectCurrent = project.getCurrent() != null && project.getCurrent();
        Boolean experienceCurrent = experience.getCurrent() != null && experience.getCurrent();
        if (projectCurrent || experienceCurrent) {
            return project.getStartDate().equals(experience.getStartDate()) || project.getStartDate().isAfter(experience.getStartDate());
        }
        if (project.getEndDate() == null || experience.getEndDate() == null) {
            return false;
        }
        return (project.getStartDate().equals(experience.getStartDate()) || project.getStartDate().isAfter(experience.getStartDate())) &&
                (project.getEndDate().equals(experience.getEndDate()) || project.getEndDate().isBefore(experience.getEndDate()));
    }

    private List<ProjectExperience> getConsultantProjectExperienceParams(List<TalentProjectDTO> projectDTOList, LanguageEnum languageEnum) {
        List<ProjectExperience> ret = new ArrayList<>();
        if(projectDTOList == null || projectDTOList.isEmpty()) {
            return ret;
        }
        for (int i = 0; i < projectDTOList.size(); i++) {
            TalentProjectDTO projectDTO = projectDTOList.get(i);
            ProjectExperience projectExperience = new ProjectExperience();
            projectExperience.setProjectExperienceName(projectDTO.getProjectName());
            String description = projectDTO.getDescription();
            if(StringUtils.isNotEmpty(description)) {
                projectExperience.setExperienceDescriptionList(getExperienceDescription(description, new NumberingFormat(NumFormat.BULLET, "\uF06C")));
            }
            projectExperience.setProjectExperienceDate(getExperienceDate(projectDTO.getStartDate(), projectDTO.getEndDate(), projectDTO.getCurrent(), languageEnum));
            projectExperience.setProjectOrder("项目" + NumberChineseFormater.format(i+1, false));
            ret.add(projectExperience);
        }

        return ret;
    }

    private void convertPdfToImage(InputStream inputStream, ByteArrayOutputStream byteArrayOutputStream) throws IOException {
        PDDocument document = PDDocument.load(inputStream);
        PDFRenderer pdfRenderer = new PDFRenderer(document);
        int numberOfPages = document.getNumberOfPages();

        // 设置 DPI（分辨率）
        int dpi = 300;

        // 计算合并图像的总高度（纵向拼接）
        int totalHeight = 0;
        int width = 0;

        // 渲染每一页并计算图像尺寸
        List<BufferedImage> pageImages = new ArrayList<>();
        for (int pageIndex = 0; pageIndex < numberOfPages; pageIndex++) {
            // 渲染每一页为 BufferedImage
            BufferedImage pageImage = pdfRenderer.renderImageWithDPI(pageIndex, dpi);
            pageImages.add(pageImage);

            // 计算总宽度和高度（假设每一页的宽度一致，计算总高度）
            if (pageIndex == 0) {
                width = pageImage.getWidth();
            }
            totalHeight += pageImage.getHeight();
        }

        // 创建一个大的空白图像，准备合并每一页
        BufferedImage combinedImage = new BufferedImage(width, totalHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = combinedImage.createGraphics();

        // 将每一页的图像绘制到合并图像中
        int currentHeight = 0;
        for (BufferedImage pageImage : pageImages) {
            g2d.drawImage(pageImage, 0, currentHeight, null);
            currentHeight += pageImage.getHeight();
        }

        // 释放图形上下文
        g2d.dispose();

        // 将合并后的图像写入 ByteArrayOutputStream
        ImageIO.write(combinedImage, "PNG", byteArrayOutputStream);
        // 关闭文档
        document.close();
    }

    protected static List<byte[]> convertPdfToPagesImages(InputStream inputStream) throws IOException {
        // 加载PDF文档
        try(PDDocument document = PDDocument.load(inputStream)) {

            PDFRenderer pdfRenderer = new PDFRenderer(document);
            int numberOfPages = document.getNumberOfPages();

            // 设置DPI（分辨率）
            int dpi = 300;

            // 存储每页图像的字节数组
            List<byte[]> pagesImageBytes = new ArrayList<>();

            // 渲染每一页并转换为字节数组
            for (int pageIndex = 0; pageIndex < numberOfPages; pageIndex++) {
                // 渲染当前页为BufferedImage
                BufferedImage pageImage = pdfRenderer.renderImageWithDPI(pageIndex, dpi);

                // 将图像转换为字节数组
                ByteArrayOutputStream bass = new ByteArrayOutputStream();
                ImageIO.write(pageImage, "PNG", bass);
                pagesImageBytes.add(bass.toByteArray());
                bass.close();
            }
            return pagesImageBytes;
        }
    }

    private List<WorkExperience> getWorkExperienceParamsByEntity(List<TalentExperienceDTO> experience, LanguageEnum languageEnum, String dateFormatter) {
        List<WorkExperience> ret = new ArrayList<>();
        if(experience == null || experience.isEmpty()) {
            return ret;
        }
        for (TalentExperienceDTO item : experience) {
            WorkExperience workExperience = new WorkExperience();
            workExperience.setWorkExperienceDate((getExperienceDate(item.getStartDate(), item.getEndDate(), item.getCurrent(), languageEnum, dateFormatter)));
            workExperience.setCompanyName(item.getCompanyName());
            workExperience.setCompanyPosition(item.getTitle());
            ret.add(workExperience);
        }

        return ret;
    }

    public void removeNullValues(Map<String, Object> map) {
        Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            if (entry.getValue() == null) {
                iterator.remove();
            }
        }
    }

    private List<SourcingConsultantWorkExperience> getSourcingConsultantWorkExperienceParams(List<TalentExperienceDTO> experienceDTOList, LanguageEnum languageEnum) {
        List<SourcingConsultantWorkExperience> workExperienceList = new ArrayList<>();
        if(experienceDTOList == null || experienceDTOList.isEmpty()) {
            return workExperienceList;
        }
        for (TalentExperienceDTO experienceDTO : experienceDTOList) {
            SourcingConsultantWorkExperience one = new SourcingConsultantWorkExperience();
            one.setWorkExperienceFormTitle(getWorkExperienceFormTitle(experienceDTO, languageEnum));
            String description = experienceDTO.getDescription();
            if(StringUtils.isNotEmpty(description)) {
                one.setExperienceDescriptionList(getExperienceDescription(description, new NumberingFormat(NumFormat.BULLET, "\uF06C")));
            }
            workExperienceList.add(one);
        }

        return workExperienceList;
    }

    private List<ExperienceDescription> getExperienceDescription(String description, NumberingFormat numberingFormat) {
        if(StringUtils.isEmpty(description)) {
            return new ArrayList<>();
        }
        List<String> experienceDescriptionList = new ArrayList<>();
        boolean existPara = false;
        String[] split = description.split(enterSplit);
        StringBuilder experienceDescriptionSB = new StringBuilder();
        for(String row : split) {
            String trim = row.trim();
            if(trim.startsWith(paraSplit)) {
                existPara = true;
                experienceDescriptionSB.append(row);
            } else {
                if(existPara) {
                    existPara = false;
                    experienceDescriptionList.add(experienceDescriptionSB.toString());
                    experienceDescriptionSB.setLength(0);
                    experienceDescriptionSB.append(row);
                    experienceDescriptionSB.append(enterSplit);
                } else {
                    experienceDescriptionSB.append(row);
                    experienceDescriptionSB.append(enterSplit);
                }
            }
        }
        if(!experienceDescriptionSB.isEmpty()) {
            experienceDescriptionList.add(experienceDescriptionSB.toString());
        }
        return getExperienceDescriptionList(experienceDescriptionList, numberingFormat);
    }

    private List<ExperienceDescription> getExperienceDescriptionList(List<String> experienceDescriptionList, NumberingFormat numberingFormat) {
        List<ExperienceDescription> ret = new ArrayList<>();
        for(String row : experienceDescriptionList) {
            int idx = row.indexOf(paraSplit);
            ExperienceDescription experienceDescription = new ExperienceDescription();
            if(idx >= 0) {
                experienceDescription.setDescText(row.substring(0, idx));
                String subParaString = row.substring(idx, row.length() - 1);
                String[] split = subParaString.split(paraSplit);
                Numberings.NumberingBuilder numberingBuilder = Numberings.of(numberingFormat);
                for(String paraRow : split) {
                    if(StringUtils.isNotEmpty(paraRow.trim())) {
                        numberingBuilder.addItem(paraRow.trim());
                    }
                }
                experienceDescription.setDescParagraph(numberingBuilder.create());
            } else {
                experienceDescription.setDescText(row);
            }

            ret.add(experienceDescription);
        }
        return ret;
    }

    private String getWorkExperienceFormTitle(TalentExperienceDTO experienceDTO, LanguageEnum languageEnum) {
        StringBuilder sb = new StringBuilder();
        String experienceDate = getExperienceDate(experienceDTO.getStartDate(), experienceDTO.getEndDate(), experienceDTO.getCurrent(), languageEnum);
        String companyName = experienceDTO.getCompanyName();
        String title = experienceDTO.getTitle();
        if(StringUtils.isNotEmpty(experienceDate)) {
            sb.append(experienceDate);
            sb.append("        ");
        }
        if(StringUtils.isNotEmpty(companyName)) {
            sb.append(companyName);
            sb.append("        ");
        }
        if(StringUtils.isNotEmpty(title)) {
            sb.append(title);
        }
        return sb.toString().trim();
    }

    private String getWorkExperienceFormTitleV2(TalentExperienceDTO experienceDTO, LanguageEnum languageEnum, String dataFormat) {
        StringBuilder sb = new StringBuilder();
        String experienceDate = getExperienceDateV2(experienceDTO.getStartDate(), experienceDTO.getEndDate(), experienceDTO.getCurrent(), languageEnum,dataFormat);
        String companyName = experienceDTO.getCompanyName();
        String title = experienceDTO.getTitle();
        if(StringUtils.isNotEmpty(experienceDate)) {
            sb.append(experienceDate);
            sb.append("        ");
        }
        if(StringUtils.isNotEmpty(companyName)) {
            sb.append(companyName);
            sb.append("        ");
        }
        if(StringUtils.isNotEmpty(title)) {
            sb.append(title);
        }
        return sb.toString().trim();
    }

    private List<EducationExperience> getEducationExperienceParamsByEntity(List<TalentEducationDTO> educations, LanguageEnum languageEnum) {
        List<EducationExperience> ret = new ArrayList<>();
        if(educations == null || educations.isEmpty()) {
            return ret;
        }
        List<EnumDegree> allEnumDegree = enumCommonService.findAllEnumDegree();
        for (TalentEducationDTO educationDTO : educations) {
            EducationExperience educationExperience = new EducationExperience();
            educationExperience.setEducationSchool(educationDTO.getCollegeName());
            educationExperience.setEducationMajor(educationDTO.getMajorName());
            String name = educationDTO.getDegreeLevel();
            Optional<EnumDegree> enumDegree = allEnumDegree.stream().filter(o -> o.getName().equalsIgnoreCase(name)).findFirst();
            educationExperience.setEducationDegree(getDegreeDisplay(enumDegree, languageEnum));
            educationExperience.setEducationDate(getExperienceDate(educationDTO.getStartDate(), educationDTO.getEndDate(), educationDTO.getCurrent(), languageEnum));
            ret.add(educationExperience);
        }

        return ret;
    }

    private List<EducationExperience> getEducationExperienceParamsByEntity(List<TalentEducationDTO> educations, LanguageEnum languageEnum, String dateFormatter) {
        List<EducationExperience> ret = new ArrayList<>();
        if(educations == null || educations.isEmpty()) {
            return ret;
        }
        List<EnumDegree> allEnumDegree = enumCommonService.findAllEnumDegree();
        for (TalentEducationDTO educationDTO : educations) {
            EducationExperience educationExperience = new EducationExperience();
            educationExperience.setEducationSchool(educationDTO.getCollegeName());
            educationExperience.setEducationMajor(educationDTO.getMajorName());
            String name = educationDTO.getDegreeLevel();
            Optional<EnumDegree> enumDegree = allEnumDegree.stream().filter(o -> o.getName().equalsIgnoreCase(name)).findFirst();
            educationExperience.setEducationDegree(getDegreeDisplay(enumDegree, languageEnum));
            educationExperience.setEducationDate(getExperienceDate(educationDTO.getStartDate(), educationDTO.getEndDate(), educationDTO.getCurrent(), languageEnum, dateFormatter));
            ret.add(educationExperience);
        }

        return ret;
    }

    private String getExperienceDate(LocalDate startDate, LocalDate endDate, Boolean current, LanguageEnum languageEnum) {
        return getExperienceDate(startDate, endDate, current, languageEnum, "yyyy.MM.dd");
    }

    private String getExperienceDateV2(LocalDate startDate, LocalDate endDate, Boolean current, LanguageEnum languageEnum,  String dateFormat) {
        return getExperienceDate(startDate, endDate, current, languageEnum, dateFormat);
    }


    private String getExperienceDate(LocalDate startDate, LocalDate endDate, Boolean current, LanguageEnum languageEnum, String dateFormatter) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormatter);
        String sStartDate = startDate != null ? startDate.format(formatter) : "";
        String sEndDate = endDate != null ? endDate.format(formatter) : "";
        sEndDate = current != null && current ? getSoFarDisplay(languageEnum) : sEndDate;
        if(StringUtils.isEmpty(sStartDate) && StringUtils.isEmpty(sEndDate)) {
            return "";
        }
        return Joiner.on(" - ").skipNulls().join(sStartDate, sEndDate);
    }

    private String getExportFileName(String fileName, JobV3 jobV3, TalentV3 talentV3) {
        List<String> placeholders = extractFileNamePlaceholders(fileName);
        if(placeholders.isEmpty()) {
            return fileName;
        }
        Map<String, String> placeholderMap = getFileNamePlaceholdersMap(placeholders, jobV3, talentV3);
        return replaceFileNamePlaceholders(fileName, placeholderMap);
    }

    private String replaceFileNamePlaceholders(String str, Map<String, String> map) {
        for (Map.Entry<String, String> entry : map.entrySet()) {
            str = str.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return str;
    }


    public static List<String> extractFileNamePlaceholders(String str) {
        List<String> placeholders = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\{(.*?)\\}");
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            placeholders.add(matcher.group(1));
        }
        return placeholders;
    }

    private Map<String, String> getFileNamePlaceholdersMap(List<String> placeholders, JobV3 jobV3, TalentV3 talentV3) {
        Map<String, String> map = new HashMap<>();
        for(String str : placeholders) {
            switch (str) {
                case ReportTemplateConstants.FILENAME_VARIABLE_FULLNAME ->
                        map.put(ReportTemplateConstants.FILENAME_VARIABLE_FULLNAME, talentV3.getFullName());
                case ReportTemplateConstants.FILENAME_VARIABLE_TITLE ->
                        map.put(ReportTemplateConstants.FILENAME_VARIABLE_TITLE, jobV3.getTitle());
                case ReportTemplateConstants.TEMPLATE_PARAM_FIRST_NAME ->
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_FIRST_NAME, talentV3.getFirstName());
                case ReportTemplateConstants.TEMPLATE_PARAM_LAST_NAME ->
                        map.put(ReportTemplateConstants.TEMPLATE_PARAM_LAST_NAME, talentV3.getLastName());
            }
        }

        return map;
    }



    private void generateFileByTemplate(HttpHeaders headers, ReportTypeEnum reportTypeEnum, JobV3 job, RecommendedReportTemplate template, TalentV3 talent, User user, GetRecommendationReportInput input, ByteArrayOutputStream outputStream) throws Exception {
        switch (reportTypeEnum) {
            case PDF:
                setDownloadHeader(headers, org.springframework.http.MediaType.APPLICATION_PDF, getExportGeneralName(reportTypeEnum));
                generatePDFReport(job, template, talent, user, input, outputStream);
                break;
            case DOC:
                setDownloadHeader(headers, org.springframework.http.MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"), getExportGeneralName(reportTypeEnum));
                generateDocReport(job, template, talent, user, input, outputStream);
                break;
        }
    }

    private void generateDocReport(JobV3 jobV3, RecommendedReportTemplate recommendedReportTemplate, TalentV3 talentV3, User user, GetRecommendationReportInput input, ByteArrayOutputStream outputStream) throws Exception {
        LanguageEnum languageEnum = LanguageEnum.fromDbValue(recommendedReportTemplate.getReportLanguage());
        String reportPath = getReportPath(languageEnum);
        try (
                InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(reportPath);
                XWPFDocument document = new XWPFDocument(inputStream);
                XWPFTemplate template = XWPFTemplate.compile(document)) {
            Map<String, Object> param = getDocParamMap(jobV3, talentV3, user, input, languageEnum);
            XWPFTemplate render = template.render(param);
            render.writeAndClose(outputStream);
        }
    }

    private Map<String, Object> getDocParamMap(JobV3 jobV3, TalentV3 talentV3, User user, GetRecommendationReportInput input, LanguageEnum languageEnum) {
        List<EnumGenderIdentity> allEnumGender = enumCommonService.findAllEnumGender();
        Map<String, Object> map = new HashMap<>();
        map.put("userName", CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
        Optional.ofNullable(user.getEmail()).ifPresent(email -> map.put("userEmail", email));
        Optional.ofNullable(user.getPhone()).ifPresent(phone -> map.put("userPhone", phone));
        map.put("createDatetime", DateUtil.today());
        Optional.ofNullable(jobV3.getTitle()).ifPresent(title -> map.put("jobTitle", title));
        Optional.ofNullable(input.getRecommendationReason()).ifPresent(reason -> map.put("recommendedReason", reason));
        Optional.ofNullable(talentV3.getFullName()).ifPresent(fullName -> map.put("talentName", fullName));
        Optional.ofNullable(talentV3.getBirthday()).ifPresent(birthday -> map.put("talentBirthday", birthday));
        Optional.ofNullable(getGender(talentV3)).ifPresent(gender -> map.put("gender", getGenderDisplay(getGenderEnumByName(gender, allEnumGender), languageEnum)));
        TalentCurrentLocation currentLocation = talentLocationRepository.findByTalentId(talentV3.getId());
        Optional.ofNullable(currentLocation).ifPresent(location -> {
            map.put("currentLocation", getCurrentLocationDisplay(currentLocation, languageEnum));
        });
        String extendedInfo = talentV3.getTalentAdditionalInfo().getExtendedInfo();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extendedInfo);
        map.put("preferredLocation", getPreferredLocations(jsonObject, languageEnum));
        Set<TalentWorkAuthorizationRelation> workAuthorization = talentV3.getWorkAuthorization();
        List<Integer> workAuthorizationIds = workAuthorization.stream().map(TalentWorkAuthorizationRelation::getEnumId).collect(Collectors.toList());
        if(!workAuthorizationIds.isEmpty()) {
            map.put("workAuthorization", getWorkAuthorizationDisplay(workAuthorizationIds, languageEnum));
        }
//        List<TalentContact> contactList = talentContactRepository.findAllByTalentIdAndStatus(talentV3.getId(), TalentContactStatus.AVAILABLE);
        List<TalentContact> contactList = talentContactRepository.findAllByTalentIdAndStatusAndIsNotWrongContact(talentV3.getId(), TalentContactStatus.AVAILABLE);
        if(!contactList.isEmpty()) {
            map.put("talentPhone", Joiner.on(", ").join(contactList.stream().filter(record -> ContactType.PHONE.equals(record.getType())).map(TalentContact::getContact).collect(Collectors.toList())));
            map.put("talentEmail", Joiner.on(", ").join(contactList.stream().filter(record -> ContactType.EMAIL.equals(record.getType())).map(TalentContact::getContact).collect(Collectors.toList())));
        }
        map.put("talentPreferredSalary", getPreferredSalary(extendedInfo, languageEnum));
        map.put("workExperience", getWorkExperienceParams(jsonObject, languageEnum));
        map.put("projectExperience", getProjectExperienceParams(jsonObject, languageEnum));
        map.put("education", getEducationExperienceParams(jsonObject, languageEnum));
        map.put("languages", getLanguageParam(talentV3, languageEnum));
        map.put("skills", getSkillsParam(jsonObject));

        return map;
    }

    private String getReportPath(LanguageEnum languageEnum) {
        return switch (languageEnum) {
            case CHINESE -> ReportTemplateConstants.CN_DOC_PATH;
            case ENGLISH -> ReportTemplateConstants.EN_DOC_PATH;
        };
    }

    public void generatePDFReport(JobV3 jobV3, RecommendedReportTemplate recommendedReportTemplate, TalentV3 talentV3, User user, GetRecommendationReportInput input, ByteArrayOutputStream outputStream) throws Exception {
        Document document = null;
        PdfWriter write = null;
        try {
            document = new Document(PageSize.A4, 50, 50, 150, 50);
            LanguageEnum languageEnum = LanguageEnum.fromDbValue(recommendedReportTemplate.getReportLanguage());
            ReportPDFFormat format = getReportFormat(languageEnum);
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            writer.setPageEvent(new HeaderFooterEventHandler(format, user));
            document.open();
            writeTalentInfoPDF(writer, document, format, jobV3, talentV3, input, languageEnum);
            writeTalentExperiencesPDF(writer, document, format, jobV3, talentV3, input, languageEnum);
        } finally {
            if(write != null) {
                write.close();
            }
            if(document != null) {
                document.close();
            }
        }
    }

    private void writeTalentExperiencesPDF(PdfWriter writer, Document document, ReportPDFFormat format, JobV3 jobV3, TalentV3 talentV3, GetRecommendationReportInput input, LanguageEnum languageEnum) throws Exception {
        String extendedInfo = talentV3.getTalentAdditionalInfo().getExtendedInfo();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extendedInfo);
        writeTalentWorkExperiences(writer, document, format, languageEnum, jsonObject);
        writeTalentProjectExperiences(writer, document, format, languageEnum, jsonObject);
        writeTalentEducations(writer, document, format, languageEnum, jsonObject);
        writeSkill(writer, document, format, languageEnum, jsonObject);
        writeLanguage(writer, document, format, languageEnum, talentV3);
    }

    private void writeLanguage(Document document, PdfWriter writer, List<EnumRelationDTO> languages, StringBuilder text) throws DocumentException {
        writeTitleLine(document, "语言能力");
        if(languages == null || languages.isEmpty()) {
            Paragraph dateParagraph = new Paragraph("无", boldFont);
            dateParagraph.setSpacingBefore(10);
            dateParagraph.setIndentationLeft(5);
            document.add(dateParagraph);
        } else {
            Map<Long, EnumLanguage> allEnumLanguagesMap = enumCommonService.findAllEnumLanguagesMap();
            List<String> languageList = new ArrayList<>();
            for (EnumRelationDTO language : languages) {
                EnumLanguage enumLanguage = allEnumLanguagesMap.get(Long.valueOf(language.getEnumId()));
                if(enumLanguage != null) {
                    languageList.add(enumLanguage.getCnDisplay());
                }
            }
            if(!languageList.isEmpty()) {
                String join = String.join(", ", languageList);
                text.append(join);
                text.append("\n");
                Paragraph dateParagraph = new Paragraph(join, normalFont);
                dateParagraph.setSpacingBefore(10);
                dateParagraph.setIndentationLeft(5);
                document.add(dateParagraph);
            }

        }
    }

    private void writeLanguage(PdfWriter writer, Document document, ReportPDFFormat format, LanguageEnum languageEnum, TalentV3 talentV3) throws DocumentException {
        writeTitleLine(document, format.language());

        String languageParam = getLanguageParam(talentV3, languageEnum);
        Paragraph jobRecommended = new Paragraph(getValueOrEmpty(languageParam), normalFont);
        jobRecommended.setSpacingBefore(20);
        document.add(jobRecommended);
    }

    private String getLanguageParam(TalentV3 talentV3, LanguageEnum languageEnum) {
        Set<TalentLanguageRelation> languages = talentV3.getLanguages();
        return getLanguageParam(languages, languageEnum);
    }

    private String getLanguageParam(Set<TalentLanguageRelation> languages, LanguageEnum languageEnum) {
        if(languages == null ) {
            return null;
        }
        List<Integer> collect = languages.stream().map(TalentLanguageRelation::getEnumId).toList();
        List<EnumLanguage> allEnumLanguages = enumCommonService.findAllEnumLanguages();
        Set<EnumLanguage> set = allEnumLanguages.stream().filter(record -> collect.contains(record.getId().intValue())).collect(Collectors.toSet());
        return set.stream().map(l -> switch (languageEnum) {
            case CHINESE -> l.getCnDisplay();
            case ENGLISH -> l.getEnDisplay();
        }).collect(Collectors.joining(", "));
    }

    private void writeSkill(PdfWriter writer, Document document, ReportPDFFormat format, LanguageEnum languageEnum, com.alibaba.fastjson.JSONObject jsonObject) throws DocumentException {
        writeTitleLine(document, format.skill());

        String skillsParam = getSkillsParam(jsonObject);
        Paragraph paragraph = new Paragraph(getValueOrEmpty(skillsParam), normalFont);
        paragraph.setSpacingBefore(20);
        document.add(paragraph);
    }

    private String getSkillsParam(com.alibaba.fastjson.JSONObject talentAdditionalInfo) {
        List<String> skillName = new ArrayList<>();
        JSONArray skillsArray = talentAdditionalInfo.getJSONArray("skills");
        if(skillsArray == null) {
            return "";
        }
        for (int i = 0; i < skillsArray.size(); i++) {
            com.alibaba.fastjson.JSONObject jsonObject = skillsArray.getJSONObject(i);
            skillName.add(jsonObject.getString("skillName"));
        }

        return String.join(", ", skillName);
    }

    private void writeTalentEducations(PdfWriter writer, Document document, ReportPDFFormat format, LanguageEnum languageEnum, com.alibaba.fastjson.JSONObject jsonObject) throws DocumentException {
        writeTitleLine(document, format.education());

        List<EducationExperience> educationExperienceParams = getEducationExperienceParams(jsonObject, languageEnum);
        for(EducationExperience educationExperience : educationExperienceParams) {
            // Create a 2x2 table
            PdfPTable table = new PdfPTable(1);
            table.setWidthPercentage(100);
            // Add the merged cell to the table
            addTableCell(educationExperience.getEducationDate(), table, boldFont);
            addTableCell(educationExperience.getEducationSchool(), table, normalFont);
            String majorAndDegree = Joiner.on(" | ").skipNulls().join(Arrays.asList(educationExperience.getEducationMajor(), educationExperience.getEducationDegree()));
            addTableCell(majorAndDegree, table, normalFont);
            table.setSpacingBefore(20);
            // Add the table to the document
            document.add(table);
        }
    }

    private List<EducationExperience> getEducationExperienceParams(com.alibaba.fastjson.JSONObject jsonObject, LanguageEnum languageEnum) {
        List<EducationExperience> ret = new ArrayList<>();
        JSONArray projectArray = jsonObject.getJSONArray("educations");
        if(projectArray == null) {
            return ret;
        }
        List<EnumDegree> allEnumDegree = enumCommonService.findAllEnumDegree();
        for (int i = 0; i < projectArray.size(); i++) {
            com.alibaba.fastjson.JSONObject one = projectArray.getJSONObject(i);
            EducationExperience educationExperience = new EducationExperience();
            educationExperience.setEducationSchool(one.getString("collegeName"));
            educationExperience.setEducationMajor(one.getString("majorName"));
            String name = one.getString("degreeLevel");
            Optional<EnumDegree> enumDegree = allEnumDegree.stream().filter(o -> o.getName().equalsIgnoreCase(name)).findFirst();
            educationExperience.setEducationDegree(getDegreeDisplay(enumDegree, languageEnum));
            educationExperience.setEducationDate(getExperienceDate(one, languageEnum));
            ret.add(educationExperience);
        }

        return ret;

    }

    private String getDegreeDisplay(Optional<EnumDegree> enumDegree, LanguageEnum languageEnum) {
        return enumDegree.map(degree -> switch (languageEnum) {
            case CHINESE -> degree.getCnDisplay();
            case ENGLISH -> degree.getEnDisplay();
        }).orElse(null);
    }

    private void writeTalentProjectExperiences(PdfWriter writer, Document document, ReportPDFFormat format, LanguageEnum languageEnum, com.alibaba.fastjson.JSONObject jsonObject) throws DocumentException {
        writeTitleLine(document, format.projectExperience());

        List<ProjectExperience> projectExperienceParams = getProjectExperienceParams(jsonObject, languageEnum);
        for(ProjectExperience projectExperience : projectExperienceParams) {
            // Create a 2x2 table
            PdfPTable table = new PdfPTable(1);
            table.setWidthPercentage(100);
            // Add the merged cell to the table
            addTableCell(projectExperience.getProjectExperienceDate(), table, boldFont);
            addTableCell(projectExperience.getProjectExperienceName(), table, normalFont);
            addTableCell(projectExperience.getProjectExperienceDesc(), table, greyNormalFont);
            table.setSpacingBefore(20);
            // Add the table to the document
            document.add(table);
        }
    }

    private void addTableCell(PdfPTable table, String label, String value) {
        PdfPCell labelCell = new PdfPCell(new Phrase(label, normalFont));
        labelCell.setBorder(Rectangle.NO_BORDER);
        labelCell.setPaddingLeft(5);
        table.addCell(labelCell);

        PdfPCell valueCell = new PdfPCell(new Phrase(value, normalFont));
        valueCell.setBorder(Rectangle.NO_BORDER);
        table.addCell(valueCell);
    }

    private void addTableCell(String str, PdfPTable table, Font font) {
        PdfPCell cell = new PdfPCell(new Phrase(str, font));
        cell.setBorderWidth(0); // Set border width to 0 to remove borders
        table.addCell(cell);
    }

    private List<ProjectExperience> getProjectExperienceParams(com.alibaba.fastjson.JSONObject jsonObject, LanguageEnum languageEnum) {
        List<ProjectExperience> ret = new ArrayList<>();
        JSONArray projectArray = jsonObject.getJSONArray("projects");
        if(projectArray == null) {
            return ret;
        }
        for (int i = 0; i < projectArray.size(); i++) {
            com.alibaba.fastjson.JSONObject one = projectArray.getJSONObject(i);
            ProjectExperience projectExperience = new ProjectExperience();
            projectExperience.setProjectExperienceName(one.getString("projectName"));
            projectExperience.setProjectExperienceDesc(one.getString("description"));
            projectExperience.setProjectExperienceDate(getExperienceDate(one, languageEnum));
            ret.add(projectExperience);
        }

        return ret;
    }


    private String getExperienceDate(com.alibaba.fastjson.JSONObject jsonObject, LanguageEnum languageEnum) {
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");
        Boolean current = jsonObject.getBoolean("current");
        startDate = StrUtil.isNotEmpty(startDate) ? DateUtil.format(DateUtil.parse(startDate, DatePattern.NORM_DATE_PATTERN), "yyyy.MM.dd") : "";
        endDate = StrUtil.isNotEmpty(endDate) ? DateUtil.format(DateUtil.parse(endDate, DatePattern.NORM_DATE_PATTERN), "yyyy.MM.dd") : "";
        endDate = current != null && current ? getSoFarDisplay(languageEnum) : endDate;
        if(StringUtils.isEmpty(startDate) && StringUtils.isEmpty(endDate)) {
            return "";
        }
        return Joiner.on(" - ").skipNulls().join(startDate, endDate);
    }

    private String getSoFarDisplay(LanguageEnum languageEnum) {
        return switch (languageEnum) {
            case ENGLISH -> ReportTemplateConstants.EN_SO_FAR_DISPLAY;
            case CHINESE -> ReportTemplateConstants.CN_SO_FAR_DISPLAY;
        };
    }

    private void writeTalentWorkExperiences(PdfWriter writer, Document document, ReportPDFFormat format, LanguageEnum languageEnum, com.alibaba.fastjson.JSONObject jsonObject) throws DocumentException {
        writeTitleLine(document, format.workExperience());

        List<WorkExperience> workExperienceParams = getWorkExperienceParams(jsonObject, languageEnum);
        for(WorkExperience workExperience : workExperienceParams) {
            // Create a 3x2 table
            PdfPTable table = new PdfPTable(1);
            table.setWidthPercentage(100);

            // Add the merged cell to the table
            addTableCell(workExperience.getWorkExperienceDate(), table, boldFont);
            addTableCell(workExperience.getCompanyName() + " | " + workExperience.getCompanyPositionAndDepartment(), table, normalFont);
            addTableCell(workExperience.getExperienceDescription(), table, greyNormalFont);
            table.setSpacingBefore(20);
            // Add the table to the document
            document.add(table);
        }
    }

    private List<WorkExperience> getWorkExperienceParams(com.alibaba.fastjson.JSONObject talentAdditionalInfo, LanguageEnum languageEnum) {
        List<WorkExperience> workExperienceList = new ArrayList<>();
        JSONArray experiencesArray = talentAdditionalInfo.getJSONArray("experiences");
        if(experiencesArray == null) {
            return workExperienceList;
        }
        for (int i = 0; i < experiencesArray.size(); i++) {
            com.alibaba.fastjson.JSONObject jsonObject = experiencesArray.getJSONObject(i);
            WorkExperience one = new WorkExperience();
            one.setCompanyName(jsonObject.getString("companyName"));
            one.setCompanyPositionAndDepartment(getPositionAndDepartment(jsonObject));
            one.setExperienceDescription(jsonObject.getString("description"));
            one.setWorkExperienceDate(getExperienceDate(jsonObject, languageEnum));
            workExperienceList.add(one);
        }

        return workExperienceList;
    }

    private String getPositionAndDepartment(com.alibaba.fastjson.JSONObject jsonObject) {
//        String department = jsonObject.getString("department");
//        String title = jsonObject.getString("title");
//        return Joiner.on(" | ").skipNulls().join(title, department);
        return jsonObject.getString("title");
    }

    private void writeTalentInfoPDF(PdfWriter writer, Document document, ReportPDFFormat format, JobV3 jobV3, TalentV3 talentV3, GetRecommendationReportInput input, LanguageEnum languageEnum) throws DocumentException {
        Paragraph jobRecommendedDesc = new Paragraph(format.jobRecommended(), boldFont);
        jobRecommendedDesc.setSpacingBefore(20);
        document.add(jobRecommendedDesc);

        Paragraph jobRecommended = new Paragraph(Optional.ofNullable(jobV3.getTitle()).orElse(""), normalFont);
        jobRecommended.setSpacingBefore(20);
        document.add(jobRecommended);

        Paragraph reasonDesc = new Paragraph(format.recommendedReason(), boldFont);
        reasonDesc.setSpacingBefore(20);
        document.add(reasonDesc);

        Paragraph reason = new Paragraph(input.getRecommendationReason(), normalFont);
        reason.setSpacingBefore(20);
        document.add(reason);

        writeTitleLine(document, format.talentInfo());
        java.util.List<EnumGenderIdentity> allEnumGender = enumCommonService.findAllEnumGender();

        // Create a 9x2 table
        PdfPTable table = new PdfPTable(2);
        table.setWidthPercentage(100);
        float[] columnWidths = {0.3f, 0.7f};
        table.setWidths(columnWidths);
        // Add content to the table cells
        setTalentInfo(table, new Phrase(format.talentName(), boldFont), new Phrase(getValueOrEmpty(talentV3.getFullName()), normalFont));
        setTalentInfo(table, new Phrase(format.talentBirthday(), boldFont), new Phrase(getValueOrEmpty(talentV3.getBirthday()), normalFont));
        String gender = getGender(talentV3);
        setTalentInfo(table, new Phrase(format.talentGender(), boldFont), new Phrase(getValueOrEmpty(getGenderDisplay(getGenderEnumByName(gender, allEnumGender), languageEnum)), normalFont));
        TalentCurrentLocation currentLocation = talentLocationRepository.findByTalentId(talentV3.getId());
        String currentLocationDisplay = getCurrentLocationDisplay(currentLocation, languageEnum);
        setTalentInfo(table, new Phrase(format.currentLocation(), boldFont), new Phrase(getValueOrEmpty(currentLocationDisplay), normalFont));
        String extendedInfo = talentV3.getTalentAdditionalInfo().getExtendedInfo();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extendedInfo);
        String preferredLocations = getPreferredLocations(jsonObject, languageEnum);
        setTalentInfo(table, new Phrase(format.preferredLocation(), boldFont), new Phrase(getValueOrEmpty(preferredLocations), normalFont));

        Set<TalentWorkAuthorizationRelation> workAuthorization = talentV3.getWorkAuthorization();
        java.util.List<Integer> workAuthorizationIds = workAuthorization.stream().map(TalentWorkAuthorizationRelation::getEnumId).collect(Collectors.toList());
        String workAuthorizationDisplay = getWorkAuthorizationDisplay(workAuthorizationIds, languageEnum);
        setTalentInfo(table, new Phrase(format.workAuthorization(), boldFont), new Phrase(getValueOrEmpty(workAuthorizationDisplay), normalFont));
        List<TalentContact> contactList = talentContactRepository.findAllByTalentIdAndStatusAndIsNotWrongContact(talentV3.getId(), TalentContactStatus.AVAILABLE);
        if(!contactList.isEmpty()) {
            String phoneJoin = Joiner.on(", ").join(contactList.stream().filter(record -> ContactType.PHONE.equals(record.getType())).map(TalentContact::getContact).collect(Collectors.toList()));
            String joinEmail = Joiner.on(", ").join(contactList.stream().filter(record -> ContactType.EMAIL.equals(record.getType())).map(TalentContact::getContact).collect(Collectors.toList()));
            setTalentInfo(table, new Phrase(format.talentPhone(), boldFont), new Phrase(getValueOrEmpty(phoneJoin), normalFont));
            setTalentInfo(table, new Phrase(format.talentEmail(), boldFont), new Phrase(getValueOrEmpty(joinEmail), normalFont));
        } else {
            setTalentInfo(table, new Phrase(format.talentPhone(), boldFont), new Phrase("", normalFont));
            setTalentInfo(table, new Phrase(format.talentEmail(), boldFont), new Phrase("", normalFont));

        }
        String preferredSalary = getPreferredSalary(extendedInfo, languageEnum);
        setTalentInfo(table, new Phrase(format.preferredSalary(), boldFont), new Phrase(getValueOrEmpty(preferredSalary), normalFont));
        table.setSpacingBefore(20);

        // Add the table to the document
        document.add(table);
    }

    private EnumGenderIdentity getGenderEnumByName(String genderName, List<EnumGenderIdentity> allEnumGender) {
        for(EnumGenderIdentity gender : allEnumGender) {
            if(gender.getName().equals(genderName)) {
                return gender;
            }
        }
        return null;
    }

    private String getPreferredSalary(String extendedInfo, LanguageEnum languageEnum) {
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extendedInfo);
        return getPreferredSalary(jsonObject.getJSONArray("preferences"), languageEnum);
    }

    private String getPreferredSalary(com.alibaba.fastjson.JSONArray preferences, LanguageEnum languageEnum) {
        if(preferences == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        int size = preferences.size();
        for(int i = 0; i < size; i++) {
            com.alibaba.fastjson.JSONObject jsonObject = preferences.getJSONObject(i);
            String preferredPayType = jsonObject.getString("payType");
            com.alibaba.fastjson.JSONObject preferredSalaryRange = jsonObject.getJSONObject("salaryRange");
            String preferredCurrency = jsonObject.getString("currency");
            EnumCurrency currency = getEnumCurrency(preferredCurrency);
            String extendedInfo = getPreferredSalaryExtendedInfo(jsonObject, size, languageEnum);
            if(preferredSalaryRange == null|| currency == null || preferredPayType == null) {
                sb.append((LanguageEnum.CHINESE.equals(languageEnum) ? CN_NO_SALARY : EN_NO_SALARY) + extendedInfo);
            } else {
                RateUnitType rateUnitType = RateUnitType.fromString(preferredPayType);
                Integer gte = preferredSalaryRange.getInteger("gte");
                Integer lte = preferredSalaryRange.getInteger("lte");
                String range = "";
                if(gte == null && lte == null) {
                    range = "";
                } else {
                    if(gte == null) {
                        range = " < " + lte;
                    } else if(lte == null) {
                        range = " > " + gte;
                    } else {
                        range = gte + " - " + lte;
                    }
                }
                sb.append(currency.getLabel2() + " " + getRateUnit(rateUnitType, languageEnum) + " " + range + extendedInfo);
            }
            sb.append("\t\n");
        }
        return sb.toString();
    }

    private final static String CN_NO_INDUSTRY = "不限行业";
    private final static String EN_NO_INDUSTRY = "All Industries";
    private final static String CN_NO_SALARY = "面议";
    private final static String EN_NO_SALARY = "Negotiable";



    private String getPreferredSalaryExtendedInfo(com.alibaba.fastjson.JSONObject jsonObject, int size, LanguageEnum languageEnum) {
        if(size <= 1) {
            return "";
        }
        List<String> mergeResult = new ArrayList<>();
        String title = jsonObject.getString("title");
        if(StringUtils.isNotEmpty(title)) {
            mergeResult.add(title);
        }
        JSONArray industries = jsonObject.getJSONArray("industries");
        String industryDesc = getIndustryDesc(industries, languageEnum);
        if(StringUtils.isNotEmpty(industryDesc)) {
            mergeResult.add(industryDesc);
        } else {
            mergeResult.add(LanguageEnum.CHINESE.equals(languageEnum) ? CN_NO_INDUSTRY : EN_NO_INDUSTRY);
        }
        String locationDesc = null;
        com.alibaba.fastjson.JSONArray locations = jsonObject.getJSONArray("locations");
        if(locations != null) {
            List<String> result = new ArrayList<>();
            for(int j = 0; j < locations.size(); j++) {
                com.alibaba.fastjson.JSONObject one = locations.getJSONObject(j);
                result.add(getLocationDescFromJSONObject(one));
            }
            locationDesc = Joiner.on(" ; ").skipNulls().join(result);
        }
        if(StringUtils.isNotEmpty(locationDesc)) {
            mergeResult.add(locationDesc);
        }
        if(mergeResult.isEmpty()) {
            return "";
        }
        return " (" + Joiner.on(" | ").skipNulls().join(mergeResult) +")";
    }

    private String getIndustryDesc(JSONArray industries, LanguageEnum languageEnum) {
        if(industries == null) {
            return null;
        }
        List<EnumIndustry> allEnumIndustry = enumCommonService.findAllEnumIndustry();
        List<String> result = new ArrayList<>();
        for(int i = 0; i < industries.size(); i++) {
            Long dbValue = industries.getLong(i);
            EnumIndustry enumIndustry = getEnumIndustry(allEnumIndustry, dbValue);
            if(enumIndustry == null) {
                continue;
            }
            if(languageEnum == LanguageEnum.CHINESE) {
                result.add(enumIndustry.getCnDisplay());
            } else {
                result.add(enumIndustry.getEnDisplay());
            }
        }
        return Joiner.on(",").skipNulls().join(result);
    }

    private EnumIndustry getEnumIndustry(List<EnumIndustry> allEnumIndustry, Long dbValue) {
        for(EnumIndustry enumIndustry : allEnumIndustry) {
            if(Objects.equals(enumIndustry.getId(), dbValue)) {
                return enumIndustry;
            }
        }
        return null;
    }

    private String getSalary(String extendedInfo, LanguageEnum languageEnum) {
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extendedInfo);
        return getSalary(jsonObject, languageEnum);
    }

    private String getSalary(com.alibaba.fastjson.JSONObject jsonObject, LanguageEnum languageEnum) {
        String preferredPayType = jsonObject.getString("payType");
        com.alibaba.fastjson.JSONObject preferredSalaryRange = jsonObject.getJSONObject("salaryRange");
        String preferredCurrency = jsonObject.getString("currency");
        EnumCurrency currency = getEnumCurrency(preferredCurrency);
        if(preferredSalaryRange == null|| currency == null || preferredPayType == null) {
            return null;
        }
        RateUnitType rateUnitType = RateUnitType.fromString(preferredPayType);
        Integer gte = preferredSalaryRange.getInteger("gte");
        Integer lte = preferredSalaryRange.getInteger("lte");
        if(gte == null || lte == null) {
            return null;
        }
        String range = gte.equals(lte) ? String.valueOf(gte) : gte + " - " + lte;
        return currency.getLabel2() + " " + getRateUnit(rateUnitType, languageEnum) + " " + range;
    }

    private String getRateUnit(RateUnitType rateUnitType, LanguageEnum languageEnum) {
        return switch (languageEnum) {
            case CHINESE -> rateUnitType.getCnDisplay();
            case ENGLISH -> rateUnitType.getEnDisplay();
        };
    }

    private EnumCurrency getEnumCurrency(String preferredCurrency) {
        List<EnumCurrency> allEnumCurrency = enumCommonService.findAllEnumCurrency();
        if(NumberUtils.isCreatable(preferredCurrency)) {
            return allEnumCurrency.stream().filter(record -> record.getId().equals(Integer.valueOf(preferredCurrency))).findFirst().orElse(null);
        } else {
            return allEnumCurrency.stream().filter(record -> record.getName().equals(preferredCurrency)).findFirst().orElse(null);
        }
    }

    private String getPreferredLocations(com.alibaba.fastjson.JSONObject additionalInfo, LanguageEnum languageEnum) {
        JSONArray preferences = additionalInfo.getJSONArray("preferences");
        if(preferences == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        int size = preferences.size();
        for(int i = 0; i < size; i++) {
            com.alibaba.fastjson.JSONObject jsonObject = preferences.getJSONObject(i);
            com.alibaba.fastjson.JSONArray locations = jsonObject.getJSONArray("locations");
            String manyLocations = getManyLocations(locations);
            if(StringUtils.isEmpty(manyLocations)) {
                continue;
            }
            String extendedInfo = getPreferredLocationExtendedInfo(jsonObject, size, languageEnum);
            sb.append(manyLocations + extendedInfo);
            sb.append("\t\n");
        }
        return sb.toString();
    }

    private String getPreferredLocationExtendedInfo(com.alibaba.fastjson.JSONObject jsonObject, int size, LanguageEnum languageEnum) {
        if(size <= 1) {
            return "";
        }
        List<String> mergeResult = new ArrayList<>();
        String title = jsonObject.getString("title");
        if(StringUtils.isNotEmpty(title)) {
            mergeResult.add(title);
        }
        JSONArray industries = jsonObject.getJSONArray("industries");
        String industryDesc = getIndustryDesc(industries, languageEnum);
        if(StringUtils.isNotEmpty(industryDesc)) {
            mergeResult.add(industryDesc);
        } else {
            mergeResult.add(LanguageEnum.CHINESE.equals(languageEnum) ? CN_NO_INDUSTRY : EN_NO_INDUSTRY);
        }
        String preferredPayType = jsonObject.getString("payType");
        com.alibaba.fastjson.JSONObject preferredSalaryRange = jsonObject.getJSONObject("salaryRange");
        String preferredCurrency = jsonObject.getString("currency");
        EnumCurrency currency = getEnumCurrency(preferredCurrency);
        StringBuilder sb = new StringBuilder();
        if(preferredSalaryRange == null|| currency == null || preferredPayType == null) {
            sb.append(LanguageEnum.CHINESE.equals(languageEnum) ? CN_NO_SALARY : EN_NO_SALARY);
        } else {
            RateUnitType rateUnitType = RateUnitType.fromString(preferredPayType);
            Integer gte = preferredSalaryRange.getInteger("gte");
            Integer lte = preferredSalaryRange.getInteger("lte");
            String range = "";
            if(gte == null && lte == null) {
                range = "";
            } else {
                if(gte == null) {
                    range = " < " + lte;
                } else if(lte == null) {
                    range = " > " + gte;
                } else {
                    range = gte + " - " + lte;
                }
            }
            sb.append(currency.getLabel2() + " " + getRateUnit(rateUnitType, languageEnum) + " " + range);
        }
        mergeResult.add(sb.toString());
        if(mergeResult.isEmpty()) {
            return "";
        }
        return " (" + Joiner.on(" | ").skipNulls().join(mergeResult) +")";
    }

    private String getManyLocations(JSONArray locations) {
        List<String> result = new ArrayList<>();
        if(locations != null) {
            for(int j = 0; j < locations.size(); j++) {
                com.alibaba.fastjson.JSONObject one = locations.getJSONObject(j);
                result.add(getLocationDescFromJSONObject(one));
            }
        }
        return result.isEmpty() ? "" : Joiner.on(" ; ").skipNulls().join(result);
    }

    private String getGender(TalentV3 talentV3) {
        TalentAdditionalInfo talentAdditionalInfo = talentV3.getTalentAdditionalInfo();
        if(talentAdditionalInfo != null) {
            String extendedInfo = talentAdditionalInfo.getExtendedInfo();
            JSONObject jsonObject = JSONUtil.parseObj(extendedInfo);
            return jsonObject.getStr("gender");
        }
        return null;
    }

    private String getCurrentLocationDisplay(TalentCurrentLocation currentLocation, LanguageEnum languageEnum) {
        if(currentLocation == null) {
            return null;
        }
        if(languageEnum == null) {
            return null;
        }
        String originalLoc = currentLocation.getOriginalLoc();
        if(originalLoc != null) {
            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(originalLoc);
            return getLocationDescFromJSONObject(jsonObject);
        }
        return null;
    }

    private String getLocationDescFromJSONObject(com.alibaba.fastjson.JSONObject jsonObject) {
        String location = jsonObject.getString("location");
        if(org.apache.commons.lang3.StringUtils.isEmpty(location)) {
            location = jsonObject.getString("originDisplay");
            if(org.apache.commons.lang3.StringUtils.isEmpty(location)) {
                String country = jsonObject.getString("country");
                String province = jsonObject.getString("province");
                String city = jsonObject.getString("city");
                if(org.apache.commons.lang3.StringUtils.isNotEmpty(city)) {
                    location = city;
                }
                if(org.apache.commons.lang3.StringUtils.isNotEmpty(province)) {
                    location = location + " - " + province;
                }
                if(StringUtils.isNotEmpty(country)) {
                    location = location + " - " + country;
                }
            }
        }
        return location;
    }

    private String getValueOrEmpty(String val) {
        return Optional.ofNullable(val).orElse("");
    }

    private void setTalentInfo(PdfPTable table, Phrase column, Phrase value) {
        PdfPCell columnCell = new PdfPCell(column);
        columnCell.setBorderWidth(0);
        table.addCell(columnCell);
        PdfPCell valueCell = new PdfPCell(value);
        valueCell.setBorderWidth(0);
        table.addCell(valueCell);
    }


    private void writeTitleLine(Document document, String title) throws DocumentException {
        Paragraph emptyBefore = new Paragraph("");
        emptyBefore.setSpacingBefore(20);
        document.add(emptyBefore);

        PdfPTable table = new PdfPTable(1);
        table.setWidthPercentage(100);
        table.getDefaultCell().setBorder(Rectangle.NO_BORDER);
        table.getDefaultCell().setBackgroundColor(new BaseColor(213, 229, 250));
        table.getDefaultCell().setFixedHeight(30);
        table.getDefaultCell().setHorizontalAlignment(Element.ALIGN_LEFT);
        table.getDefaultCell().setVerticalAlignment(Element.ALIGN_MIDDLE);
        table.addCell(new Paragraph(ReportTemplateConstants.PREFIX_EMPTY + title, boldFont));
        document.add(table);
    }


    private TalentServiceImpl.RecommendTemplate parseRecommendTemplate(String templateId) {
        TalentServiceImpl.RecommendTemplate recommendTemplate = new TalentServiceImpl.RecommendTemplate();
        String[] split = templateId.split(":");
        recommendTemplate.setRecommendTemplateType(RecommendTemplateType.fromString(split[0]));
        recommendTemplate.setId(Long.valueOf(split[1]));
        recommendTemplate.setLanguageEnum(LanguageEnum.fromDbValue(Integer.parseInt(split[2])));
        recommendTemplate.setReportTypeEnum(ReportTypeEnum.fromDbValue(Integer.parseInt(split[3])));
        return recommendTemplate;
    }

    private void generateFileByResume(HttpHeaders headers, TalentServiceImpl.RecommendTemplate recommendTemplate, JobV3 jobV3, TalentV3 talentV3, User user, GetRecommendationReportInput input, ByteArrayOutputStream outputStream) throws Exception {
        Long resumeId = recommendTemplate.getId();
        Resume resume = resumeRepository.getById(resumeId);
        ResponseEntity<CloudFileObjectMetadata> response = parserService.getFileFromS3(resume.getUuid(), "resume");
        if(response.getStatusCode() != HttpStatus.OK) {
            log.error("GetFileFromS3 error, response status code : {}.", response.getStatusCode());
            throw new ExternalInterfaceException("common service getFileFromS3 error", 500);
        }
        CloudFileObjectMetadata body = response.getBody();
        switch (recommendTemplate.getReportTypeEnum()) {
            case PDF:
                setDownloadHeader(headers, org.springframework.http.MediaType.APPLICATION_PDF, getExportGeneralName(recommendTemplate.getReportTypeEnum()));
                generatePDFReportFromResume(recommendTemplate.getLanguageEnum(), jobV3, user, input, outputStream, body);
                break;
            case DOC:
                setDownloadHeader(headers, org.springframework.http.MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"), getExportGeneralName(recommendTemplate.getReportTypeEnum()));
                generateDocReportFromResume(recommendTemplate.getLanguageEnum(), jobV3, talentV3, user, input, outputStream, body);
                break;
        }
    }

    private void generateDocReportFromResume(LanguageEnum languageEnum, JobV3 jobV3, TalentV3 talentV3, User user, GetRecommendationReportInput input, ByteArrayOutputStream outputStream, CloudFileObjectMetadata cloudFileObjectMetadata) throws Exception {
        String reportPath = getResumeReportPath(languageEnum);
        InputStream urlInputSteam = null;
        try (ByteArrayOutputStream temp = new ByteArrayOutputStream();){
            Map<String, String> param = getBaseDocParamMap(jobV3, user, input);
            com.spire.doc.Document document = new com.spire.doc.Document(this.getClass().getClassLoader().getResourceAsStream(reportPath));
            for(String key : param.keySet()) {
                document.replace(key, param.get(key), false, true);
            }

            String s3Link = cloudFileObjectMetadata.getS3Link();
            urlInputSteam = new BufferedInputStream(new URL(s3Link).openStream());
            document.insertTextFromStream(urlInputSteam, FileFormat.Docx);
            SectionCollection sections = document.getSections();
            for(int i = 1; i < sections.getCount(); i++) {
                clearSectionHeaderFooter(sections.get(i));
            }
            //保存结果文档
            document.saveToFile(temp, FileFormat.Docx_2013);
            restWord(temp, outputStream);
        } finally {
            if(urlInputSteam != null) {
                urlInputSteam.close();
            }
        }
    }

    private void restWord(ByteArrayOutputStream outputStream, ByteArrayOutputStream output) {
        byte[] byteArray = outputStream.toByteArray();
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArray);
             XWPFDocument doc = new XWPFDocument(inputStream);) {
            List<XWPFParagraph> paragraphs = doc.getParagraphs();
            if (paragraphs.size() < 1) return;
            XWPFParagraph firstParagraph = paragraphs.get(0);
            if (firstParagraph.getText().contains("Spire.Doc")) {
                doc.removeBodyElement(doc.getPosOfParagraph(firstParagraph));
            }
            doc.write(output);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void clearSectionHeaderFooter(com.spire.doc.Section newSection) {
        //获取该节的页眉，移除其中的内容，然后在其中添加一个空白段落
        com.spire.doc.HeaderFooter header1 = newSection.getHeadersFooters().getHeader();
        header1.getChildObjects().clear();
        header1.addParagraph();

        //获取该节的页脚，移除其中的内容，然后在其中添加一个空白段落
        com.spire.doc.HeaderFooter footer1 = newSection.getHeadersFooters().getFooter();
        footer1.getChildObjects().clear();
        footer1.addParagraph();

        //调整第一节中页眉的位置高度
        newSection.getPageSetup().setHeaderDistance(0);
        //调整第一节中页脚的位置高度
        newSection.getPageSetup().setFooterDistance(0);
    }

    private Map<String, String> getBaseDocParamMap(JobV3 jobV3, User user, GetRecommendationReportInput input) {
        Map<String, String> map = new HashMap<>();
        String userName = CommonUtils.formatFullName(user.getFirstName(), user.getLastName());
        map.put("{{userName}}", StringUtils.isNotEmpty(userName) ? userName : "");
        String userEmail = user.getEmail();
        map.put("{{userEmail}}", StringUtils.isNotEmpty(userEmail) ? userEmail : "");
        String userPhone = user.getPhone();
        map.put("{{userPhone}}", StringUtils.isNotEmpty(userPhone) ? userPhone : "");
        map.put("{{createDatetime}}", DateUtil.today());
        String jobTitle = jobV3.getTitle();
        map.put("{{jobTitle}}", StringUtils.isNotEmpty(jobTitle) ? jobTitle : "");
        String recommendedReason = input.getRecommendationReason();
        map.put("{{recommendedReason}}", StringUtils.isNotEmpty(recommendedReason) ? recommendedReason : "");

        return map;
    }

    private String getResumeReportPath(LanguageEnum languageEnum) {
        return switch (languageEnum) {
            case CHINESE -> ReportTemplateConstants.CN_RESUME_DOC_PATH;
            case ENGLISH -> ReportTemplateConstants.EN_RESUME_DOC_PATH;
        };
    }

    private void setDownloadHeader(HttpHeaders headers, org.springframework.http.MediaType mediaType, String filename) throws UnsupportedEncodingException {
        headers.setContentType(mediaType);
        headers.set("Content-Disposition", "attachment;filename="+ URLEncoder.encode(filename, "UTF-8"));
        headers.set("Access-Control-Expose-Headers", "Content-Disposition");
        headers.set("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.set("Pragma", "no-cache");
        headers.set("charset", "utf-8");
        headers.set("Expires", "0");
    }

    private void generatePDFReportFromResume(LanguageEnum languageEnum, JobV3 jobV3, User user, GetRecommendationReportInput input, ByteArrayOutputStream outputStream, CloudFileObjectMetadata cloudFileObjectMetadata) throws Exception {
        try (ByteArrayOutputStream template1 = getPDFMergeTemplate(languageEnum, jobV3, user, input);
             BufferedInputStream template2 = getMergedPDF(cloudFileObjectMetadata)){
            // 创建PdfReader实例来读取文件1
            PdfReader reader1 = new PdfReader(template1.toByteArray());

            // 创建PdfReader实例来读取文件2
            PdfReader reader2 = new PdfReader(template2);
            // 创建一个新的PDF文档
            Document document = new Document();
            // 创建PdfCopy实例，用于将内容复制到新的PDF文档中
            PdfCopy copy = new PdfCopy(document, outputStream);
            document.open();

            // 从两个PDF读取器中复制页面
            for (int i = 1; i <= reader1.getNumberOfPages(); i++) {
                copy.addPage(copy.getImportedPage(reader1, i));
            }
            for (int i = 1; i <= reader2.getNumberOfPages(); i++) {
                copy.addPage(copy.getImportedPage(reader2, i));
            }

            // 关闭文档和读取器
            copy.close();
            document.close();
            reader1.close();
            reader2.close();
        }
    }

    private BufferedInputStream getMergedPDF(CloudFileObjectMetadata cloudFileObjectMetadata) throws IOException {
        String s3Link = cloudFileObjectMetadata.getS3Link();
        return new BufferedInputStream(new URL(s3Link).openStream());
    }

    private ByteArrayOutputStream getPDFMergeTemplate(LanguageEnum languageEnum, JobV3 jobV3, User user, GetRecommendationReportInput input) throws DocumentException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4, 50, 50, 150, 50);
        ReportPDFFormat format = getReportFormat(languageEnum);
        PdfWriter writer = PdfWriter.getInstance(document, outputStream);
        writer.setPageEvent(new HeaderFooterEventHandler(format, user));
        document.open();

        Paragraph jobRecommendedDesc = new Paragraph(format.jobRecommended(), boldFont);
        document.add(jobRecommendedDesc);
        Paragraph jobRecommended = new Paragraph(Optional.ofNullable(jobV3.getTitle()).orElse(""), normalFont);
        jobRecommended.setSpacingBefore(20);
        document.add(jobRecommended);
        Paragraph reasonDesc = new Paragraph(format.recommendedReason(), boldFont);
        reasonDesc.setSpacingBefore(20);
        document.add(reasonDesc);
        Paragraph reason = new Paragraph(input.getRecommendationReason(), normalFont);
        reason.setSpacingBefore(20);
        document.add(reason);

        writer.close();
        document.close();
        return outputStream;
    }

    private ReportPDFFormat getReportFormat(LanguageEnum languageEnum) {
        return switch (languageEnum) {
            case CHINESE -> new CnReportPDFFormat();
            case ENGLISH -> new EnReportPDFFormat();
        };
    }

    private String getExportGeneralName(ReportTypeEnum reportTypeEnum) {
        return switch (reportTypeEnum) {
            case PDF -> "Recommendation Report.pdf";
            case DOC -> "Recommendation Report.docx";
        };
    }

    private String getGenderDisplay(EnumGenderIdentity enumGender, LanguageEnum languageEnum) {
        if(enumGender == null) {
            return null;
        }
        return switch (languageEnum) {
            case ENGLISH -> enumGender.getEnDisplay();
            case CHINESE -> enumGender.getCnDisplay();
        };
    }

    private String getWorkAuthorizationDisplay(List<Integer> workAuthorizationIds, LanguageEnum languageEnum) {
        List<EnumWorkAuthorization> workAuthorizationList = enumWorkAuthorizationRepository.findAllById(Lists.transform(workAuthorizationIds, Integer::longValue));
        return Joiner.on(", ").skipNulls().join(workAuthorizationList.stream().map(record -> {
            return switch (languageEnum) {
                case ENGLISH -> record.getEnDisplay();
                case CHINESE -> record.getCnDisplay();
            };
        }).collect(Collectors.toList()));
    }
}
