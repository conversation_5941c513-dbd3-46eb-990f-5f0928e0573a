package com.altomni.apn.talent.service.talent;

import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentVoiceMessageNote;
import com.altomni.apn.common.dto.talent.CreateTalentVoiceMessageNoteDTO;
import com.altomni.apn.common.dto.talent.TalentNoteDTO;
import com.altomni.apn.common.dto.talent.TalentReviewNoteDTO;
import com.altomni.apn.common.dto.talent.CreateTalentNoteDTO;
import com.altomni.apn.talent.web.rest.talent.dto.TopTalentNoteDTO;

import java.util.List;
import java.util.Map;

public interface TalentNoteService {
    void deleteTalentNote(Long id);

    TalentNote updateTalentNote(CreateTalentNoteDTO talentNote);

    TalentNote createTalentNote(CreateTalentNoteDTO talentNote);

    TalentVoiceMessageNote createTalentVoiceMessageNote(CreateTalentVoiceMessageNoteDTO talentNoteInput);

    TalentReviewNoteDTO createTalentReviewNote(TalentReviewNoteDTO talentReviewNoteDTO);

    TalentReviewNoteDTO updateTalentReviewNote(Long id, TalentReviewNoteDTO talentReviewNoteDTO);

    List<Map<String, Object>> getTalentNoteByType(Long talentId, List<String> type, String noteType);

    void topTalentNote(TopTalentNoteDTO dto);

    List<TalentNoteDTO> findAllByTalentId(Long talentId);

    List<TalentNoteDTO> findAllByTalentIdAndAgencyId(Long talentId, Long agencyId);

    List<TalentNoteDTO> findAllByVoipPhoneCallIds(List<String> phoneCalls);

    void saveParsedResult(String json);
    String getTalentNoteEnrich(Long talentId);
}
