package com.altomni.apn.common.utils;

import cn.hutool.core.lang.Pair;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import lombok.experimental.UtilityClass;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.annotations.CategoryTextAnnotation;
import org.jfree.chart.axis.*;
import org.jfree.chart.labels.StandardCategoryItemLabelGenerator;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.renderer.category.BarRenderer;
import org.jfree.chart.renderer.category.StandardBarPainter;
import org.jfree.chart.title.ImageTitle;
import org.jfree.chart.title.LegendTitle;
import org.jfree.chart.title.TextTitle;
import org.jfree.chart.ui.*;
import org.jfree.data.category.CategoryDataset;
import org.jfree.data.category.DefaultCategoryDataset;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.List;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.function.BiConsumer;

@UtilityClass
public class ChartUtil {

    /**
     * 创建数据集
     */
    private CategoryDataset createDataset(Map<String, LinkedList<Pair<String, Long>>> currentMap, Map<String, LinkedList<Pair<String, Long>>> lastMap) {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();
        BiConsumer<String, LinkedList<Pair<String, Long>>> consumer = (key, pairList) ->
                pairList.forEach(pair -> dataset.addValue(pair.getValue(), key, pair.getKey()));
        currentMap.forEach(consumer);
        lastMap.forEach(consumer);
        return dataset;
    }


    public byte[] getByteFromChart(Map<String, LinkedList<Pair<String, Long>>> currentMap, Map<String, LinkedList<Pair<String, Long>>> lastMap) {
        // 创建数据集
        CategoryDataset dataset = createDataset(currentMap, lastMap);
        // 创建柱状图
        JFreeChart barChart = ChartFactory.createBarChart(
                "Summary",
                null,
                null,
                dataset,
                PlotOrientation.HORIZONTAL,
                true,
                true,
                false
        );

        //阴影不可见和设置平面图
        BarRenderer renderer = new BarRenderer();
        renderer.setShadowVisible(false);
        renderer.setBarPainter(new StandardBarPainter());

        //设置显示柱状图具体数据
        renderer.setDefaultItemLabelGenerator(new StandardCategoryItemLabelGenerator());
        renderer.setDefaultItemLabelsVisible(true);
        renderer.setDefaultItemLabelFont(new Font("SansSerif", Font.BOLD, 12));
        renderer.setDefaultItemLabelPaint(Color.BLACK);
        // 将标签放置在柱子顶部
        renderer.setDefaultPositiveItemLabelPosition(new org.jfree.chart.labels.ItemLabelPosition(
                org.jfree.chart.labels.ItemLabelAnchor.OUTSIDE3, TextAnchor.CENTER_LEFT
        ));

        //一组数据类间隔
        renderer.setItemMargin(0.05);

        barChart.getCategoryPlot().setRenderer(renderer);

        // 水平网格颜色
        barChart.getCategoryPlot().setRangeGridlinePaint(new Color(227, 230, 238));
        // 设置可见
        barChart.getCategoryPlot().setRangeGridlinesVisible(true);
        // 设置格式
        barChart.getCategoryPlot().setRangeGridlineStroke(new BasicStroke());

        //去掉边框
        barChart.getCategoryPlot().setOutlineVisible(false);

        //y轴线条不显示
        ValueAxis valueAxis = barChart.getCategoryPlot().getRangeAxis();
        valueAxis.setAxisLineVisible(false);

        // 关闭自动调整范围和刻度单位选择
//        valueAxis.setAutoRange(false);  // 禁用自动范围
//        valueAxis.setAutoTickUnitSelection(false);  // 禁用自动刻度单位选择

        // 设置Y轴刻度单位为5
//        TickUnits standardUnits = new TickUnits();
//        standardUnits.add(new NumberTickUnit(5));
//        valueAxis.setStandardTickUnits(standardUnits);

        //设置背景为摆设
        barChart.getCategoryPlot().setBackgroundPaint(Color.WHITE);
        //设置维度的颜色
        barChart.getCategoryPlot().getRenderer().setSeriesPaint(0, new Color(145, 204, 116));
        barChart.getCategoryPlot().getRenderer().setSeriesPaint(1, new Color(84,112,198));

        //设置y 抽到地步
        barChart.getCategoryPlot().setRangeAxisLocation(AxisLocation.BOTTOM_OR_LEFT);

        // 设置图例
        LegendTitle legend = barChart.getLegend();
        legend.setPosition(RectangleEdge.TOP);
        legend.setItemFont(new Font("SansSerif", Font.PLAIN, 14));
        legend.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 创建自定义的标题
        TextTitle chartTitle = new TextTitle("Summary",
                new Font("SansSerif", Font.BOLD, 16));
        chartTitle.setPosition(RectangleEdge.TOP);
        chartTitle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        chartTitle.setPadding(new RectangleInsets(10, 10, 0, 0));
        barChart.setTitle(chartTitle);

        // 保存图表为图片
        int width = 800;
        int height = 800;
        // 将图表保存为二进制流
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ChartUtils.writeChartAsPNG(baos, barChart, width, height);
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomParameterizedException("create chart png is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
        }
    }

    public static byte[] getByteFromChart2(
            Map<String, LinkedList<Pair<String, Long>>> currentMap,
            Map<String, LinkedList<Pair<String, Long>>> lastMap,
            String compareUnit) {

        Map<String, Long> currentValueMap = new LinkedHashMap<>();
        Map<String, Long> lastValueMap = new LinkedHashMap<>();
        currentMap.forEach((series, list) -> {
            for (Pair<String, Long> pair : list) {
                currentValueMap.put(pair.getKey(), pair.getValue());
            }
        });
        lastMap.forEach((series, list) -> {
            for (Pair<String, Long> pair : list) {
                lastValueMap.put(pair.getKey(), pair.getValue());
            }
        });

        // 按单词边界换行，每行最多15字符
        int wrapLen = 15;
        Map<String, String> labelWrapMap = new LinkedHashMap<>();
        for (String label : currentValueMap.keySet()) {
            labelWrapMap.put(label, wrapLabel(label, wrapLen));
        }

        DefaultCategoryDataset dataset = new DefaultCategoryDataset();
        for (Map.Entry<String, Long> entry : currentValueMap.entrySet()) {
            dataset.addValue(entry.getValue(), "Current", labelWrapMap.get(entry.getKey()));
        }

        JFreeChart barChart = ChartFactory.createBarChart(
                null,
                null,
                null,
                dataset,
                PlotOrientation.VERTICAL,
                false,
                false,
                false
        );

        BarRenderer renderer = new BarRenderer();
        renderer.setBarPainter(new org.jfree.chart.renderer.category.StandardBarPainter());
        renderer.setShadowVisible(false);
        renderer.setSeriesPaint(0, new Color(66, 133, 244));
        renderer.setMaximumBarWidth(0.06); // 柱体宽度
        renderer.setDefaultItemLabelsVisible(false);

        CategoryPlot plot = barChart.getCategoryPlot();
        plot.setRenderer(renderer);
        plot.setRangeGridlinePaint(new Color(227, 230, 238));
        plot.setRangeGridlinesVisible(true);
        plot.setOutlineVisible(false);

        // x轴设置
        CategoryAxis axis = plot.getDomainAxis();
        axis.setTickLabelFont(new Font("SansSerif", Font.PLAIN, 10));
        axis.setMaximumCategoryLabelWidthRatio(1.0f);
        axis.setCategoryMargin(0.14); // 柱间距较小，标签空间更大
        axis.setMaximumCategoryLabelLines(3); // 最多3行

        plot.getRangeAxis().setAxisLineVisible(false);
        plot.getRangeAxis().setUpperMargin(0.28);

//        TextTitle chartTitle = new TextTitle("Summary", new Font("SansSerif", Font.BOLD, 16));
//        chartTitle.setPosition(RectangleEdge.TOP);
//        chartTitle.setHorizontalAlignment(HorizontalAlignment.LEFT);
//        chartTitle.setPadding(new RectangleInsets(22, 10, 0, 0));
//        barChart.setTitle(chartTitle);

        barChart.addSubtitle(buildLegendImageTitle(compareUnit));

        // === 透明背景设置 ===
        barChart.setBackgroundPaint(new Color(255,255,255,0)); // Chart整体透明
        plot.setBackgroundPaint(new Color(255,255,255,0));     // 绘图区透明

        // 柱顶current值和diff标签，右/左分开
        for (String originCategory : currentValueMap.keySet()) {
            String category = labelWrapMap.get(originCategory);
            Long currVal = currentValueMap.getOrDefault(originCategory, 0L);
            Long lastVal = lastValueMap.getOrDefault(originCategory, 0L);
            long diff = currVal - lastVal;

            String diffLabel;
            Color labelColor;
            if (lastValueMap.containsKey(originCategory)) {
                if (diff > 0) {
                    diffLabel = String.format("▲%d", diff);
                    labelColor = new Color(36, 185, 54);
                } else if (diff < 0) {
                    diffLabel = String.format("▼%d", Math.abs(diff));
                    labelColor = Color.RED;
                } else {
                    diffLabel = String.format("▼%d", 0);
                    labelColor = Color.RED;
                }
            } else {
                diffLabel = String.format("▼%d", 0);
                labelColor = Color.RED;
            }

            double barTop = currVal + Math.max(0.45, currVal * 0.08);

            // 差值：左侧
            CategoryTextAnnotation diffAnnotation = new CategoryTextAnnotation(
                    diffLabel, category, barTop);
            diffAnnotation.setFont(new Font("SansSerif", Font.BOLD, 14));
            diffAnnotation.setPaint(labelColor);
            diffAnnotation.setTextAnchor(TextAnchor.BOTTOM_LEFT);
            plot.addAnnotation(diffAnnotation);

            // current值：右侧
            CategoryTextAnnotation currAnnotation = new CategoryTextAnnotation(
                    currVal.toString(), category, barTop);
            currAnnotation.setFont(new Font("SansSerif", Font.BOLD, 15));
            currAnnotation.setPaint(Color.BLACK);
            currAnnotation.setTextAnchor(TextAnchor.BOTTOM_RIGHT);
            plot.addAnnotation(currAnnotation);
        }

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ChartUtils.writeChartAsPNG(baos, barChart, 1400, 400); // 图片宽度可根据实际调整
            return baos.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("create chart png is error, msg = " + e.getMessage());
        }
    }

    // 单词优先断行（每行最长maxLen），不截词
    private static String wrapLabel(String label, int maxLen) {
        if (label == null) return "";
        StringBuilder sb = new StringBuilder();
        int lineLen = 0;
        for (String word : label.split(" ")) {
            if (lineLen + word.length() > maxLen) {
                sb.append('\n');
                lineLen = 0;
            } else if (sb.length() > 0) {
                sb.append(' ');
                lineLen++;
            }
            sb.append(word);
            lineLen += word.length();
        }
        return sb.toString();
    }

    public static ImageTitle buildLegendImageTitle(String compareUnit) {
        // 文本内容
        String legendText = "Compared to last " + compareUnit;
        // 字体与图标
        Font font = new Font("SansSerif", Font.PLAIN, 15);
        int iconGap = 8;

        // 计算宽度（图标+间隔+文字宽度）
        BufferedImage dummyImg = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = dummyImg.createGraphics();
        g2.setFont(font);
        int textWidth = g2.getFontMetrics().stringWidth(legendText);
        int iconWidth = g2.getFontMetrics().stringWidth("▼ ▲");
        int totalWidth = iconWidth + iconGap + textWidth + 6; // 6px padding
        int height = g2.getFontMetrics().getHeight() + 6;
        g2.dispose();

        // 生成 legend 图像
        BufferedImage legendImg = new BufferedImage(totalWidth, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = legendImg.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g.setFont(font);

        int baseY = height - g.getFontMetrics().getDescent() - 3;
        int x = 3;

        // 红▼
        g.setColor(Color.RED);
        g.drawString("▼", x, baseY);
        x += g.getFontMetrics().stringWidth("▼") + 2;
        // 绿▲
        g.setColor(new Color(36,185,54));
        g.drawString("▲", x, baseY);
        x += g.getFontMetrics().stringWidth("▲") + iconGap;
        // 灰色文字
        g.setColor(Color.GRAY);
        g.drawString(legendText, x, baseY);

        g.dispose();

        // 返回 ImageTitle，左上角对齐
        return new ImageTitle(legendImg);
    }

//    /**
//     * 生成 Lark 风格卡片图片
//     * @param dataMap key: 指标名，value: 渲染文本（可包含<font color='red'>等）
//     * @param compareUnit 比较周期说明，如 "week"、"month"
//     * @return 图片字节数组
//     */
//    public static byte[] getReportCardFromMap(Map<String, String> dataMap, String compareUnit) throws Exception {
//        java.util.List<String> indicators = Arrays.asList(
//                "Active Users",
//                "Average Active Duration",
//                "No-Usage Users",
//                "Users with Low Active Time",
//                "User Active Duration",
//                "Calls",
//                "Notes",
//                "Emails"
//        );
//        int width = 800, rowHeight = 40;
//        int nRows = (int) indicators.stream().filter(dataMap::containsKey).count();
//        int headerHeight = 46, paddingTop = 20, alertHeight = dataMap.getOrDefault("alert", "").isEmpty() ? 0 : 36;
//        int height = paddingTop + headerHeight + alertHeight + rowHeight * nRows + 30;
//
//        BufferedImage img = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
//        Graphics2D g = img.createGraphics();
//        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
//
//        // 卡片圆角白底
//        g.setColor(Color.WHITE);
//        g.fillRoundRect(0, 0, width, height, 18, 18);
//
//        // Header背景
//        g.setColor(new Color(245,245,245));
//        g.fillRoundRect(12, paddingTop, width-24, headerHeight, 16, 16);
//
//        // Header表头文本
//        g.setColor(Color.BLACK);
//        g.setFont(new Font("Arial", Font.BOLD, 17));
//        g.drawString("Indicator", 40, paddingTop + 30);
//        g.drawString("Number", 310, paddingTop + 30);
//
//        // legend
////        g.setFont(new Font("Arial", Font.PLAIN, 14));
////        int x = 420, y = paddingTop + 29;
////        g.setColor(Color.BLACK); g.drawString("(", x, y); x += g.getFontMetrics().stringWidth("(");
////        g.setColor(Color.RED); g.drawString("▼", x, y); x += g.getFontMetrics().stringWidth("▼ ");
////        g.setColor(new Color(36,185,54)); g.drawString("▲", x, y); x += g.getFontMetrics().stringWidth("▲ ");
////        g.setColor(Color.GRAY); g.drawString("Compared to last " + compareUnit + ")", x, y);
//
//        g.setFont(new Font("Arial", Font.PLAIN, 14));
//        int x = 420, y = paddingTop + 29;
//        // ( 黑色
//        g.setColor(Color.BLACK);
//        g.drawString("(", x, y);
//        x += g.getFontMetrics().stringWidth("(");
//        // ▼ 红色
//        g.setColor(Color.RED);
//        g.drawString("▼", x, y);
//        x += g.getFontMetrics().stringWidth("▼ ");
//        // ▲ 绿色
//        g.setColor(new Color(36,185,54));
//        g.drawString("▲", x, y);
//        x += g.getFontMetrics().stringWidth("▲ ");
//        // "Compared to last week" 灰色
//        String legendText = "Compared to last " + compareUnit;
//        g.setColor(Color.GRAY);
//        g.drawString(legendText, x, y);
//        x += g.getFontMetrics().stringWidth(legendText);
//        // ) 黑色
//        g.setColor(Color.BLACK);
//        g.drawString(")", x, y);
//
//        // alert行
//        int yPos = paddingTop + headerHeight + 12;
//        if (!dataMap.getOrDefault("alert", "").isEmpty()) {
//            g.setColor(new Color(255, 164, 0));
//            g.setFont(new Font("Arial", Font.PLAIN, 15));
//            g.drawString(dataMap.get("alert"), 36, yPos + 3);
//            yPos += alertHeight;
//        }
//
//        // 正文
//        int colName = 40, colValue = 310;
//        g.setFont(new Font("Arial", Font.PLAIN, 17));
//        for (String key : indicators) {
//            if (!dataMap.containsKey(key)) continue;
//
//            // 内容
//            g.setColor(Color.BLACK);
//            g.setFont(new Font("Arial", Font.PLAIN, 17));
//            g.drawString(key, colName, yPos + 27);
//
//            // value列支持多色
//            java.util.List<RichText> valueParts = parseRichText(dataMap.get(key));
//            int valX = colValue, valY = yPos + 27;
//            for (RichText part : valueParts) {
//                g.setColor(part.color);
//                g.setFont(new Font("Arial", Font.PLAIN, 17));
//                g.drawString(part.text, valX, valY);
//                valX += g.getFontMetrics().stringWidth(part.text);
//            }
//
//            // 分割线
//            g.setColor(new Color(238, 238, 238)); // #EEEEEE
//            g.setStroke(new BasicStroke(1));
//            g.drawLine(32, yPos + rowHeight, width - 32, yPos + rowHeight);
//
//            yPos += rowHeight;
//        }
//        g.dispose();
//        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//        ImageIO.write(img, "png", baos);
//        return baos.toByteArray();
//    }
    /**
     * 生成 Lark 风格卡片图片
     * @param dataMap key: 指标名，value: 渲染文本（可包含<font color='red'>等）
     * @param compareUnit 比较周期说明，如 "week"、"month"
     * @return 图片字节数组
     */
    public static byte[] getReportCardFromMap(Map<String, String> dataMap, String compareUnit, boolean isTeamReport) throws Exception {
        java.util.List<String> indicators = Arrays.asList(
                "Active Users",
                "Average Active Duration",
                "No-Usage Users",
                "Users with Low Active Time",
                "Calls",
                "Notes",
                "Emails"
        );
        if (!isTeamReport) {
            indicators = Arrays.asList(
                    "User Active Duration",
                    "Calls",
                    "Notes",
                    "Emails"
            );
        }

        int width = 800, rowHeight = 40;
        int nRows = (int) indicators.stream().filter(dataMap::containsKey).count();
        int headerHeight = 46, paddingTop = 20;

        // === alert渲染相关 ===
        String alertText = dataMap.getOrDefault("alert", "");
        boolean hasAlert = alertText != null && !alertText.trim().isEmpty();
        int alertFontSize = 17;
        int alertLineHeight = 32;
        int alertSpacing = hasAlert ? 10 : 0;

        // 预先创建 Graphics2D 获取宽度（还没画图，可以临时建个空图片）
        BufferedImage tmpImg = new BufferedImage(10, 10, BufferedImage.TYPE_INT_ARGB);
        Graphics2D tmpG = tmpImg.createGraphics();
        tmpG.setFont(new Font("SansSerif", Font.PLAIN, alertFontSize));
        int alertMaxWidth = width - 72; // 36 左右边距
        java.util.List<String> alertLines = splitTextToLines(tmpG, alertText, alertMaxWidth);
        tmpG.dispose();

        int alertHeight = hasAlert ? (alertLineHeight * alertLines.size()) + alertSpacing : 0;
        int height = paddingTop + alertHeight + headerHeight + rowHeight * nRows + 30;

        BufferedImage img = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = img.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 卡片圆角白底
        g.setColor(Color.WHITE);
        g.fillRoundRect(0, 0, width, height, 18, 18);

        // === ALERT顶部红字自动换行 ===
        if (hasAlert) {
            g.setColor(new Color(255, 193, 7)); // 明亮橙黄
            g.setFont(new Font("SansSerif", Font.PLAIN, alertFontSize));
            int alertY = paddingTop + alertFontSize + 2;
            for (String line : alertLines) {
                g.drawString(line, 36, alertY);
                alertY += alertLineHeight;
            }
        }

        // Header背景
        int headerY = paddingTop + alertHeight;
        g.setColor(new Color(245,245,245));
        g.fillRoundRect(12, headerY, width-24, headerHeight, 16, 16);

        // Header表头文本
        g.setColor(Color.BLACK);
        g.setFont(new Font("SansSerif", Font.BOLD, 16));
        g.drawString("Indicator", 40, headerY + 30);
        g.drawString("Number", 310, headerY + 30);

        // legend部分
        g.setFont(new Font("SansSerif", Font.PLAIN, 14));
        int x = 420, y = headerY + 29;
        g.setColor(Color.BLACK); g.drawString("(", x, y); x += g.getFontMetrics().stringWidth("(");
        g.setColor(Color.RED); g.drawString("▼", x, y); x += g.getFontMetrics().stringWidth("▼ ");
        g.setColor(new Color(36,185,54)); g.drawString("▲", x, y); x += g.getFontMetrics().stringWidth("▲ ");
        g.setColor(Color.GRAY); g.drawString("Compared to last " + compareUnit, x, y); x += g.getFontMetrics().stringWidth("Compared to last " + compareUnit);
        g.setColor(Color.BLACK); g.drawString(")", x, y);

        // 正文内容
        int yPos = headerY + headerHeight + 12;
        int colName = 40, colValue = 310;
        g.setFont(new Font("SansSerif", Font.PLAIN, 15));
        for (String key : indicators) {
            if (!dataMap.containsKey(key)) continue;

            // 指标名
            g.setColor(Color.BLACK);
            g.setFont(new Font("SansSerif", Font.PLAIN, 15));
            g.drawString(key, colName, yPos + 27);

            // value列支持多色
            java.util.List<RichText> valueParts = parseRichText(dataMap.get(key));
            int valX = colValue, valY = yPos + 27;
            for (RichText part : valueParts) {
                g.setColor(part.color);
                g.setFont(new Font("SansSerif", Font.PLAIN, 15));
                g.drawString(part.text, valX, valY);
                valX += g.getFontMetrics().stringWidth(part.text);
            }

            // 分割线
            g.setColor(new Color(238, 238, 238)); // #EEEEEE
            g.setStroke(new BasicStroke(1));
            g.drawLine(32, yPos + rowHeight, width - 32, yPos + rowHeight);

            yPos += rowHeight;
        }
        g.dispose();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(img, "png", baos);
        return baos.toByteArray();
    }

    private static class RichText {
        String text;
        Color color;
        RichText(String text, Color color) {
            this.text = text;
            this.color = color;
        }
    }

    private static java.util.List<RichText> parseRichText(String value) {
        java.util.List<RichText> parts = new ArrayList<>();
        int idx = 0;
        while (idx < value.length()) {
            int fontStart = value.indexOf("<font", idx);
            if (fontStart < 0) {
                parts.add(new RichText(value.substring(idx), Color.BLACK));
                break;
            }
            if (fontStart > idx) {
                parts.add(new RichText(value.substring(idx, fontStart), Color.BLACK));
            }
            int colorIdx = value.indexOf("color='", fontStart) + 7;
            int colorEnd = value.indexOf("'", colorIdx);
            String colorStr = value.substring(colorIdx, colorEnd);
            Color color = "red".equals(colorStr) ? Color.RED : "green".equals(colorStr) ? new Color(36,185,54) : Color.BLACK;
            int tagEnd = value.indexOf(">", colorEnd) + 1;
            int fontClose = value.indexOf("</font>", tagEnd);
            String text = value.substring(tagEnd, fontClose);
            parts.add(new RichText(text, color));
            idx = fontClose + 7;
        }
        return parts;
    }

    // 按最大像素宽度自动换行（避免截词）
    private static java.util.List<String> splitTextToLines(Graphics2D g, String text, int maxWidth) {
        java.util.List<String> lines = new java.util.ArrayList<>();
        if (text == null || text.isEmpty()) return lines;
        String[] words = text.split(" ");
        StringBuilder line = new StringBuilder();
        for (String word : words) {
            String test = line.length() == 0 ? word : line + " " + word;
            if (g.getFontMetrics().stringWidth(test) > maxWidth) {
                if (line.length() > 0) lines.add(line.toString());
                line = new StringBuilder(word);
            } else {
                if (line.length() > 0) line.append(" ");
                line.append(word);
            }
        }
        if (line.length() > 0) lines.add(line.toString());
        return lines;
    }

}
