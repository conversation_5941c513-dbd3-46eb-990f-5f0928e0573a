package com.altomni.apn.common.web.rest.crmenums;

import com.altomni.apn.common.aop.cache.CacheControl;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.crmenums.*;
import com.altomni.apn.common.vo.crmenums.DictVO;
import com.altomni.apn.common.vo.crmenums.EnumDictVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/v1/dict")
public class CrmEnumDictResource {

    @Resource
    private EnumBusinessProgressService enumBusinessProgressService;

    @Resource
    private EnumCompanyClientLevelService enumCompanyClientLevelService;

    @Resource
    private EnumCompanyContactCategoryService enumCompanyContactCategoryService;

    @Resource
    private EnumContactTypeService enumContactTypeService;

    @Resource
    private EnumFollowUpContactTypeService enumFollowUpContactTypeService;


    @GetMapping("/business-progress")
    @CacheControl
    public ResponseEntity<List<EnumDictVO>> findAllBusinessProgress(@RequestParam(value = "type") SortType type) {
        log.info("findAllBusinessProgress");
        return ResponseEntity.ok(enumBusinessProgressService.findAllBusinessProgress(type));
    }

    @GetMapping("/client-level")
    @CacheControl
    public ResponseEntity<List<EnumDictVO>> findAllCompanyClientLevel(@RequestParam(value = "type") SortType type) {
        return ResponseEntity.ok(enumCompanyClientLevelService.findAllCompanyClientLevel(type));
    }

    @GetMapping("/contact-category")
    @CacheControl
    public ResponseEntity<List<EnumDictVO>> findAllCompanyContactCategory(@RequestParam(value = "type") SortType type) {
        return ResponseEntity.ok(enumCompanyContactCategoryService.findAllCompanyContactCategory(type));
    }

    @GetMapping("/contact-type")
    @CacheControl
    public ResponseEntity<List<DictVO>> findAllContactType(@RequestParam(value = "type") SortType type) {
        return ResponseEntity.ok(enumContactTypeService.findAllOrderBySortType(type));
    }

    @GetMapping("/follow-up-contact-type")
    @CacheControl
    public ResponseEntity<List<DictVO>> findAllFollowUpContactType(@RequestParam(value = "type") SortType type) {
        return ResponseEntity.ok(enumFollowUpContactTypeService.findAllOrderBySortType(type));
    }

}
