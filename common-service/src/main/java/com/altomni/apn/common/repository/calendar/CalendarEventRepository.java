package com.altomni.apn.common.repository.calendar;

import com.altomni.apn.common.domain.calendar.CalendarEvent;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Repository
public interface CalendarEventRepository extends JpaRepository<CalendarEvent, Long> {

    @Modifying
    @Transactional
    @Query(value = " delete from calendar_event where id = ?1 ", nativeQuery = true)
    void deleteById(Long id);

    CalendarEvent findCalendarEventByTypeIdAndReferenceId(Integer typeId, Long referenceId);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event set lark_calendar_id = ?2, lark_event_id = ?3  where id = ?1 ", nativeQuery = true)
    void updateLarkCalendarIdAndEventIdById(Long id, String larkCalendarId, String larkEventId);

    List<CalendarEvent> findAllByIdIn(List<Long> idList);

    @Query(value = " select * from calendar_event where lark_calendar_id is null and puser_id = ?1 and start_time > now() ", nativeQuery = true)
    List<CalendarEvent> findCalendarEventListByCreatedAndStartDate(Long userId);

    List<CalendarEvent> findAllByReferenceIdIn(List<Long> referenceIdList);

    @Query("SELECT DISTINCT ce FROM CalendarEvent ce " +
            "JOIN CalendarEventAttendee cea ON ce.id = cea.eventId " +
            "WHERE cea.userId IN :userIds " +
            "AND ce.typeId = 20 " +  // 系统日程
            "AND (cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.TO_BE_COMPLETED " +
            "     OR cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.OVERDUE)")
    List<CalendarEvent> findUncompletedAndOverdueSystemCalendarsByUserIds(@Param("userIds") List<Long> userIds);

    @Query("SELECT distinct ce FROM CalendarEvent ce " +
            "JOIN CalendarEventAttendee cea ON ce.id = cea.eventId " +
            "WHERE ce.typeId = 20 " +
            "AND ce.calendarType = :calendarType " +
            "AND (cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.TO_BE_COMPLETED " +
            "     OR cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.OVERDUE)")
    List<CalendarEvent> findUncompletedAndOverdueByCalendarType(@Param("calendarType") CalendarTypeEnum calendarType);


    @Query("SELECT distinct ce FROM CalendarEvent ce " +
            "JOIN CalendarEventAttendee cea ON ce.id = cea.eventId " +
            "WHERE ce.typeId = 20 " +
            "AND ce.calendarType = :calendarType " +
            "AND ce.referenceId = :referenceId " +
            "AND (cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.TO_BE_COMPLETED " +
            "     OR cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.OVERDUE)")
    List<CalendarEvent> findUncompletedAndOverdueByCalendarTypeAndReferenceId(@Param("calendarType") CalendarTypeEnum calendarType, @Param("referenceId") Long referenceId);

    @Query("SELECT MAX(ce.lastModifiedDate) FROM CalendarEvent ce " +
            "JOIN CalendarEventAttendee cea ON ce.id = cea.eventId " +
            "WHERE cea.userId = :userId " +
            "AND ce.typeId = 20")  // 系统日程
    Instant findMaxLastModifiedDateForAllUserSystemCalendars(@Param("userId") Long userId);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event set last_modified_date = now()  where id in (?1)  ", nativeQuery = true)
    void updateLastModifiedDate(List<Long> eventId);


    @Query(value = "select distinct j.id from job j " +
            "inner join job_project jp on jp.id=j.pteam_id " +
            "where j.id in :jobIds", nativeQuery = true)
    List<Long> findPrivateJobByIds(@Param("jobIds")List<Long> jobIds);
}