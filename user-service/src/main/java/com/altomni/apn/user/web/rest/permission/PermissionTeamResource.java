package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.aop.datasync.DataSyncAspect;
import com.altomni.apn.common.aop.datasync.annotation.DataSyncAnnotation;
import com.altomni.apn.common.config.PrivilegeName;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.service.dto.permission.*;
import com.altomni.apn.user.service.permission.PermissionTeamService;
import com.altomni.apn.user.service.permission.PermissionUserTeamService;
import com.altomni.apn.user.service.user.UserService;
import com.altomni.apn.user.web.rest.vm.permission.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/teams")
public class PermissionTeamResource {

    @Resource
    private PermissionTeamService permissionTeamService;

    @Resource
    private UserService userService;

    @Resource
    private PermissionUserTeamService permissionUserTeamService;

    @PostMapping("")
    public ResponseEntity<PermissionTeamDTO> createTeam(@RequestBody PermissionTeamCreateVM permissionTeamCreateVM){
        log.info("({}) REST request to create team {}", permissionTeamCreateVM);
        return ResponseEntity.ok(permissionTeamService.create(permissionTeamCreateVM));
    }

    @PutMapping("")
    public ResponseEntity<PermissionTeamDTO> updateTeam(@RequestBody PermissionTeamUpdateVM permissionTeamUpdateVM){
        log.info("({}) REST request to create team {}", permissionTeamUpdateVM);
        return ResponseEntity.ok(permissionTeamService.update(permissionTeamUpdateVM));
    }

    @GetMapping("/{teamId}/count/active-users")
    public ResponseEntity<Integer> countActiveUsersByTeam(@PathVariable("teamId") Long teamId){
        log.info("({}) REST request to count active user by team {}", SecurityUtils.getCurrentUserLogin(), teamId);
        return ResponseEntity.ok(permissionTeamService.countActiveUsersByTeamId(teamId));
    }

    @DeleteMapping("/{deleteTeamId}/{changeTeamId}")
    public ResponseEntity<Integer> deleteAndChangeTeam(@PathVariable("deleteTeamId") Long deleteTeamId, @PathVariable("changeTeamId") Long changeTeamId){
        log.info("({}) REST request to delete team {}, change team {}", SecurityUtils.getCurrentUserLogin(), deleteTeamId, changeTeamId);
        return ResponseEntity.ok(permissionTeamService.deleteAndChangeTeam(deleteTeamId, changeTeamId));
    }

    @GetMapping("/search-job-and-user-by-teamId/{teamId}")
    public ResponseEntity<PermissionTeamJobAndUserCountVM> searchJobAndUserByTeamId(@PathVariable("teamId") Long teamId){
        log.info("({}) search job and user by teamId {}", SecurityUtils.getCurrentUserLogin(), teamId);
        return ResponseEntity.ok(permissionUserTeamService.searchJobAndUserByTeamId(teamId));
    }

    @PrivilegeName(value = "Get Teams")
    @GetMapping("/tree")
    public ResponseEntity<List<PermissionTeamTreeDTO>> getTeamsWithLeaders() throws ExecutionException, InterruptedException {
        log.info("({}) REST request to get team tree with leaders", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionTeamService.getTeamTreeWithLeaders());
    }

    @GetMapping("/plain")
    public ResponseEntity<Map<Long, String>> getPlainTeamsWithNameAndId() throws ExecutionException, InterruptedException {
        log.info("({}) REST request to get team name and id ", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionTeamService.getPlainTeamsWithNameAndId());
    }

    @PrivilegeName(value = "Get Teams")
    @GetMapping("/self-sub-tree")
    public ResponseEntity<List<PermissionTeamTreeDTO>> getSelfSubTeamsWithLeaders() throws ExecutionException, InterruptedException {
        log.info("({}) REST request to get self and sub team tree with leaders", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionTeamService.getSelfSubTeamsWithLeaders());
    }

    @PrivilegeName(value = "Get Teams with data permission ")
    @GetMapping("/tree/plain")
    public ResponseEntity<List<PermissionTeamTreeDTO>> getPlainTeams() throws ExecutionException, InterruptedException {
        log.info("({}) REST request to get team tree with permission", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionTeamService.getPlainTeamTree());
    }

    @PrivilegeName(value = "Get Teams with data permission")
    @GetMapping("/tree/with-permission")
    public ResponseEntity<List<PermissionTeamTreeDTO>> getTeamsWithPermission() throws ExecutionException, InterruptedException {
        log.info("({}) REST request to get team tree with permission", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionTeamService.getTeamTreeWithPermissionByType(Module.JOB));
    }

    @PrivilegeName(value = "Get Teams with data permission by type")
    @GetMapping("/tree/with-permission/{module}")
    public ResponseEntity<List<PermissionTeamTreeDTO>> getTeamsWithPermissionByType(@PathVariable("module") Module module) throws ExecutionException, InterruptedException {
        log.info("({}) REST request to get team tree with permission", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionTeamService.getTeamTreeWithPermissionByType(module));
    }

    @PrivilegeName(value = "Get Teams with data permission and user data by type")
    @GetMapping("/tree/with-permission-and-user/{module}/{isPrimary}")
    public ResponseEntity<List<PermissionTeamTreeDTO>> getTeamsWithPermissionAndUserByType(@PathVariable("module") Module module, @PathVariable("isPrimary") Boolean isPrimary) throws ExecutionException, InterruptedException {
        log.info("({}) REST request to get team tree with permission and user ", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionTeamService.getTeamTreeWithPermissionUserByType(module, isPrimary));
    }

    @GetMapping("/{teamId}/users")
    @Deprecated
    public ResponseEntity<List<PermissionTeamMemberDTO>> getTeamMembers(@PathVariable("teamId") Long teamId){
        log.info("REST request to get users by teamId: {}", teamId);
        List<PermissionTeamMemberDTO> users = userService.findUsersByPermissionTeamId(teamId);
        return ResponseEntity.ok(users);
    }

    /**
     * 公司团队列表搜索
     * @param searchVM
     * @return
     */
    @PostMapping("/users-search")
    public ResponseEntity<List<PermissionTeamMemberWithDeliveryLabelDTO>> searchTeamMembers(@RequestBody PermissionTeamDeliverySearchVM searchVM){
        log.info("REST request to get users by teamId: {}, param: {}", searchVM.getTeamId(), searchVM);
        List<PermissionTeamMemberWithDeliveryLabelDTO> users = userService.findUsersWithDeliveryLabelByPermissionTeamId(searchVM);
        return ResponseEntity.ok(users);
    }

    @GetMapping("/{teamId}/users/plain")
    public ResponseEntity<List<PermissionTeamMemberDTO>> getPlainTeamMembers(@PathVariable("teamId") Long teamId){
        log.info("REST request to get plain users by teamId: {}", teamId);
        List<PermissionTeamMemberDTO> users = userService.findPlainUsersByPermissionTeamId(teamId);
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/plain")
    public ResponseEntity<List<PermissionTeamMemberDTO>> getPlainTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM){
        log.info("REST request to get plain users by teamIds: {}", teamMemberSearchVM);
        List<PermissionTeamMemberDTO> users = userService.findPlainUsersByPermissionTeamIdIn(teamMemberSearchVM.getTeamIds());
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/team-users")
    public ResponseEntity<List<PermissionTeamUserDTO>> getTeamUsersByPermissionTeamIdIn(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM){
        log.info("REST request to get plain users by teamIds: {}", teamMemberSearchVM);
        List<PermissionTeamUserDTO> users = userService.findTeamUsersByPermissionTeamIdIn(teamMemberSearchVM.getTeamIds());
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/all-status-team-user-ids")
    public ResponseEntity<Set<Long>> getAllTeamUserIdsByPermissionTeamIdIn(@RequestBody Set<Long> teamIds){
        log.info("REST request to get all user ids by teamIds: {}", teamIds);
        Set<Long> users = userService.findTeamUserIdsByPermissionTeamIdIn(teamIds);
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/active-team-user-ids")
    public ResponseEntity<Set<Long>> getAllActiveTeamUserIdsByPermissionTeamIdIn(@RequestBody Set<Long> teamIds){
        log.info("REST request to get all active user ids by teamIds: {}", teamIds);
        Set<Long> users = userService.findActiveTeamUserIdsByPermissionTeamIdIn(teamIds);
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/plain/with-team")
    public ResponseEntity<List<PermissionTeamMemberDTO>> getTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM) {
        log.info("REST request to get users by teamIds: {}", teamMemberSearchVM);
        List<PermissionTeamMemberDTO> users = userService.findUsersByPermissionTeamIdIn(teamMemberSearchVM.getTeamIds());
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/plain/with-team/active")
    public ResponseEntity<List<PermissionTeamUserDTO>> getActiveTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM) {
        log.info("REST request to get active users by teamIds: {}", teamMemberSearchVM);
        List<PermissionTeamUserDTO> users = userService.findActiveUsersByPermissionTeamIdIn(teamMemberSearchVM.getTeamIds());
        return ResponseEntity.ok(users);
    }

    @GetMapping("/{teamId}/non-contains/users")
    public ResponseEntity<List<UserBriefDTO>> getUsersNotInTeam(@PathVariable("teamId") Long teamId){
        log.info("REST request to get users by teamId: {}", teamId);
        return ResponseEntity.ok(userService.findUsersNotInTeam(teamId));
    }

    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @PostMapping("/{teamId}/users")
    public ResponseEntity<Void> addUserToTeam(@PathVariable("teamId") Long teamId, @RequestBody PermissionTeamMembersVM permissionTeamMembersVM){
        log.info("({}) REST request to add user to team", SecurityUtils.getCurrentUserLogin());
        permissionUserTeamService.addUsersToTeam(permissionTeamMembersVM.getUserIds(), teamId);
        MDC.put(DataSyncAspect.SYNC_DATA_ID, permissionTeamMembersVM.getUserIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        return ResponseEntity.ok().build();
    }

    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @DeleteMapping("/{teamId}/users")
    public ResponseEntity<Void> removeUserFromTeam(@PathVariable("teamId") Long teamId, @RequestBody PermissionTeamMembersVM permissionTeamMembersVM){
        log.info("({}) REST request to remove user from team", SecurityUtils.getCurrentUserLogin());
        permissionUserTeamService.removeUsersFromTeam(permissionTeamMembersVM.getUserIds(), teamId);
        MDC.put(DataSyncAspect.SYNC_DATA_ID, permissionTeamMembersVM.getUserIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        return ResponseEntity.ok().build();
    }

    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @PutMapping("/users/transfer/primary")
    public ResponseEntity<Void> primaryTeamUsersTransfer(@RequestBody PermissionTeamUsersTransferPrimaryVM transferVM){
        log.info("({}) REST request to transfer user between primary teams {}", SecurityUtils.getCurrentUserLogin(), transferVM);
        permissionUserTeamService.primaryTeamUsersTransfer(transferVM);
        MDC.put(DataSyncAspect.SYNC_DATA_ID, String.valueOf(transferVM.getUserId()));
        return ResponseEntity.ok().build();
    }

    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @PutMapping("/users/transfer/secondary")
    public ResponseEntity<Void> secondaryTeamUsersTransfer(@RequestBody PermissionTeamUsersTransferSecondaryVM transferVM){
        log.info("({}) REST request to transfer user between secondary teams{}", SecurityUtils.getCurrentUserLogin(), transferVM);
        permissionUserTeamService.secondaryTeamUsersTransfer(transferVM);
        MDC.put(DataSyncAspect.SYNC_DATA_ID, transferVM.getUserIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{teamId}/leader")
    public ResponseEntity<Void> updateTeamLeader(@PathVariable("teamId") Long teamId, @RequestBody PermissionTeamLeaderVM permissionTeamLeaderVM){
        log.info("({}) REST request to update team leader, team {}, leader {}", SecurityUtils.getCurrentUserLogin(), teamId, permissionTeamLeaderVM.getLeaderUserId());
        permissionTeamService.addTeamLeader(teamId, permissionTeamLeaderVM.getLeaderUserId());
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{teamId}/leader/{leaderId}")
    public ResponseEntity<Void> deleteTeamLeader(@PathVariable("teamId") Long teamId, @PathVariable("leaderId") Long leaderId){
        log.info("({}) REST request to delete team leader, team {}, leader {}", SecurityUtils.getCurrentUserLogin(), teamId, leaderId);
        permissionTeamService.deleteTeamLeader(teamId, leaderId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/user/{userId}/primary_team_id")
    public ResponseEntity<Long> getPrimaryTeamIdByUserId(@PathVariable("userId") Long userId){
        log.info("({}) REST request to get primary team id by user id {}", SecurityUtils.getCurrentUserLogin(), userId);

        return ResponseEntity.ok(permissionUserTeamService.findPrimaryTeamByUserId(userId).getTeamId());
    }

    @GetMapping("/user/{userId}/upper/team-leaders")
    public ResponseEntity<Set<Long>> getUpperTeamLeadersByUserId(@PathVariable("userId") Long userId) {
        log.info("({}) REST request to get upper team leaders by user id {}", SecurityUtils.getCurrentUserLogin(), userId);
        return ResponseEntity.ok(permissionUserTeamService.getAllUpperTeamLeaders(userId));
    }

    @GetMapping("/level1-team/user/{userId}")
    public ResponseEntity<Long> getLevel1TeamIdByUserId(@PathVariable("userId") Long userId) {
        log.info("({}) REST request to get level 1 team id by user id {}", SecurityUtils.getCurrentUserLogin(), userId);
        return ResponseEntity.ok(permissionUserTeamService.getLevel1TeamIdByUserId(userId));
    }

    @GetMapping("/level1-team-profit-leader/user/{userId}")
    public ResponseEntity<Long> getLevel1TeamProfitLeaderByUserId(@PathVariable("userId") Long userId) {
        log.info("({}) REST request to get level 1 team profit leader id by user id {}", SecurityUtils.getCurrentUserLogin(), userId);
        return ResponseEntity.ok(permissionUserTeamService.getLevel1TeamProfitLeaderByUserId(userId));
    }

    @PostMapping("/all-child-teams")
    public ResponseEntity<List<PermissionTeam>> getAllChildTeams(@RequestBody List<Long> parentTeamIds) {
        log.info("({}) REST request to get all child teams by parent team id {}", SecurityUtils.getCurrentUserLogin(), parentTeamIds);
        return ResponseEntity.ok(permissionUserTeamService.getAllChildTeamsByParentTeamId(parentTeamIds));
    }
}
