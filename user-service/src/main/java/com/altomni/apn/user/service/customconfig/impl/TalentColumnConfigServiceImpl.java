package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.bean.BeanUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.application.pipeline.CreationTypeDTO;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.customconfig.TalentColumnConfigService;
import com.altomni.apn.user.service.dto.customconfig.*;
import com.altomni.apn.user.service.dto.job.ColumnPreferenceDTO;
import com.altomni.apn.user.service.job.JobService;
import com.altomni.apn.user.service.mapper.customconfig.*;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class TalentColumnConfigServiceImpl implements TalentColumnConfigService {
    final static Category COLUMN_CATEGORY = Category.TALENT_COLUMN;
    final static ConfigSubcategory TALENT_COLUMN = ConfigSubcategory.TALENT_COLUMN_GENERAL;

    final static ConfigSubcategory TALENT_CUSTOMIZED_COLUMN = ConfigSubcategory.TALENT_COLUMN_CUSTOMIZED;

    final static ConfigSubcategory TALENT_DATABASE_COLUMN = ConfigSubcategory.TALENT_COLUMN_DATABASE;
    final static ConfigSubcategory TALENT_RELATE_JOB_FOLDER_COLUMN = ConfigSubcategory.TALENT_COLUMN_RELATE_JOB_FOLDER;
    final static ConfigSubcategory TALENT_RELATE_JOB_FOLDER_CUSTOMIZED = ConfigSubcategory.TALENT_COLUMN_RELATE_JOB_FOLDER_CUSTOMIZED;

    @Resource
    private SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    TalentColumnConfigMapper talentColumnConfigMapper;

    @Resource
    TalentRelateJobFolderColumnConfigMapper talentRelateJobFolderColumnConfigMapper;

    @Resource
    TalentSearchFolderColumnConfigMapper talentSearchFolderColumnConfigMapper;

    @Resource
    TalentDatabaseColumnConfigMapper talentDatabaseColumnConfigMapper;

    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    private ApplicationIPGProperties applicationIPGProperties;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public BaseConfig getTalentDatabaseColumnConfigByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        Optional<UserPreference> result = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));
        if (result.isPresent() && StringUtils.isNotBlank(result.get().getTalentDatabaseColumnConfig())) {
            return talentDatabaseColumnConfigMapper.toDto(result.get());
        } else {
            //return getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_DATABASE_COLUMN);
            return convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_DATABASE_COLUMN).getCustomConfig());
        }
    }

    @Resource
    private JobService jobService;

    @Override
    public UserTalentColumnConfigDTO getTalentColumnConfigByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        Optional<UserPreference> result = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));
        if (result.isPresent() && StringUtils.isNotBlank(result.get().getTalentColumnConfig())) {
            TalentColumnConfigDTO talentColumnConfigDTO = talentColumnConfigMapper.toDto(result.get());
            UserTalentColumnConfigDTO ret = new UserTalentColumnConfigDTO();
            BeanUtil.copyProperties(talentColumnConfigDTO, ret);
            //TODO v3需要设计如何存储createTalentType
//            setCreationTalentType(ret, userId);
            return ret;
        }
        UserTalentColumnConfigDTO ret = new UserTalentColumnConfigDTO();
        TalentColumnConfigDTO talentColumnConfigDTO = convertSystemConfigDTOToTalentColumnConfigDTO(getTalentColumnConfigByTenantId().getCustomConfig());
        BeanUtil.copyProperties(talentColumnConfigDTO, ret);
//        setCreationTalentType(ret, userId);

        return ret;
    }

    @Deprecated
    private void setCreationTalentType(UserTalentColumnConfigDTO ret, Long userId) {
        ResponseEntity<ColumnPreferenceDTO> userPreferenceByUserId = jobService.findUserPreferenceByUserId(userId, ModuleType.CANDIDATE);
        if(userPreferenceByUserId != null && userPreferenceByUserId.getStatusCode() == HttpStatus.OK) {
            ColumnPreferenceDTO body = userPreferenceByUserId.getBody();
            if(body != null) {
                CreationTypeDTO creationType = body.getCreationType();
                if(creationType != null) {
                    ret.setCreationTalentType(creationType.getCreationTalentType());
                }
            }
        }
    }

    private TalentColumnConfigDTO convertSystemConfigDTOToTalentColumnConfigDTO(JsonNode systemColumnConfig) {
        TalentColumnConfigDTO talentColumnConfigDTO = new TalentColumnConfigDTO();
        UserCustomConfig userCustomConfig = new UserCustomConfig();
        userCustomConfig.setColumnConfig(JsonMapperUtil.jsonNodeToCustomColumnFieldList(systemColumnConfig));
        talentColumnConfigDTO.setCustomConfig(userCustomConfig);
        return talentColumnConfigDTO;
    }


    private SystemConfigDTO getTalentColumnConfigByTenantId() {
        if (applicationIPGProperties.getIpgRuleTenantIds().contains(SecurityUtils.getTenantId())) {
            return getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_CUSTOMIZED_COLUMN);
        } else {
            return getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_COLUMN);
        }
    }


    private SystemConfigDTO getSystemConfigByCategoryAndSubCategory(Category category, ConfigSubcategory subcategory) {
        return systemConfigDefaultMapper.toDto(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(category, subcategory));
    }


    @Override
    public TalentColumnConfigDTO saveTalentDatabaseColumnConfig(TalentColumnConfigDTO talentColumnConfigDTO, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference != null) {
            userPreference.setTalentDatabaseColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(talentColumnConfigDTO.getCustomConfig()));
        } else {
            talentColumnConfigDTO.setUserId(userId);
            userPreference = talentDatabaseColumnConfigMapper.toEntity(talentColumnConfigDTO);
        }

        userPreference = userPreferenceRepository.saveAndFlush(userPreference);
        talentColumnConfigDTO = talentDatabaseColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO;
    }

    @Override
    public TalentColumnConfigDTO saveTalentColumnConfig(TalentColumnConfigDTO talentColumnConfigDTO, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference != null) {
            userPreference.setTalentColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(talentColumnConfigDTO.getCustomConfig()));
        } else {
            talentColumnConfigDTO.setUserId(userId);
            userPreference = talentColumnConfigMapper.toEntity(talentColumnConfigDTO);
        }

        userPreference = userPreferenceRepository.saveAndFlush(userPreference);
        talentColumnConfigDTO = talentColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO;
    }

    @Override
    public PageConfig updateTalentPageConfig(PageConfig pageConfig) {
        Long userId = SecurityUtils.getUserId();
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
        }

        UserCustomConfig userCustomConfig = Optional.ofNullable(userPreference.getTalentColumnConfig())
                .map(JsonMapperUtil::jsonStringToCustomColumnConfig)
                .orElse(new UserCustomConfig());

        //init the column config for update page only
        if (userCustomConfig.getColumnConfig() == null) {
            TalentColumnConfigDTO talentColumnConfigDTO = convertSystemConfigDTOToTalentColumnConfigDTO(getTalentColumnConfigByTenantId().getCustomConfig());
            userCustomConfig.setColumnConfig(talentColumnConfigDTO.getCustomConfig().getColumnConfig());
        }
        userCustomConfig.setPageConfig(pageConfig);

        userPreference.setTalentColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig));
        userPreference = userPreferenceRepository.saveAndFlush(userPreference);

        TalentColumnConfigDTO talentColumnConfigDTO = talentColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO.getCustomConfig().getPageConfig();
    }


    @Override
    public PageConfig updateTalentDatabasePageConfig(PageConfig pageConfig) {
        Long userId = SecurityUtils.getUserId();
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
        }

        UserCustomConfig userCustomConfig = Optional.ofNullable(userPreference.getTalentDatabaseColumnConfig())
                .map(JsonMapperUtil::jsonStringToCustomColumnConfig)
                .orElse(new UserCustomConfig());

        //init the column config for empty column config
        if (userCustomConfig.getColumnConfig() == null) {
            TalentColumnConfigDTO talentColumnConfigDTO = convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_DATABASE_COLUMN).getCustomConfig());
            userCustomConfig.setColumnConfig(talentColumnConfigDTO.getCustomConfig().getColumnConfig());
        }
        userCustomConfig.setPageConfig(pageConfig);

        userPreference.setTalentDatabaseColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig));
        userPreference = userPreferenceRepository.saveAndFlush(userPreference);

        TalentColumnConfigDTO talentColumnConfigDTO = talentDatabaseColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO.getCustomConfig().getPageConfig();
    }

    @Override
    public BaseConfig getTalentRelateJobFolderColumnConfigByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException("UserId is invalid");
        }

        Optional<UserPreference> result = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));
        if (result.isPresent() && StringUtils.isNotBlank(result.get().getTalentRelateJobFolderColumnConfig())) {
            return talentRelateJobFolderColumnConfigMapper.toDto(result.get());
        } else {
            if (applicationIPGProperties.getIpgRuleTenantIds().contains(SecurityUtils.getTenantId())) {
                return convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_RELATE_JOB_FOLDER_CUSTOMIZED).getCustomConfig());
            } else {
                return convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_RELATE_JOB_FOLDER_COLUMN).getCustomConfig());
            }
        }
    }

    @Override
    public BaseConfig saveTalentRelateJobFolderColumnConfig(TalentColumnConfigDTO talentColumnConfigDTO, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference != null) {
            userPreference.setTalentRelateJobFolderColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(talentColumnConfigDTO.getCustomConfig()));
        } else {
            talentColumnConfigDTO.setUserId(userId);
            userPreference = talentRelateJobFolderColumnConfigMapper.toEntity(talentColumnConfigDTO);
        }

        userPreference = userPreferenceRepository.saveAndFlush(userPreference);
        talentColumnConfigDTO = talentRelateJobFolderColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO;
    }

    @Override
    public PageConfig updateTalentTalentRelateJobFolderPageConfig(PageConfig pageConfig) {
        Long userId = SecurityUtils.getUserId();
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
        }

        UserCustomConfig userCustomConfig = Optional.ofNullable(userPreference.getTalentRelateJobFolderColumnConfig())
                .map(JsonMapperUtil::jsonStringToCustomColumnConfig)
                .orElse(new UserCustomConfig());

        //init the column config for update page only
        if (userCustomConfig.getColumnConfig() == null) {
            TalentColumnConfigDTO talentColumnConfigDTO = convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_RELATE_JOB_FOLDER_COLUMN).getCustomConfig());
            userCustomConfig.setColumnConfig(talentColumnConfigDTO.getCustomConfig().getColumnConfig());
        }
        userCustomConfig.setPageConfig(pageConfig);

        userPreference.setTalentRelateJobFolderColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig));
        userPreference = userPreferenceRepository.saveAndFlush(userPreference);

        TalentColumnConfigDTO talentColumnConfigDTO = talentRelateJobFolderColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO.getCustomConfig().getPageConfig();
    }

    @Override
    public BaseConfig getTalentSearchFolderColumnConfigByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException("UserId is invalid");
        }

        Optional<UserPreference> result = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));
        if (result.isPresent() && StringUtils.isNotBlank(result.get().getTalentSearchFolderColumnConfig())) {
            return talentSearchFolderColumnConfigMapper.toDto(result.get());
        } else {
            if (applicationIPGProperties.getIpgRuleTenantIds().contains(SecurityUtils.getTenantId())) {
                return convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_CUSTOMIZED_COLUMN).getCustomConfig());
            } else {
                return convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, TALENT_COLUMN).getCustomConfig());
            }
        }
    }

    @Override
    public BaseConfig updateTalentSearchFolderColumnConfig(TalentColumnConfigDTO talentColumnConfigDTO, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference != null) {
            userPreference.setTalentSearchFolderColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(talentColumnConfigDTO.getCustomConfig()));
        } else {
            talentColumnConfigDTO.setUserId(userId);
            userPreference = talentSearchFolderColumnConfigMapper.toEntity(talentColumnConfigDTO);
        }

        userPreference = userPreferenceRepository.saveAndFlush(userPreference);
        talentColumnConfigDTO = talentSearchFolderColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO;
    }

    @Override
    public PageConfig updateTalentSearchFolderPageConfig(PageConfig pageConfig) {
        Long userId = SecurityUtils.getUserId();
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
        }

        UserCustomConfig userCustomConfig = Optional.ofNullable(userPreference.getTalentSearchFolderColumnConfig())
                .map(JsonMapperUtil::jsonStringToCustomColumnConfig)
                .orElse(new UserCustomConfig());

        //init the column config for update page only
        if (userCustomConfig.getColumnConfig() == null) {
            TalentColumnConfigDTO talentColumnConfigDTO = convertSystemConfigDTOToTalentColumnConfigDTO(getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, applicationIPGProperties.getIpgRuleTenantIds().contains(SecurityUtils.getTenantId()) ? TALENT_CUSTOMIZED_COLUMN : TALENT_COLUMN).getCustomConfig());
            userCustomConfig.setColumnConfig(talentColumnConfigDTO.getCustomConfig().getColumnConfig());
        }
        userCustomConfig.setPageConfig(pageConfig);

        userPreference.setTalentSearchFolderColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig));
        userPreference = userPreferenceRepository.saveAndFlush(userPreference);

        TalentColumnConfigDTO talentColumnConfigDTO = talentSearchFolderColumnConfigMapper.toDto(userPreference);

        return talentColumnConfigDTO.getCustomConfig().getPageConfig();
    }

}

