package com.altomni.apn.user.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.domain.enumeration.SyncLarkEnum;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderStatus;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderUserType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.permission.PermissionTeamSimple;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolder;
import com.altomni.apn.common.domain.user.*;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.folder.TeamUserSetRollList;
import com.altomni.apn.common.dto.permission.PermissionPrivilegeDataSimple;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.sso.SsoUserActive;
import com.altomni.apn.common.dto.sso.SsoUserBinding;
import com.altomni.apn.common.dto.sso.SsoUserInfo;
import com.altomni.apn.common.dto.user.*;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTimezoneDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.EmailAlreadyUsedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.LoginAlreadyUsedException;
import com.altomni.apn.common.repository.job.BaseJobRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.user.config.env.ApplicationProperties;
import com.altomni.apn.user.config.env.CanalProperties;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.enumeration.CreditEffectType;
import com.altomni.apn.user.domain.enumeration.CreditType;
import com.altomni.apn.user.domain.permission.PermissionExtraRoleTeam;
import com.altomni.apn.user.domain.permission.PermissionExtraUserTeam;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.domain.user.PermissionTeamMemberVM;
import com.altomni.apn.user.domain.user.UserAccount;
import com.altomni.apn.user.domain.user.UserInfoWithPermission;
import com.altomni.apn.user.domain.user.UserRoleWithPermission;
import com.altomni.apn.user.repository.customconfig.TenantPushRulesRepository;
import com.altomni.apn.user.repository.permission.*;
import com.altomni.apn.user.repository.user.*;
import com.altomni.apn.user.service.authority.AuthorityService;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.cache.CacheUser;
import com.altomni.apn.user.service.calendar.CalendarService;
import com.altomni.apn.user.service.company.CompanyService;
import com.altomni.apn.user.service.customconfig.TenantConfigService;
import com.altomni.apn.user.service.dto.permission.*;
import com.altomni.apn.user.service.dto.user.*;
import com.altomni.apn.user.service.management.ManagementService;
import com.altomni.apn.user.service.permission.PermissionTeamService;
import com.altomni.apn.user.service.permission.PermissionUserTeamService;
import com.altomni.apn.user.service.query.UserSearch;
import com.altomni.apn.user.service.talent.TalentService;
import com.altomni.apn.user.service.tenant.TenantService;
import com.altomni.apn.user.service.user.UserAccountService;
import com.altomni.apn.user.service.user.UserService;
import com.altomni.apn.user.service.xxljob.XxlJobService;
import com.altomni.apn.user.web.rest.vm.ManagedUserVM;
import com.altomni.apn.user.web.rest.vm.RefreshTokenVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamDeliverySearchVM;
import com.altomni.apn.user.web.rest.vm.permission.RelateJobFolderUserAllInfo;
import com.altomni.apn.user.web.rest.vm.permission.RelateJobFolderUserInfo;
import com.altomni.apn.user.web.rest.vm.user.UserPushIntervalDTO;
import com.altomni.apn.user.web.rest.vm.user.UserVM;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigInteger;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.TenantGeneralConfigConstants.DEFAULT_CURRENCY;

/**
 * Service class for managing users.
 */
@Slf4j
@Service
@Transactional
public class UserServiceImpl implements UserService {
    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD).withZone(ZoneId.of("UTC"));

    public static final long MAXIMUM_LOGIN_FAILED_COUNT = 5;

    @Resource
    private AuthorityService authorityService;

    @Resource
    private ApplicationProperties properties;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Resource
    private PermissionUserRoleRepository  userRoleRepository;

    @Resource
    private TenantPushRulesRepository tenantPushRulesRepository;

    @Resource
    private TenantService tenantService;

    //private AnalyticsService analyticsService;

    @Resource
    private UserAccountService userAccountService;

    @Resource
    private UserAccountRepository accountRepository;

    @Resource
    private PermissionUserTeamRepository permissionUserTeamRepository;

    @Resource
    private PermissionTeamRepository permissionTeamRepository;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private InitiationService initiationService;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private UserImpersonationRepository userImpersonationRepository;

    @Resource
    private ManagementService managementService;

    @Resource
    private CalendarService calendarService;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private TenantConfigService tenantConfigService;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;


    @Resource
    private PermissionExtraUserTeamRepository permissionExtraUserTeamRepository;

    @Resource
    private PermissionExtraRoleTeamRepository permissionExtraRoleTeamRepository;

    @Resource
    private PermissionTeamService permissionTeamService;

    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    @Resource
    private BaseJobRepository baseJobRepository;

    @Resource
    private UserLanguageRelationRepository userLanguageRelationRepository;

    @Resource
    private UserDeliveryCountryRelationRepository userDeliveryCountryRelationRepository;

    @Resource
    private UserDeliveryProcessRelationRepository userDeliveryProcessRelationRepository;

    @Resource
    private UserDeliveryIndustryRelationRepository userDeliveryIndustryRelationRepository;

    @Resource
    private UserDeliveryJobFunctionRelationRepository userDeliveryJobFunctionRelationRepository;

    @Resource
    private PermissionTeamLeaderRepository permissionTeamLeaderRepository;

    @Resource
    private TalentService talentService;

    @Resource
    private CompanyService companyService;

    @Resource
    private PermissionUserTeamService permissionUserTeamService;

    @Value("${application.notification.lark.apn-create-user.webhookKey:}")
    private String webhookKey;

    @Value("${application.notification.lark.apn-create-user.webhookUrl:}")
    private String webhookUrl;

    @Resource
    private CacheUser cacheUser;

    @Value("${application.user.account.expire_time:600}")
    private Long userAccountExpireTime;

    @Value("${application.user.account.isOpen:false}")
    private Boolean cacheIsOpen;

    @PersistenceContext
    private EntityManager entityManager;


    /**
     * User login. Call the oauth2 login api and return user profile
     *
     * @param loginVM username and password
     * @return jsonObject access token & refresh token
     */
    @Override
    public LoginUserDTO login(LoginVM loginVM) {
        String username = loginVM.getUsername();
        this.checkLockedAccount(username);
        return this.getUserWithAuthoritiesByLogin(loginVM.getUsername()).map(user -> {
            if (!user.isActivated()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_LOGIN_INACTIVEUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
            }
            try {
                String uid = user.getUid();
                LoginUserDTO loginUserDTO = new LoginUserDTO();
                Long accessTokenExpireInSeconds = properties.getActiveUserPeriod().longValue();
                Long userIdFrom = user.getId();
                Long userIdTo = user.getId();
                // For impersonation purpose
                if (Objects.nonNull(loginVM.getTargetUserId())) {
                    //user = getUserWithAuthoritiesByLogin(loginVM.getTargetUserId()).get();
                    final Optional<Instant> expireAtOpt = userImpersonationRepository.
                            findExpireTime(loginVM.getTargetUserId(),
                                    user.getId(), user.getTenantId(), Instant.now());
                    if (expireAtOpt.isEmpty()) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_LOGIN_DENIEDPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
                    }
                    userIdFrom = loginVM.getTargetUserId();
                    final Instant expireAt = expireAtOpt.get();
                    user = userRepository.getById(loginVM.getTargetUserId());
                    this.checkLockedAccount(user.getEmail());
                    this.checkLockedAccount(user.getUsername());
                    accessTokenExpireInSeconds = Math.min(accessTokenExpireInSeconds, Duration.between(Instant.now(), expireAt).getSeconds());
                } else {
                    final List<LoginUserDTO.Impersonation> impersonations = userImpersonationRepository
                            .findAllAvailableGrantFromUserByGrantToUserIdAndTenantId(user.getId(), user.getTenantId(), Instant.now())
                            .stream()
                            .map(impersonation -> new LoginUserDTO.Impersonation(impersonation.getGrantToUser().getId(),
                                    impersonation.getGrantToUser().getEmail(),
                                    impersonation.getExpireAt()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(impersonations)) {
                        loginUserDTO.setAvailableImpersonations(impersonations);
                    }
                }
                loginVM.setUsername(uid);
                loginVM.setPassword(AES.decrypt(loginVM.getPassword(), properties.getSecret()));
                CredentialDTO credentialDTO = authorityService.findCredential(loginVM).getBody();
                BeanUtils.copyProperties(user, loginUserDTO);
                loginUserDTO.setCredential(credentialDTO);
                List<Object> authorities = user.getRoles().stream().map(role -> new JSONObject(new HashMap<>() {{
                    put("name", role.getName());
                }})).collect(Collectors.toList());
                //loginUserDTO.setAuthorities(authorities);
                this.removeLoginFailedUser(username);
                this.saveActiveUser(userIdFrom, userIdTo, credentialDTO.getAccess_token(), accessTokenExpireInSeconds.intValue());
                return loginUserDTO;
            } catch (Exception e) {
                loginVM.setUsername(username);
                log.error("[UserService.login] Login user {}, exception {}", loginVM, ExceptionUtils.getStackTrace(e));
                String errorMsg = this.countLoginFailed(username);
                String message = "The username and password do not match.";
                if (e instanceof CustomParameterizedException) {
                    message = e.getMessage();
                }
                throw new CustomParameterizedException(message + " " + errorMsg);
            }
        }).orElseThrow(() -> new CustomParameterizedException("The username and password do not match."));
    }

    protected void validateAccount(ManagedUserVM managedUserVM) {
        if (managedUserVM.getTenantId() == null || managedUserVM.getTenantId().equals(1L)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        this.findOneByLogin(managedUserVM.getEmail()).ifPresent(user -> {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_EMAILEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        });
        if (managedUserVM.getUsername() != null) {
            this.findOneByLogin(managedUserVM.getUsername()).ifPresent(user -> {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_USERNAMEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
            });
        }
    }

    @Override
    public Optional<User> completePasswordReset(String newPassword, String key) {
        log.info("Reset user password by reset key");
        String decryptedPassword = LoginUtil.validatePassword(newPassword, properties.getSecret());
        return userRepository.findOneByResetKey(key)
                .filter(user -> user.getResetDate().isAfter(Instant.now().minusSeconds(86400)))
                .map(user -> {
                    user.setPassword(passwordEncoder.encode(decryptedPassword));
                    user.setResetKey(null);
                    user.setResetDate(null);
                    return user;
                });
    }

    @Override
    public Optional<User> requestPasswordReset(String mail) {
        return userRepository.findOneByEmail(mail)
                .filter(User::isActivated)
                .map(user -> {
                    user.setResetKey(RandomUtil.generateResetKey());
                    user.setResetDate(Instant.now());
                    return user;
                });
    }

    /**
     * Recruiter / Consumer registration
     *
     * @return User object
     */
    @Override
    public User registerAccount(ManagedUserVM managedUserVM) {
        String decryptedPassword = LoginUtil.validatePassword(managedUserVM.getPassword(), properties.getSecret());
        this.validateAccount(managedUserVM);
        User newUser = new User();
        Set<Role> authorities = new HashSet<>();
        String encryptedPassword = passwordEncoder.encode(decryptedPassword);
        newUser.setUsername(managedUserVM.getUsername());
        // new user gets initially a generated password
        newUser.setPassword(encryptedPassword);
        newUser.setFirstName(managedUserVM.getFirstName());
        newUser.setLastName(managedUserVM.getLastName());
        newUser.setEmail(managedUserVM.getEmail());
        newUser.setPhone(managedUserVM.getPhone());
        newUser.setImageUrl(managedUserVM.getImageUrl());
        newUser.setLangKey(managedUserVM.getLangKey());
        newUser.setNote(managedUserVM.getNote());
        // new user active by default
        if (managedUserVM.getTenantId().equals(1L)) { // this is consumer
            newUser.setActivated(true);
        } else { // this is recruiter
            newUser.setActivated(false);
            newUser.setCancellationTime(Instant.now());
        }
        // new user gets registration key
        // newUser.setActivationKey(RandomUtil.generateActivationKey());
        //authorities.add(authority);
        newUser.setRoles(authorities);
        Tenant tenant = managementService.queryTenant(managedUserVM.getTenantId()).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_REGISTERACCOUNT_TENANTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        newUser.setTenantId(managedUserVM.getTenantId());
        newUser.setDataScope(DataScope.PERMISSION_SELF.toDbValue());
        newUser = addUser(newUser);
        // generate uid, this is to generate oauth token
        newUser.setUid(newUser.getId() + "," + managedUserVM.getTenantId());
        User result = userRepository.saveAndFlush(newUser);
        if (!ObjectUtils.equals(result.getTenantId(), 1L)) {
            UserAccount userAccount = userAccountService.createUserAccount(result.getId(), managedUserVM.getTenantId(), null, null, null, CreditEffectType.INSTANT);
            log.debug("Init user account: {}", userAccount);
        }
        log.debug("New User Registration: {}", result);
        //analyticsService.identify(result); TODO
        NotificationUtils.sendAlertToLark(webhookKey, webhookUrl, getCreateApnUserLarkMessage(newUser, tenant));
        return result;
    }

    //
    // In order to be compatible with the transition version of the service, the management service temporarily cancels the limitation on the maximum number of tenant users. Subsequent V3 will reopen this business.
    //
    private synchronized User addUser(User user) {
        Tenant tenant = managementService.queryTenant(user.getTenantId()).getBody();
        int userCount = userRepository.countByTenantId(user.getTenantId());
        if (tenant != null && userCount >= tenant.getUserMaxLimit()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_ADDUSER_ACCOUNTMAXLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }

        return userRepository.saveAndFlush(user);
    }

//    private User addUser(User user) {
//        return userRepository.saveAndFlush(user);
//    }

    /**
     * Admin create recruiter
     *
     * @param userDTO
     * @return
     */
    @Override
    @Transactional
    public User createUser(UserDTO userDTO) {

        Tenant tenant = managementService.queryTenant(userDTO.getTenantId()).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_REGISTERACCOUNT_TENANTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        Integer totalUserCredit = userRepository.findTotalUsedCreditByTenant(tenant.getId(), CreditType.BULK.toDbValue());
        if (totalUserCredit != null && userDTO.getMonthlyCredit() != null && tenant.getBulkCredit() != null && userDTO.getMonthlyCredit() > (tenant.getBulkCredit() - totalUserCredit)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATEUSER_TOTALUSERCREDITERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList((tenant.getBulkCredit() - totalUserCredit)), userApiPromptProperties.getUserService()));
        }
        validateCredit(tenant.getId(), userDTO, new UserAccount());

        User user = new User();
        user.setId(userDTO.getId());
        user.setJobTitle(userDTO.getJobTitle());
        user.setUsername(userDTO.getUsername());
        user.setFirstName(userDTO.getFirstName());
        user.setLastName(userDTO.getLastName());
        user.setEmail(userDTO.getEmail());
        user.setImageUrl(userDTO.getImageUrl());
        user.setMonthlyCredit(userDTO.getMonthlyCredit());
        user.setBulkCredit(userDTO.getBulkCredit());
        user.setPhone(userDTO.getPhone());
        user.setCustomTimezone(userDTO.getCustomTimezone());
        if (userDTO.getLangKey() == null) {
            // default language
            user.setLangKey("en");
        } else {
            user.setLangKey(userDTO.getLangKey());
        }
        String encryptedPassword = passwordEncoder.encode(RandomUtil.generatePassword());
        user.setPassword(encryptedPassword);
        user.setResetKey(RandomUtil.generateResetKey());
        user.setResetDate(Instant.now());
        user.setActivated(true);

        user.setTenantId(userDTO.getTenantId());
        user.setNote(userDTO.getNote());
        user = addUser(user);
        // save uid, this is used to generate oauth token
        user.setUid(user.getId() + "," + userDTO.getTenantId());
        /*if (userDTO.getAuthorities() != null) {
            Set<Role> authorities = new HashSet<>(userDTO.getAuthorities());
            user.setRoles(authorities);
        }*/
        user.setEnumLevelOfExperienceId(userDTO.getLevelOfExperience());
        user.setUserLanguageRelation(userDTO.getLanguages());

        user = userRepository.saveAndFlush(user);
        if (!ObjectUtils.equals(user.getTenantId(), 1L)) {
            UserAccount userAccount = userAccountService.createUserAccount(user.getId(), user.getTenantId(), userDTO.getMonthlyCredit(), userDTO.getBulkCredit(), userDTO.getEffectCredit(), userDTO.getCreditEffectType());
            log.debug("Init user account: {}", userAccount);
        }
        log.debug("New Consumer Registration: {}", user);
        this.initializeUserTeams(user.getId(), userDTO.getPrimaryTeamId(), userDTO.getSecondaryTeamIds());
        user.setPrimaryTeamId(userDTO.getPrimaryTeamId());
        user.setSecondaryTeamIds(userDTO.getSecondaryTeamIds());
        ManagedUserVM managedUserVM = new ManagedUserVM();
        managedUserVM.setId(user.getId());
        managedUserVM.setEmail(user.getEmail());
        managedUserVM.setTenantId(user.getTenantId());
        NotificationUtils.sendAlertToLark(webhookKey, webhookUrl, getCreateApnUserLarkMessage(user, tenant));
        return user;
    }

    private String getCreateApnUserLarkMessage(User user, Tenant tenant) {
        StringBuilder sb = new StringBuilder();
        sb.append("租户【{tenantName}】新建用户【{userName}】, 邮箱：【{email}】");
        Map<String, String> map = new HashMap<>(16);
        map.put("tenantName", tenant.getName());
        map.put("userName", CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
        map.put("email", user.getEmail());
        Optional.ofNullable(user.getPrimaryTeamId())
                .flatMap(primaryTeamId -> permissionTeamRepository.findById(user.getPrimaryTeamId()))
                .ifPresent(permissionTeam -> sb.append(", 主团队: 【").append(permissionTeam.getName()).append("】"));
        return StrUtil.format(sb, map);
    }



    @Override
    @Transactional
    public void createTenantAdminUser(TenantAdminUserDto userDTO) {
        if (userRepository.findOneByEmailIgnoreCase(userDTO.email()).isPresent()) {
            throw new EmailAlreadyUsedException();
        }
        User user = new User();
        user.setId(userDTO.id());
        user.setTenantId(userDTO.tenantId());
        user.setUsername(userDTO.email());
        user.setFirstName(userDTO.firstName());
        user.setLastName(userDTO.lastName());
        user.setMonthlyCredit(0);
        user.setEmail(userDTO.email());
        user.setLangKey("en");
        // 密码是在 sso 系统设置的
        String password = "{NO_PASSWORD}";
        String encryptedPassword = passwordEncoder.encode(password);
        user.setPassword(encryptedPassword);
        user.setResetKey(RandomUtil.generateResetKey());
        user.setResetDate(Instant.now());
        user.setActivated(true);
        user.setCustomTimezone("Asia/Shanghai");
        user.setDataScope(DataScope.PERMISSION_ALL.toDbValue());

        log.info(user.toString());
        try {
            user = addUser(user);
            user.setUid(user.getId() + "," + userDTO.tenantId());
            Role role = roleRepository.findFirstByNameAndInternal(AuthoritiesConstants.TENANT_ADMIN, Boolean.TRUE);
            user.getRoles().add(role);
            user = userRepository.save(user);
            UserAccount userAccount = userAccountService.createTenantAdminUserAccount(user.getId(), userDTO.tenantId(), 0);
            log.debug("Init user account: {}", userAccount);
            log.debug("New Consumer Registration: {}", user);
        } catch (Exception e) {
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATETENANTADMINUSER_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
    }


    /**
     * Update basic information (first name, last name, email, language) for the current user.
     *
     * @param firstName first name of user
     * @param lastName  last name of user
     * @param email     email id of user
     * @param langKey   language key
     * @param imageUrl  image URL of user
     */
    @Override
    public void updateUser(String firstName, String lastName, String email, String langKey, String imageUrl) {
        userRepository.findUser(SecurityUtils.getCurrentUserLogin().get().getUsername()).ifPresent(user -> {
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setEmail(email);
            user.setLangKey(langKey);
            user.setImageUrl(imageUrl);
            userRepository.saveAndFlush(user);
            log.debug("Changed Information for User: {}", user);
        });
    }

    @Override
    @Transactional
    public UserDTO updateUser(Long userId, UserDTO userDTO) {
        User user = userRepository.findById(userId).orElse(null);
        checkRequiredFields(user, userDTO);
        if (user == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSER_USERIDNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(userId), userApiPromptProperties.getUserService()));
        }
        boolean activated = user.isActivated();
        userDTO.setId(userId);
        userDTO.setTenantId(user.getTenantId());
        Optional<User> existingUser = userRepository.findOneByEmail(userDTO.getEmail());
        if (existingUser.isPresent() && (!existingUser.get().getId().equals(userDTO.getId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_EMAILEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        existingUser = findOneByLogin(userDTO.getUsername());
        if (existingUser.isPresent() && (!existingUser.get().getId().equals(userDTO.getId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_USERNAMEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        if (!user.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        Long previousPrimaryTeamId = null;
        if (ObjectUtil.isNotEmpty(userDTO.getPrimaryTeamId())) {
            previousPrimaryTeamId = this.updateUserTeamsAndReturnPrePrimaryTeamId(userId, userDTO.getPrimaryTeamId(), userDTO.getSecondaryTeamIds());
        }
        ServiceUtils.myCopyProperties(userDTO, user, UserDTO.UPDATE_SKIP_PROPERTIES);
        UserAccount userAccount = this.refreshAccount(userDTO);

        user.setMonthlyCredit(userAccount.getMonthlyAmount());
        user.setBulkCredit(userAccount.getBulkCredit());
        user.setEnumLevelOfExperienceId(userDTO.getLevelOfExperience());
        user.setUserLanguageRelation(userDTO.getLanguages());
        //发生变化的时候触发
        if (userDTO.getActivated() != null && !Objects.equals(activated, userDTO.getActivated())) {
            if (userDTO.getActivated()) {
                //激活
                user.setCancellationTime(null);
            } else {
                //非激活, 离职
                user.setCancellationTime(Instant.now());
                Long newTalentOwner = permissionUserTeamService.getSuperiorLeader(userId);
                talentService.transferOwnership(userId, newTalentOwner);
                talentService.handoverConfidentialTalent(userId, newTalentOwner);
            }
        }

        user = userRepository.save(user);
        log.debug("Changed Information for User: {}", user);

        UserDTO result = new UserDTO(user);
        result.setJobTitle(user.getJobTitle());
        result.setUsedBulkCredit(userDTO.getUsedBulkCredit());
        result.setUsedMonthlyCredit(userDTO.getUsedMonthlyCredit());
        result.setEffectCredit(userAccount.getEffectCredit());
        result.setCreditEffectType(userAccount.getCreditEffectType());
        result.setLevelOfExperience(user.getEnumLevelOfExperienceId());
        result.setLanguages(userDTO.getLanguages());

        //set teams infos
        setTeams(result, userId);
//        Long primaryId = userDTO.getPrimaryTeamId();
//        if (Objects.isNull(primaryId)){
//            Optional<PermissionUserTeam> primary = permissionUserTeamRepository.findFirstByUserIdAndIsPrimary(user.getId(), Boolean.TRUE);
//            if (primary.isPresent()){
//                primaryId = primary.get().getTeamId();
//            }
//        }
//        result.setPrimaryTeamId(primaryId);
//        result.setTeams(result.getTeams().stream().map(t-> t.setIsPrimaryTeam(t.getId().equals(result.getPrimaryTeamId()))).collect(Collectors.toSet()));
//        Set<Long> secondaryTeamIds = userDTO.getSecondaryTeamIds();
//        if (CollectionUtils.isEmpty(secondaryTeamIds)){
//            secondaryTeamIds = result.getTeams().stream().filter(team-> !team.getIsPrimaryTeam()).map(PermissionUserTeamDTO::getId).collect(Collectors.toSet());
//        }
//        result.setSecondaryTeamIds(secondaryTeamIds);
        result.setPreviousTeamId(previousPrimaryTeamId);

        talentService.teamAddUserSetRollList(new TeamUserSetRollList(List.of(userId), userDTO.getPrimaryTeamId(), userDTO.getPrimaryTeamId()));
        talentService.teamRemoveUserSetRollList(new TeamUserSetRollList(List.of(userId), previousPrimaryTeamId, userDTO.getPrimaryTeamId()));
        return result;
    }


    private void setTeams(UserDTO result, Long userId) {
        Set<PermissionUserTeamDTO> teamDTOSet = new HashSet<>();
        Set<Long> secondaryTeamIds = new HashSet<>();
        List<Object[]> teamsList = permissionUserTeamRepository.findTeamsByUserId(userId);
        teamsList.forEach(s -> {
            PermissionUserTeamDTO dto = new PermissionUserTeamDTO();
            dto.setId(Long.parseLong(StrUtil.toString(s[0])));
            dto.setName(StrUtil.toString(s[1]));
            if (BooleanUtil.toBoolean(StrUtil.toString(s[2]))) {
                result.setPrimaryTeamId(dto.getId());
            } else {
                secondaryTeamIds.add(dto.getId());
            }
            dto.setIsPrimaryTeam(BooleanUtil.toBoolean(StrUtil.toString(s[2])));
            teamDTOSet.add(dto);
        });
        result.setSecondaryTeamIds(secondaryTeamIds);
        result.setTeams(teamDTOSet);

    }

    private void checkRequiredFields(User user, UserDTO userDTO) {
        //if active user, check the mandatory field
        if (userDTO.getActivated() != null && userDTO.getActivated()) {
            if ((org.apache.commons.lang3.StringUtils.isBlank(user.getUsername()) || org.apache.commons.lang3.StringUtils.isBlank(user.getFirstName())
                 || org.apache.commons.lang3.StringUtils.isBlank(user.getLastName()) || org.apache.commons.lang3.StringUtils.isBlank(user.getEmail())
                 || CollUtil.isEmpty(user.getTeams())) && (org.apache.commons.lang3.StringUtils.isBlank(userDTO.getUsername())
                                                           || org.apache.commons.lang3.StringUtils.isBlank(userDTO.getFirstName()) || org.apache.commons.lang3.StringUtils.isBlank(userDTO.getLastName())
                                                           || org.apache.commons.lang3.StringUtils.isBlank(userDTO.getEmail()) || ObjectUtil.isEmpty(userDTO.getPrimaryTeamId()))) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CHECKREQUIREDFIELDS_EMPTY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));

            }
        }
    }

    private UserAccount refreshAccount(UserDTO userDTO) {
        UserAccount userAccount = userAccountService.findByUserId(userDTO.getId());
        if (Objects.isNull(userDTO.getCreditEffectType())) {
            userDTO.setCreditEffectType(CreditEffectType.INSTANT);
        }
        if (Objects.isNull(userAccount)) {
            userAccount = userAccountService.createUserAccount(userDTO.getId(), userDTO.getTenantId(), userDTO.getMonthlyCredit(), userDTO.getBulkCredit(), userDTO.getEffectCredit(), userDTO.getCreditEffectType());
            return userAccount;
        }

        boolean accountChanged = false;

        if (userDTO.getCreditEffectType() == CreditEffectType.NEXT_MONTH) {
            userDTO.setMonthlyCredit(userAccount.getMonthlyAmount());
        } else {
            userDTO.setEffectCredit(userAccount.getEffectCredit());
        }

        validateCredit(userDTO.getTenantId(), userDTO, userAccount);

        if (Objects.nonNull(userDTO.getBulkCredit()) && !userDTO.getBulkCredit().equals(userAccount.getBulkCredit())) {
            userAccount.setBulkCredit(userDTO.getBulkCredit());
            accountChanged = true;
        }
        if (Objects.nonNull(userDTO.getMonthlyCredit()) && !userDTO.getMonthlyCredit().equals(userAccount.getMonthlyAmount())) {
            userAccount.setMonthlyAmount(userDTO.getMonthlyCredit());
            accountChanged = true;
        }
        if (Objects.nonNull(userDTO.getEffectCredit()) && !userDTO.getEffectCredit().equals(userAccount.getEffectCredit())) {
            userAccount.setEffectCredit(userDTO.getEffectCredit());
            accountChanged = true;
        }
        if (Objects.nonNull(userDTO.getCreditEffectType()) && userDTO.getCreditEffectType() != userAccount.getCreditEffectType()) {
            userAccount.setCreditEffectType(userDTO.getCreditEffectType());
            accountChanged = true;
        }

        if (userDTO.getActivated() != null && BooleanUtils.isFalse(userDTO.getActivated())) {
            userAccount.setBulkCredit(0);
            userAccount.setMonthlyAmount(0);
            userAccount.setEffectCredit(0);
            accountChanged = true;
        }

        if (Objects.isNull(userAccount.getCreditEffectType())) {
            userAccount.setCreditEffectType(CreditEffectType.INSTANT);
            accountChanged = true;
        }

        if (accountChanged) {
            if (Objects.isNull(userAccount.getVersion())) {
                userAccount.setVersion(0);
            }
            if (userAccount.getCreditEffectType() != CreditEffectType.NEXT_MONTH) {
                userAccount.setEffectCredit(null);
            }
            userAccountService.updateById(userAccount.getId(), userAccount.getMonthlyAmount(), userAccount.getBulkCredit(), userAccount.getEffectCredit(), userAccount.getCreditEffectType(), userAccount.getVersion());
        }
        return userAccount;
    }

    private void initializeUserTeams(Long userId, Long primaryTeamId, Set<Long> secondaryTeamIds) {
        if (primaryTeamId == null && CollectionUtils.isEmpty(secondaryTeamIds)) {
            return;
        }
        primaryTeamId = this.checkPrimaryTeam(primaryTeamId);
        secondaryTeamIds = this.checkSecondaryTeams(secondaryTeamIds);
        List<PermissionUserTeam> userTeams = new ArrayList<>();
        userTeams.add(new PermissionUserTeam(null, userId, primaryTeamId, SecurityUtils.getTenantId(), Boolean.TRUE));
        if (CollectionUtils.isNotEmpty(secondaryTeamIds)) {
            userTeams.addAll(secondaryTeamIds.stream().map(teamId -> new PermissionUserTeam(null, userId, teamId, SecurityUtils.getTenantId(), Boolean.FALSE))
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(userTeams)) {
            permissionUserTeamRepository.saveAll(userTeams);
        }
        cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteClientContactDataPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteReportDataPermissionCacheByUserId(userId);
    }

    private Long updateUserTeamsAndReturnPrePrimaryTeamId(Long userId, Long newPrimaryTeamId, Set<Long> newSecondaryTeamIds) {
        newPrimaryTeamId = this.checkPrimaryTeam(newPrimaryTeamId);
        newSecondaryTeamIds = this.checkSecondaryTeams(newSecondaryTeamIds);
        Long previousPrimaryTeamId = -1L;
        Set<Long> previousSecondaryTeamIds = new HashSet<>();
        for (PermissionUserTeam permissionUserTeam : permissionUserTeamRepository.findAllByUserId(userId)) {
            if (permissionUserTeam.getIsPrimary()) {
                previousPrimaryTeamId = permissionUserTeam.getTeamId();
            } else {
                previousSecondaryTeamIds.add(permissionUserTeam.getTeamId());
            }
        }
        if (!newPrimaryTeamId.equals(previousPrimaryTeamId) || !previousSecondaryTeamIds.equals(newSecondaryTeamIds)) {
            permissionUserTeamRepository.deleteByUserId(userId);
            //清理 team leader 关系
            if (!newPrimaryTeamId.equals(previousPrimaryTeamId)) {
                removeTeamLeader(userId, previousPrimaryTeamId);
            }
            if (!previousSecondaryTeamIds.equals(newSecondaryTeamIds)) {
                Set<Long> finalNewSecondaryTeamIds = newSecondaryTeamIds;
                previousSecondaryTeamIds.stream().filter(teamId -> !finalNewSecondaryTeamIds.contains(teamId))
                        .forEach(teamId -> removeTeamLeader(userId, teamId));
            }
            this.initializeUserTeams(userId, newPrimaryTeamId, newSecondaryTeamIds);
        }
        /*if (Objects.nonNull(teamIds)){
            Set<Long> removingTeams = Sets.difference(previousTeamIds, teamIds);
            Set<Long> newTeams = Sets.difference(teamIds, previousTeamIds);
            this.removeUserTeams(userId, removingTeams);
            this.addUserTeams(userId, newTeams);
        }*/
        return previousPrimaryTeamId;
    }

    private void removeTeamLeader(Long userId, Long previousTeamId) {
        permissionTeamLeaderRepository.deleteAllByTeamIdAndUserIdIn(previousTeamId, Set.of(userId));
    }

    private Long checkPrimaryTeam(Long primaryTeamId) {
        if (Objects.isNull(primaryTeamId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CHECKPRIMARYTEAM_PRIMARYTEAMIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        if (!permissionTeamRepository.existsByIdAndTenantIdAndDeleted(primaryTeamId, SecurityUtils.getTenantId(), Boolean.FALSE)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CHECKPRIMARYTEAM_PRIMARYTEAMNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        return primaryTeamId;
    }

    private Set<Long> checkSecondaryTeams(Set<Long> teamIds) {
        if (Objects.isNull(teamIds)) {
            teamIds = new HashSet<>();
        }
        teamIds.stream().forEach(teamId -> {
            if (!permissionTeamRepository.existsByIdAndTenantIdAndDeleted(teamId, SecurityUtils.getTenantId(), Boolean.FALSE)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CHECKPRIMARYTEAM_PRIMARYTEAMNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
            }
        });
        return teamIds;
    }

    /*private void removeUserTeams(Long userId, Set<Long> teamIds){
        if (CollectionUtils.isNotEmpty(teamIds)){
            teamIds.forEach(teamId -> permissionUserTeamRepository.deleteByUserIdAndTeamId(userId, teamId));
        }
    }*/
    /*private void addUserTeams(Long userId, Set<Long> teamIds){
        if (CollectionUtils.isNotEmpty(teamIds)){
            permissionUserTeamRepository.saveAll(teamIds.stream().map(teamId -> new PermissionUserTeam(null, userId, teamId))
                    .collect(Collectors.toList()));
        }
    }*/

    private void validateCredit(Long tenantId, UserDTO userDTO, UserAccount userAccount) {
        TenantCreditInfoVO vo = tenantService.findAvailableCredit(tenantId);
        if (Objects.isNull(userAccount.getBulkCredit())) {
            userAccount.setBulkCredit(0);
        }
        if (Objects.nonNull(userDTO.getBulkCredit()) && vo.getAvailableBulkCredit() < (userDTO.getBulkCredit() - userAccount.getBulkCredit())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATECREDIT_CREDITNOTENOUGH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        if (Objects.isNull(userAccount.getMonthlyAmount())) {
            userAccount.setMonthlyAmount(0);
        }
        if (Objects.nonNull(userDTO.getMonthlyCredit()) && vo.getAvailableMonthlyCredit() < (userDTO.getMonthlyCredit() - userAccount.getMonthlyAmount())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATECREDIT_MONTHLYCREDITNOTENOUGH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }

        int effectCredit = Objects.isNull(userAccount.getEffectCredit()) ? 0 : userAccount.getEffectCredit();
        if (Objects.nonNull(userDTO.getEffectCredit()) && vo.getNextMonthAvailableCredit() < (userDTO.getEffectCredit() - effectCredit)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATECREDIT_MONTHEFFECTCREDITNOTENOUGH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
    }

    @Override
    public void changePassword(String password) {
        String decryptedPassword = LoginUtil.validatePassword(password, properties.getSecret());
        userRepository.findUser(SecurityUtils.getCurrentUserLogin().get().getUsername()).ifPresent(user -> {
            String encryptedPassword = passwordEncoder.encode(decryptedPassword);
            user.setPassword(encryptedPassword);
            userRepository.saveAndFlush(user);
            log.debug("Changed password for User: {}", user);
        });
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserVM> getAllManagedUsers(UserSearch userSearch) {
        log.info("first day: {}, last day: {}", DateUtil.firstDayOfCurrentMonth(), DateUtil.firstDayOfNextMonth());
        Long tenantId = SecurityUtils.getTenantId();

        CompletableFuture<Page<UserVM>> pageUserFuture = CompletableFuture.supplyAsync(() -> {
            JPAQuery<UserVM> userQuery = userRepository.buildUserQueryForSearch(jpaQueryFactory, userSearch, tenantId);
            List<UserVM> users = userQuery.fetch();
            long totalCount = userQuery.fetchCount();
            return new PageImpl<>(users, userSearch.getPageable(), totalCount);
        }, executor);
        CompletableFuture<List<Long>> userIdFuture = pageUserFuture.thenApply(pageUser -> pageUser.getContent().stream().map(UserVM::getId).toList());

        CompletableFuture<Map<Long, JSONArray>> userRoleMapFuture = userIdFuture.thenApplyAsync(userIds -> {
            if (userIds.isEmpty()) {
                return Map.of();
            }
            return roleRepository.findRolesGroupByUserId(tenantId, userIds).stream()
                    .collect(Collectors.toMap(userRoles -> Long.valueOf(userRoles[0].toString()), userRoles -> JSONArray.parseArray(userRoles[1].toString())));
        }, executor);
        CompletableFuture<Map<Long, JSONArray>> userTeamsMapFuture = userIdFuture.thenApplyAsync(userIds -> {
            if (userIds.isEmpty()) {
                return Map.of();
            }
            return permissionTeamRepository.findTeamsGroupByUserId(tenantId, userIds).stream()
                    .collect(Collectors.toMap(userTeams -> Long.valueOf(userTeams[0].toString()), userTeams -> JSONArray.parseArray(userTeams[1].toString())));
        }, executor);
        CompletableFuture<Map<Long, Integer>> userMonthCreditMapFuture = userIdFuture.thenApplyAsync(userIds -> {
            if (userIds.isEmpty()) {
                return Map.of();
            }
            return userRepository.findUsedMonthlyCredit(tenantId, DateUtil.firstDayOfCurrentMonth(), DateUtil.firstDayOfNextMonth(), userIds).stream()
                    .collect(Collectors.toMap(c -> Long.valueOf(c[0].toString()), c -> Integer.valueOf(c[1].toString())));
        }, executor);
        CompletableFuture<Map<Long, Integer>> bulkCreditMapFuture = userIdFuture.thenApplyAsync(userIds -> {
            if (userIds.isEmpty()) {
                return Map.of();
            }
            return userRepository.findUsedBulkCredit(tenantId, userIds).stream()
                    .collect(Collectors.toMap(c -> Long.valueOf(c[0].toString()), c -> Integer.valueOf(c[1].toString())));
        }, executor);
        CompletableFuture<Map<Long, List<Long>>> bulkLanguagesMapFuture = userIdFuture.thenApplyAsync(userIds -> {
            if (userIds.isEmpty()) {
                return Map.of();
            }
            return userLanguageRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserLanguageRelation::getUserId, Collectors.mapping(UserLanguageRelation::getEnumLanguageId, Collectors.toList())));
        }, executor);
        CompletableFuture.allOf(pageUserFuture, userRoleMapFuture, userTeamsMapFuture, userMonthCreditMapFuture, bulkCreditMapFuture, bulkLanguagesMapFuture)
                .exceptionally(t -> {
                    log.error("Error occurred when fetching user data: ", t);
                    throw new ExternalServiceInterfaceException("Error occurred when fetching user data");
                }).join();

        Page<UserVM> result = pageUserFuture.join();
        Map<Long, JSONArray> userToRoles = userRoleMapFuture.join();
        Map<Long, JSONArray> userToTeams = userTeamsMapFuture.join();
        Map<Long, Integer> monthlyCreditMap = userMonthCreditMapFuture.join();
        Map<Long, Integer> bulkCreditMap = bulkCreditMapFuture.join();
        Map<Long, List<Long>> bulkLanguagesMap = bulkLanguagesMapFuture.join();

        result.forEach(userDTO -> {
            userDTO.setUsedMonthlyCredit(monthlyCreditMap.getOrDefault(userDTO.getId(), null));
            userDTO.setUsedBulkCredit(bulkCreditMap.getOrDefault(userDTO.getId(), null));
            userDTO.setAuthorities(userToRoles.getOrDefault(userDTO.getId(), null));
            userDTO.setTeams(userToTeams.getOrDefault(userDTO.getId(), null));
            userDTO.setLanguages(bulkLanguagesMap.getOrDefault(userDTO.getId(), null));
        });
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserDTO> getAllActivatedUsers(Pageable pageable) {
        return userRepository.findAllByTenantIdAndActivated(SecurityUtils.getTenantId(), true, pageable).map(UserDTO::new);
    }

    /**
     * @param login login could be id in string format, email or login username
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<User> findOneByLogin(String login) {
        if (StringUtils.isNumeric(login)) {
            return Optional.ofNullable(userRepository.findById(Long.valueOf(login)).orElse(null));
        }
        return userRepository.findUser(login);
    }

    @Override
    @Transactional(readOnly = true)
    public User getCurrentUser() {
        return findOneByLogin(SecurityUtils.getCurrentUserLogin().get().getUsername()).orElse(null);
    }

    /**
     * @return a list of all the authorities
     */
    @Override
    public List<Role> getAuthorities() {
        return roleRepository.findAll();
    }

    /**
     * get user full name
     *
     * @param userId user id
     * @return user's full name
     */
    @Override
    public String getUserFullName(Long userId) {
        return userRepository.findUserFullName(userId);
    }

    /**
     * query by user id
     *
     * @param userId user id
     * @return User entity
     */
    @Override
    public User findOne(Long userId) {
        return userRepository.findById(userId).orElse(null);
    }


    /**
     * Limit user registration
     */
    @Override
    public User createLimitUser(ManagedUserVM managedUserVM) {
        if (managedUserVM.getTenantId().equals(1L)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATELIMITUSE_TENANTINCORRECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        String decryptedPassword = LoginUtil.validatePassword(managedUserVM.getPassword(), properties.getSecret());
        User user = new User();
        Set<Role> authorities = new HashSet<>();
        //authorities.add(roleRepository.findById(AuthoritiesConstants.LIMIT_USER).orElseThrow());
        user.setRoles(authorities);
        String encryptedPassword = passwordEncoder.encode(decryptedPassword);
        user.setUsername(managedUserVM.getUsername());
        user.setPassword(encryptedPassword);
        user.setFirstName(managedUserVM.getFirstName());
        user.setLastName(managedUserVM.getFirstName());
        user.setEmail(managedUserVM.getEmail());
        user.setPhone(managedUserVM.getPhone());
        user.setImageUrl(managedUserVM.getImageUrl());
        user.setLangKey(managedUserVM.getLangKey());
        user.setActivated(false);
        Tenant tenant = managementService.queryTenant(managedUserVM.getTenantId()).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_REGISTERACCOUNT_TENANTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        user.setTenantId(managedUserVM.getTenantId());
        user.setNote(managedUserVM.getNote());
        User userCreated = addUser(user);
        // generate uid, this is to generate oauth token
        userCreated.setUid(userCreated.getId() + "," + managedUserVM.getTenantId());
        User result = userRepository.saveAndFlush(userCreated);
        userAccountService.createLimitUserAccount(result.getId(), managedUserVM.getTenantId());
        log.debug("New User Registration: {}", result);
        //analyticsService.identify(result); TODO
        return result;
    }

    @Cacheable(cacheNames = {"BriefUsers"}, key = "'tenant:' + #tenantId", unless = "#result == null")
    @Override
    public List<UserBriefDTO> getAllBriefUsers(Long tenantId) {
        return userRepository.findAllBriefByTenantId(tenantId);
    }

    @Cacheable(cacheNames = {"AllTenantBriefUsers"}, key = "'emails'", unless = "#result == null")
    @Override
    public List<String> getAllValidUserEmails() {
        return userRepository.findAllActiveUserEmail();
    }

    @Cacheable(cacheNames = {"PermissionUsers"}, key = "'tenant:' + #tenantId", unless = "#result == null")
    @Override
    public List<PermissionTeamUserDTO> getAllPermissionUsers(Long tenantId) {
        return userRepository.findUsersWithoutRoleByTenantId(tenantId);
    }

    @Override
    public List<UserWithRolesAndTeamsDTO> getAllUsersWithRolesAndTeams() {
        Map<Long, UserWithRolesAndTeamsDTO> map = new HashMap<>();
        List<IUserWithRolesAndTeamsDTO> users = userRepository.findAllWithRolesAndTeamsByTenantId(SecurityUtils.getTenantId());
        users.forEach(u -> {
            if (map.containsKey(u.getId())) {
                UserWithRolesAndTeamsDTO userWithRolesAndTeamsDTO = map.get(u.getId());
                if (StringUtils.isNotEmpty(u.getRoleName())) {
                    userWithRolesAndTeamsDTO.getAuthorities().add(Map.entry("name", u.getRoleName()));
                }
                if (StringUtils.isNotEmpty(u.getTeamName())) {
                    userWithRolesAndTeamsDTO.getTeams().add(new PermissionTeamSimple(u.getTeamId(), u.getTeamName(), false));
                }
            } else {
                map.put(u.getId(), new UserWithRolesAndTeamsDTO()
                        .setId(u.getId())
                        .setUsername(u.getUsername())
                        .setFirstName(u.getFirstName())
                        .setLastName(u.getLastName())
                        .setEmail(u.getEmail())
                        .setTenantId(u.getTenantId())
                        .setCreatedDate(u.getCreatedDate())
                        .setAuthorities(StringUtils.isEmpty(u.getRoleName()) ? new HashSet<>() : Set.of(Map.entry("name", u.getRoleName())).stream().collect(Collectors.toSet()))
                        .setTeams(StringUtils.isEmpty(u.getTeamName()) ? new HashSet<>() : Set.of(new PermissionTeamSimple(u.getTeamId(), u.getTeamName(), false)).stream().collect(Collectors.toSet())));
            }
        });
        return map.values().stream().collect(Collectors.toList());
    }

    @Override
    public List<UserBriefDTO> getBriefUsersByIds(List<Long> ids) {
        return userRepository.findAllBriefUsers(ids);
    }

    @Override
    public List<UserBriefDTO> getAllBriefUsersByIds(List<Long> ids) {
        return userRepository.findAllBriefUsers(ids);
    }

    @Override
    public List<UserBriefDTO> getBriefUsersByUids(List<String> uids) {
        return userRepository.findBriefUsersByUids(uids);
    }

    @Override
    public List<User> getExistUsers(List<Long> userIds) {
        return userRepository.findALLByIdInAndActivated(userIds, true);
    }

    @Override
    public List<User> getAllUsersByIds(List<Long> userIds) {
        return userRepository.findAllByIdIn(userIds);
    }

    @Override
    public Map<String, UserUidNameDTO> getUidNameMapByUids(List<String> uids) {
        return userRepository.findUidNameUsersByUids(uids).stream().collect(Collectors.toMap(UserUidNameDTO::getUid, Function.identity()));
    }

    @Override
    public Map<Long, UserUidNameDTO> getUidNameMapByIdsOrTenant(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return userRepository.findUidNameUserByTenantId(SecurityUtils.getTenantId()).stream().collect(Collectors.toMap(UserUidNameDTO::getId, Function.identity()));
        }
        return userRepository.findUidNameUserByIds(ids).stream().collect(Collectors.toMap(UserUidNameDTO::getId, Function.identity()));
    }

    @Override
    public Map<String, UserUidNameDTO> getUidNameMapByUidsOrTenant(List<String> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return userRepository.findUidNameUserByTenantId(SecurityUtils.getTenantId()).stream().collect(Collectors.toMap(UserUidNameDTO::getUid, Function.identity()));
        }
        return userRepository.findUidNameUsersByUids(uids).stream().collect(Collectors.toMap(UserUidNameDTO::getUid, Function.identity()));
    }

    @Override
    public List<UserBriefDTO> searchBriefUsers(String name, Long tenantId) {
        return userRepository.searchBriefUsers(name, tenantId);
    }

    @Override
    public Set<String> getUserCountriesByTenantId(Long tenantId) {
        return userRepository.findUserCountry(tenantId);
    }

    @Override
    public List<UserBriefDTO> getAllBriefUsersByUsername(String username) {
        List<User> users = userRepository.findAllByUsernameContainingAndActivated(username, true);
        return ServiceUtils.convert2DTOList(users, UserBriefDTO.class);
    }

    @Override
    public Page<User> findByUserName(BooleanExpression booleanExpression, Pageable pageable) {
        Sort sort;
        if (pageable.getSort() == null) {
            sort = Sort.by(Sort.Direction.DESC, "createdDate");
        } else {
            sort = pageable.getSort();
        }
        PageRequest pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);
        return userRepository.findAll(booleanExpression, pageRequest);
    }

    private UserBriefDTO toUserBriefDTO(User user) {
        UserBriefDTO userBriefDTO = new UserBriefDTO();
        ServiceUtils.myCopyProperties(user, userBriefDTO);
        return userBriefDTO;
    }

    /**
     * job to JobDTO
     *
     * @param users user list
     */
    @Override
    public List<UserBriefDTO> toUserBriefDTOList(List<User> users) {
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyList();
        }
        List<UserBriefDTO> result = new ArrayList<>();
        for (User user : users) {
            result.add(toUserBriefDTO(user));
        }
        return result;
    }

    /**
     * Automatically update tenants/users credit at 00:01:00AMUTC on first day of every month.
     */
//    @Scheduled(cron = "0 1 0 1 * ?", zone = "UTC")
//    @Override
//    @Transactional
//    public void updateTenantAndActiveUserCredit() {
//        LocalDate today = LocalDate.now(ZoneId.of("UTC"));
//        LocalDate lastDayOfCurrentMonth = today.with(TemporalAdjusters.lastDayOfMonth());
//        log.info("[UserAccountService: updateTenantAndActiveUserCredit] automatically update credits for all tenants and active users credit on {}, set expired date to {}", DATE_FORMATTER.format(today), DATE_FORMATTER.format(lastDayOfCurrentMonth));
//
//        try {
//            tenantService.updateTenantAndActiveUserCredit(DATE_FORMATTER.format(lastDayOfCurrentMonth));
//        } catch (Exception e) {
//            log.error("[UserService] the scheduled task failed to update tenant and active userCredit. message: {}", e.getMessage());
//
//        }
//    }

    /**
     * Update the tenant status that is 24 hours overdue at 0:1 every day.
     */
//    @Scheduled(cron = "0 1 0 * * ?", zone = "UTC")
//    @Transactional
//    public void updateTenantStatus() {
//        Instant time = Instant.now().minus(Duration.ofHours(24));
//        List<Tenant> tenantList = tenantRepository.findExpireTenants(time);
//        log.info("[UserService]  automatically update tenant status, time: {}, tenantList: {}", time, tenantList.stream().map(Tenant::getId).collect(Collectors.toList()));
//        tenantList.forEach(tenant -> {
//            try {
//                tenantService.updateTenantStatusToInActive(tenant.getId());
//            } catch (Exception e) {
//                log.error("[UserService] the scheduled task failed to expire the tenant. id: {}, message: {}", tenant.getId(), e.getMessage());
//            }
//        });
//    }

//    @Override
//    @Transactional
//    public void updateTenantAndActiveUserCreditDebugOnly() {
//        LocalDate today = LocalDate.now(ZoneId.of("UTC"));
//        LocalDate lastDayOfCurrentMonth = LocalDate.now(ZoneId.of("UTC")).withDayOfMonth(today.lengthOfMonth());
//        accountRepository.updateActiveUserMonthlyCreditDebugOnly(DATE_FORMATTER.format(lastDayOfCurrentMonth));
//        //update all tenant credit
//        tenantRepository.updateTenantMonthlyCredit();
//    }
    @Override
    @Transactional(readOnly = true)
    public Optional<User> getUserWithAuthoritiesByLogin(String login) {
        return Optional.of(userRepository.findOneWithRolesByUsername(login).orElseThrow(() -> new CustomParameterizedException("The username and password do not match.")));
    }

    @Override
    public User findFirstUserByTenantIdAndAuthorityName(Long tenantId, String authorityName) {
        return userRepository.findFirstUserByTenantIdAndAuthorityName(tenantId, authorityName);
    }

    @Override
    public List<PermissionTeamMemberDTO> findUsersByPermissionTeamId(Long permissionTeamId) {
        return userRepository.findUsersByPermissionTeamId(permissionTeamId);
    }

    @Override
    public List<PermissionTeamMemberDTO> findUsersByPermissionTeamIdIn(Set<Long> permissionTeamIds) {
        return userRepository.findUsersByPermissionTeamIdIn(permissionTeamIds);
    }

    @Override
    public List<PermissionTeamUserDTO> findActiveUsersByPermissionTeamIdIn(Set<Long> permissionTeamIds) {
        return userRepository.findActiveUsersByPrimaryPermissionTeamIdIn(permissionTeamIds);
    }

    @Override
    public List<UserBriefDTO> findUsersNotInTeam(Long teamId) {
        Set<Long> invalidUserIds = userRepository.findUserIdsByPermissionTeamId(teamId);
        return userRepository.findBriefUsersByTenantId(SecurityUtils.getTenantId()).stream().filter(user -> !invalidUserIds.contains(user.getId())).collect(Collectors.toList());
    }

    @Override
    public Set<Long> findAllUserIdByTeamId(Long teamId) {
        return permissionUserTeamRepository.findAllUserIdByTeamId(teamId);
    }

    @Override
    public List<UserBriefDTO> findAllUserIdByIdsOrTenant(Set<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            return userRepository.findAllBriefUsersByIds(ids);
        } else {
            return userRepository.findAllBriefByTenantId(SecurityUtils.getTenantId());
        }
    }

    @Override
    public List<UserBriefDTO> findAllActivatedBriefByTeamId(Long teamId) {
       return userRepository.findAllActivatedBriefByTeamId(teamId);
    }

    @Override
    public List<PermissionTeamMemberDTO> findPlainUsersByPermissionTeamId(Long teamId) {
        return userRepository.findPlainUsersByPermissionTeamId(teamId);
    }

    @Override
    public List<PermissionTeamMemberDTO> findPlainUsersByPermissionTeamIdIn(Set<Long> teamIds) {
        return userRepository.findPlainUsersByPermissionTeamIdIn(teamIds);
    }

    @Override
    public List<PermissionTeamUserDTO> findTeamUsersByPermissionTeamIdIn(Set<Long> teamIds) {
        return userRepository.findTeamUsersByPermissionTeamIdIn(teamIds);
    }

    @Override
    public Set<Long> findTeamUserIdsByPermissionTeamIdIn(Set<Long> teamIds) {
        List<PermissionTeamUserDTO> teamUsers = userRepository.findAllStatusTeamUsersByPermissionTeamIdIn(teamIds);
        Set<Long> userIds = teamUsers.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toSet());
        return userIds;
    }

    @Override
    public Set<Long> findActiveTeamUserIdsByPermissionTeamIdIn(Set<Long> teamIds) {
        List<PermissionTeamUserDTO> teamUsers = userRepository.findAllActiveStatusTeamUsersByPermissionTeamIdIn(teamIds);
        Set<Long> userIds = teamUsers.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toSet());
        return userIds;
    }


    @Override
    public UserDTO getAccount() throws ExecutionException, InterruptedException {

        String redisKey = LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + SecurityUtils.getUserId();
        if (cacheIsOpen) {
            String redisValue = commonRedisService.get(redisKey);
            if (StringUtils.isNotBlank(redisValue)) {
                UserDTO dto = JSONUtil.toBean(redisValue, UserDTO.class);
                return dto;
            }
        }

        Long userId = SecurityUtils.getUserId();
        final Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<User> userCompletableFuture = CompletableFuture.supplyAsync(() -> userRepository.findById(userId).get());
        CompletableFuture<UserAccount> accountCompletableFuture = CompletableFuture.supplyAsync(() -> userAccountService.findByUserId(userId));
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        CompletableFuture<Integer> monthlyUsedCompletableFuture = CompletableFuture.supplyAsync(() -> userRepository.findTotalMonthlyUsedCreditByUserId(userId, CreditType.MONTHLY.toDbValue(), year, month));
        CompletableFuture<Integer> bulkCreditCompletableFuture = CompletableFuture.supplyAsync(() -> userRepository.findTotalUsedCreditByUserId(userId, CreditType.BULK.toDbValue()));
        CompletableFuture<List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer>> privilegesCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return cachePermission.getPrivilegePermissionSetByUserIdFromCacheOrDB(userId);
        });
        //CompletableFuture<PermissionPrivilegeTreeDTO> privilegeTreeCompletableFuture = CompletableFuture.supplyAsync(() -> cachePermissionReadOnly.getPrivilegePermissionTreeByUserIdFromCacheOrDB(userId));
        CompletableFuture<List<Long>> companyIdsCompletableFuture = CompletableFuture.supplyAsync(() -> userRepository.findCompanyIdsByUserIdWithAm(userId));
        CompletableFuture<List<Role>> rolesCompletableFuture = CompletableFuture.supplyAsync(() -> roleRepository.findRolesByUserId(userId));
        CompletableFuture<Set<PermissionUserTeamDTO>> teamVMsCompletableFuture = CompletableFuture.supplyAsync(() -> permissionTeamRepository.findTeamVOsByUserId(userId));
        teamVMsCompletableFuture.get();
        CompletableFuture<TenantConfigDTO> tenantConfigDTOCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return tenantConfigService.findByConfigCode(TenantConfigCode.GENERAL_CONFIG, SecurityUtils.getTenantId());
        });
        User user = userCompletableFuture.get();
        UserAccount account = accountCompletableFuture.get();
        if (account != null) {
            user.setMonthlyCredit(account.getMonthlyAmount());
            user.setBulkCredit(account.getBulkCredit());
        }
        Integer monthlyUsed = monthlyUsedCompletableFuture.get();
        Integer bulk = bulkCreditCompletableFuture.get();
        user.setUsedMonthlyCredit(monthlyUsed);
        user.setUsedBulkCredit(bulk);

        List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> privileges = privilegesCompletableFuture.get();
        //PermissionPrivilegeTreeDTO privilegeTree = privilegeTreeCompletableFuture.get();
        List<Long> companyIds = companyIdsCompletableFuture.get();
        List<Object> authorities = rolesCompletableFuture.get().stream().map(role -> new JSONObject(new HashMap<>() {{
            put("name", role.getName());
        }})).collect(Collectors.toList());
        UserDTO userDTO = new UserDTO(user, authorities, teamVMsCompletableFuture.get(), true);
        userDTO.setPrivileges(Objects.isNull(privileges) ? new ArrayList<>() : privileges);
        userDTO.setJobTitle(user.getJobTitle());
        //userDTO.setPrivilegeTree(privilegeTree);
        userDTO.setCompanyIdsWithAm(companyIds);
        userDTO.setCustomTimezone(user.getCustomTimezone());
        userDTO.setCrmUserId(userDTO.getId());
        TenantConfigDTO tenantConfigDTO = tenantConfigDTOCompletableFuture.get();
        if (tenantConfigDTO != null && StrUtil.isNotBlank(tenantConfigDTO.getConfigValue())) {
            userDTO.setCurrency(getCurrency(tenantConfigDTO.getConfigValue()));
        }

        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(userDTO.getTenantId())) {
            userDTO.getTenant().setIsIpgRuleTenant(true);
        }
        if (cacheIsOpen) {
            commonRedisService.set(redisKey, JSONUtil.toJsonStr(userDTO), userAccountExpireTime.intValue());
        }
        return userDTO;
    }

    private int getCurrency(String configValue) {
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(configValue);
        return Integer.parseInt(fieldToValueMap.getOrDefault(DEFAULT_CURRENCY, "1"));
    }

    /**
     * If the user login failed for 5 times in 24 hours, this account will be locked for 30 minutes.
     *
     * @param username username for login
     * <AUTHOR>
     */
    protected void checkLockedAccount(String username) {
        String lockedUserKey = String.format(RedisConstants.DATA_KEY_LOCKED_USER_ACCOUNT, this.getClass().getSimpleName(), username);
        if (commonRedisService.exists(lockedUserKey)) {
            Long lockedInMinutes = commonRedisService.getTTL(lockedUserKey) / 60 + 1;
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CHECKLOCKEDACCOUNT_LOCKACCOUNT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(lockedInMinutes), userApiPromptProperties.getUserService()));
        }
    }

    private String countLoginFailed(String username) {
        //canal 模拟账号不锁定
        if (whitelist(username)) {
            return "";
        }
        String loginFailedUserKey = String.format(RedisConstants.DATA_KEY_COUNT_LOGIN_FAILED, this.getClass().getSimpleName(), username);
        String message = null;
        if (commonRedisService.exists(loginFailedUserKey) && Long.valueOf(commonRedisService.get(loginFailedUserKey)) < MAXIMUM_LOGIN_FAILED_COUNT) {
            long failedCount = commonRedisService.incr(loginFailedUserKey);
            if (failedCount < MAXIMUM_LOGIN_FAILED_COUNT) {
                message = String.format("You have failed to login for %d times over the past %d hours. You have %d more chances left",
                        failedCount, RedisConstants.EXPIRE_IN_24_HOURS / 3600, MAXIMUM_LOGIN_FAILED_COUNT - failedCount);
            } else {
                String lockedUserKey = String.format(RedisConstants.DATA_KEY_LOCKED_USER_ACCOUNT, this.getClass().getSimpleName(), username);
                commonRedisService.set(lockedUserKey, "1", RedisConstants.EXPIRE_IN_30_MINUTES);
                message = String.format("You have failed to login for %d times over the past %d hours. Your account was locked for %d minutes",
                        MAXIMUM_LOGIN_FAILED_COUNT, RedisConstants.EXPIRE_IN_24_HOURS / 3600, RedisConstants.EXPIRE_IN_30_MINUTES / 60);
            }
        } else {
            commonRedisService.set(loginFailedUserKey, "1", RedisConstants.EXPIRE_IN_24_HOURS);
            message = String.format("You have failed to login for 1 time over the past %d hours. You have %d more chances left",
                    RedisConstants.EXPIRE_IN_24_HOURS / 3600, MAXIMUM_LOGIN_FAILED_COUNT - 1);
        }
        return message;
    }

    @Resource
    private CanalProperties canalProperties;

    private boolean whitelist(String username) {
        //不检查登录失败次数和锁定的账号，目前仅有canal同步账号
        return username.equals(canalProperties.getLoginUser());
    }

    private void removeLoginFailedUser(String username) {
        String loginFailedUserKey = String.format(RedisConstants.DATA_KEY_COUNT_LOGIN_FAILED, this.getClass().getSimpleName(), username);
        commonRedisService.delete(loginFailedUserKey);
    }

    private void saveActiveUser(Long userIdFrom, Long userIdTo, String token, Integer expireInSeconds) {
        String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userIdFrom, userIdTo, token);
        commonRedisService.set(activeUserKey, "1", expireInSeconds);
    }

    @Override
    public List<User> getTenantAdminUsers() {
        return userRepository.findByRoleNameIn(Arrays.asList(AuthoritiesConstants.TENANT_ADMIN));
    }

    @Override
    public User createTenantAdminUser(UserDTO userDTO, String roleName, Long tenantId) {
        if (userDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATETENANTADMINUSER_USERIDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        } else if (userRepository.findOneByUsername(userDTO.getUsername().toLowerCase()).isPresent()) {
            throw new LoginAlreadyUsedException();
        } else if (userRepository.findOneByEmailIgnoreCase(userDTO.getEmail()).isPresent()) {
            throw new EmailAlreadyUsedException();
        }
        Tenant tenant = managementService.queryTenant(tenantId).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATETENANTADMINUSER_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        User user = new User();
        user.setTenantId(tenantId);
        user.setUsername(userDTO.getUsername().toLowerCase());
        user.setFirstName(userDTO.getFirstName());
        user.setLastName(userDTO.getLastName());
        if (userDTO.getEmail() != null) {
            user.setEmail(userDTO.getEmail().toLowerCase());
        }
        user.setImageUrl(userDTO.getImageUrl());

        String randomPassword = RandomUtil.generatePassword();
        String encryptedPassword = passwordEncoder.encode(randomPassword);
        user.setPassword(encryptedPassword);
        user.setResetKey(RandomUtil.generatePassword());
        user.setResetDate(Instant.now());
        user.setActivated(true);
        user.setRoles(roleRepository.findByName(roleName));

        User newUser = userRepository.saveAndFlush(user);

        newUser.setUid(newUser.getId() + "," + tenantId);
        userRepository.save(newUser);
        newUser.setTenant(tenant);
        log.debug("Created Information for UserAdmin: {}", user);
        return newUser;
    }

    @Override
    public CredentialDTO refreshToken(RefreshTokenVM refreshTokenVM) {
        final CredentialDTO credentialDTO = authorityService.refreshToken(refreshTokenVM).getBody();
//        this.saveActiveUser(credentialDTO.getAccess_token());
        return credentialDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, UserStatusDTO userStatusDTO) {
        User existUser = userRepository.findById(userId).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSERSTATUS_USERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService())));
        Tenant existTenant = managementService.queryTenant(existUser.getTenantId()).getBody();
        if (existTenant == null || existTenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSERSTATUS_INVALIDTENANTUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }

        if (existTenant.getStatus() == 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSERSTATUS_NOTSETSTATUS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }

        UserAccount userAccount = refreshAccount(existUser);
        if (userAccount != null) {
            existUser.setMonthlyCredit(userAccount.getMonthlyAmount());
            existUser.setBulkCredit(userAccount.getBulkCredit());
        }
        // inactive, 离职流程
        if (userStatusDTO.getActivated() != null && !Objects.equals(existUser.isActivated(), userStatusDTO.getActivated())) {
            if (userStatusDTO.getActivated()) {
                //激活
                existUser.setCancellationTime(null);
                existUser.setActivated(true);
            } else {
                existUser.setActivated(false);
                //非激活
                existUser.setCancellationTime(Instant.now());
                Long newTalentOwner = permissionUserTeamService.getSuperiorLeader(userId);
                talentService.transferOwnership(userId, newTalentOwner);
                talentService.handoverConfidentialTalent(userId, newTalentOwner);
            }
        }
        userRepository.save(existUser);
    }

    @Override
    public Page<UserPageVO> searchUserList(Long tenantId, Pageable pageable) {
        Tenant tenant = managementService.queryTenant(tenantId).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_SEARCHUSERLIST_TENANTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        CompletableFuture<Page<UserPageVO>> pageUserFuture = CompletableFuture.supplyAsync(() -> userRepository.findAllByTenantId(tenantId, pageable));
        CompletableFuture<List<Long>> userIdFuture = pageUserFuture.thenApply(pageUser -> pageUser.getContent().stream().map(UserPageVO::getId).toList());

        CompletableFuture<Map<Long, Integer>> userMonthCreditMapFuture = userIdFuture.thenApplyAsync(userIds -> {
            if (userIds.isEmpty()) {
                return Map.of();
            }
            return userRepository.findUsedMonthlyCredit(tenantId, DateUtil.firstDayOfCurrentMonth(), DateUtil.firstDayOfNextMonth(), userIds).stream()
                    .collect(Collectors.toMap(c -> Long.valueOf(c[0].toString()), c -> Integer.valueOf(c[1].toString())));
        }, executor);
        CompletableFuture<Map<Long, Integer>> bulkCreditMapFuture = userIdFuture.thenApplyAsync(userIds -> {
            if (userIds.isEmpty()) {
                return Map.of();
            }
            return userRepository.findUsedBulkCredit(tenantId, userIds).stream()
                    .collect(Collectors.toMap(c -> Long.valueOf(c[0].toString()), c -> Integer.valueOf(c[1].toString())));
        }, executor);
        CompletableFuture.allOf(pageUserFuture, userMonthCreditMapFuture, bulkCreditMapFuture)
                .exceptionally(t -> {
                    log.error("Error occurred when fetching user data: ", t);
                    throw new ExternalServiceInterfaceException("Error occurred when fetching user data");
                }).join();

        Page<UserPageVO> userPage = pageUserFuture.join();
        Map<Long, Integer> monthlyCreditMap = userMonthCreditMapFuture.join();
        Map<Long, Integer> bulkCreditMap = bulkCreditMapFuture.join();

        userPage.forEach(userDTO -> {
            if (userDTO.getMonthlyCredit() != null) {
                userDTO.setAvailableMonthlyCredit(userDTO.getActivated().equals(Boolean.FALSE) ? userDTO.getMonthlyCredit() : Math.max(userDTO.getMonthlyCredit() - monthlyCreditMap.getOrDefault(userDTO.getId(), 0), 0));
            }
            if (userDTO.getBulkCredit() != null) {
                userDTO.setAvailableBulkCredit(userDTO.getActivated().equals(Boolean.FALSE) ? userDTO.getBulkCredit() : Math.max(userDTO.getBulkCredit() - bulkCreditMap.getOrDefault(userDTO.getId(), 0), 0));
            }
        });
        return userPage;
    }

    private UserAccount refreshAccount(User existUser) {

        UserAccount userAccount = userAccountService.findByUserId(existUser.getId());
        if (userAccount == null) {
            return null;
        }
        boolean accountChanged = false;

        if (BooleanUtils.isFalse(existUser.isActivated())) {
            userAccount.setBulkCredit(0);
            userAccount.setMonthlyAmount(0);
            userAccount.setEffectCredit(0);
            accountChanged = true;
        }

        if (Objects.isNull(userAccount.getCreditEffectType())) {
            userAccount.setCreditEffectType(CreditEffectType.INSTANT);
            accountChanged = true;
        }

        if (accountChanged) {
            if (Objects.isNull(userAccount.getVersion())) {
                userAccount.setVersion(0);
            }
            if (userAccount.getCreditEffectType() != CreditEffectType.NEXT_MONTH) {
                userAccount.setEffectCredit(null);
            }
            userAccountService.updateById(userAccount.getId(), userAccount.getMonthlyAmount(), userAccount.getBulkCredit(), userAccount.getEffectCredit(), userAccount.getCreditEffectType(), userAccount.getVersion());
        }
        return userAccount;
    }

    @Override
    public void updateTimezoneByUserId(Long userId, UserTimezoneDTO userTimezoneDTO) {
        List<UserTimeZoneVO> userTimeZoneVOList = userRepository.findUserTimeZoneVOByIds(CollUtil.newArrayList(userId));
        if (CollUtil.isEmpty(userTimeZoneVOList)) {
            return;
        }
        UserTimeZoneVO userTimeZoneVO = userTimeZoneVOList.get(0);
        if (!Objects.equals(userTimezoneDTO.getTimezone(), userTimeZoneVO.getCustomTimezone())) {
            //第一次设置或者发生变化
            xxlJobService.updateXxlJobByTimezone(new XxlJobUpdateByTimezoneDTO(userTimeZoneVO.getCustomTimezone(), userTimezoneDTO.getTimezone(), userId));
        }
        userRepository.updateTimezoneByUserId(userId, userTimezoneDTO.getTimezone());
    }

    @Override
    public UserTimeZoneVO getTimezoneByUserId(Long userId) {
        List<UserTimeZoneVO> userTimeZoneVO = userRepository.findUserTimeZoneVOByIds(CollUtil.newArrayList(userId));
        if (CollUtil.isEmpty(userTimeZoneVO)) {
            log.error("user is not found, id = {}", userId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSERSTATUS_USERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        return userTimeZoneVO.get(0);
    }

    @Override
    public List<UserTimeZoneVO> getTimezoneListByUserIdList(List<Long> ids) {
        return userRepository.findUserTimeZoneVOByIds(ids);
    }

    @Override
    public List<RelateJobFolderUserInfo> getRelateJobFolderUsers(Long jobId) {
        Optional<JobV3> jobV3 = baseJobRepository.findById(jobId);
        if (jobV3.isEmpty()) {
            throw new CustomParameterizedException("job not exist!");
        }
        JobV3 job = jobV3.get();
        this.fillDataForPrivateJob(job);
        //查询当前租户下排除自己的用户信息(主团队dataScope)
        List<UserInfoWithPermission> userInfoWithPermissionByTenantId = userRepository.findUserInfoWithPermissionByTenantId(SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        //按userid分，List<UserInfoWithPermission> 有is_primary = true/false的
        Map<Long, List<UserInfoWithPermission>> id2UserInfoWithPermissionMap = userInfoWithPermissionByTenantId.stream().collect(Collectors.groupingBy(UserInfoWithPermission::getId));
        List<Long> userIdList = new ArrayList<>(id2UserInfoWithPermissionMap.keySet());
        //拿到role相关dataScope和extraTeamId
        List<UserRoleWithPermission> userRoleWithPermissionByTenantId = userRepository.findUserRoleWithPermissionByTenantId(SecurityUtils.getTenantId());
        Map<Long, List<UserRoleWithPermission>> userId2DataScopeMap = userRoleWithPermissionByTenantId.stream().collect(Collectors.groupingBy(UserRoleWithPermission::getUserId));
        List<Long> roleIdList = userRoleWithPermissionByTenantId.stream().map(UserRoleWithPermission::getRoleId).collect(Collectors.toList());
        Map<Long, List<PermissionExtraRoleTeam>> roleId2ExtraTeamIdMap = permissionExtraRoleTeamRepository.findAllByRoleIdIn(roleIdList).stream().collect(Collectors.groupingBy(PermissionExtraRoleTeam::getRoleId));
        //拿到user相关的extraTeamId
        Map<Long, List<PermissionExtraUserTeam>> userClassification = permissionExtraUserTeamRepository.findAllByUserIdIn(userIdList).stream().collect(Collectors.groupingBy(PermissionExtraUserTeam::getUserId));
        List<RelateJobFolderUserInfo> relateJobFoldersUsersByUserId = getRelateJobFoldersUsersByUserId(userIdList, id2UserInfoWithPermissionMap, job, getOtherProjectUser(jobId), userClassification, userId2DataScopeMap, roleId2ExtraTeamIdMap);
        return relateJobFoldersUsersByUserId.stream()
                .sorted(Comparator.comparing(RelateJobFolderUserInfo::getFullName))
                .collect(Collectors.toList());
    }

    private void fillDataForPrivateJob(JobV3 job) {
        List<Long> privateJobTeamIds = baseJobRepository.checkPrivateJob(job.getPermissionTeamId());
        job.setIsPrivateJob(CollectionUtils.isNotEmpty(privateJobTeamIds));
        Set<Long> authorizedUserIdsForPrivateJob = job.getIsPrivateJob() ? baseJobRepository.findAuthorizedUserIdsByJobId(job.getId()) : new HashSet<>();
        job.setAuthorizedUsersForPrivateJob(authorizedUserIdsForPrivateJob);
    }

    private Set<Long> getOtherProjectUser(Long jobId) {
        TalentAssociationJobFolder confirmFolder = talentRelateJobFolderRepository.getTalentRelateJobFolderByJobIdIsAndUserIdIsAndStatusIs(jobId, SecurityUtils.getUserId(), RelateJobFolderStatus.CONFIRM);
        List<TalentAssociationJobFolder> otherProject = confirmFolder != null ? talentRelateJobFolderRepository.getTalentRelateJobFoldersByJobIdIsAndFolderIdIsNot(jobId, confirmFolder.getFolderId()) : talentRelateJobFolderRepository.getTalentRelateJobFoldersByJobIdIs(jobId);
        return otherProject.stream().map(TalentAssociationJobFolder::getUserId).collect(Collectors.toSet());
    }

    private List<RelateJobFolderUserInfo> getRelateJobFoldersUsersByUserId(List<Long> userIdList, Map<Long, List<UserInfoWithPermission>> id2UserInfoWithPermissionMap, JobV3 job, Set<Long> otherProjectUser, Map<Long, List<PermissionExtraUserTeam>> userClassification, Map<Long, List<UserRoleWithPermission>> userRoleMap, Map<Long, List<PermissionExtraRoleTeam>> roleId2ExtraTeamIdMap) {
        return userIdList.stream().map(id -> {
            UserInfoWithPermission primaryUserInfo = getPrimaryUserInfoWithPermission(id2UserInfoWithPermissionMap.get(id));
            RelateJobFolderUserInfo relateJobFolderUserInfo = new RelateJobFolderUserInfo();
            relateJobFolderUserInfo.setId(primaryUserInfo.getId());
            relateJobFolderUserInfo.setFullName(CommonUtils.formatFullNameWithBlankCheck(primaryUserInfo.getFirstName(), primaryUserInfo.getLastName()));
            Integer dataScope = Objects.isNull(primaryUserInfo.getDataScope()) ? DataScope.PERMISSION_SELF.toDbValue() : primaryUserInfo.getDataScope();
            List<UserRoleWithPermission> userRoleWithPermissions = userRoleMap.get(primaryUserInfo.getId());
            Set<Long> extraTeamIds = new HashSet<>();
            List<UserInfoWithPermission> secondUserInfoWithPermission = getSecondUserInfoWithPermission(id2UserInfoWithPermissionMap.get(id));
            primaryUserInfo.setDataScope(getMaxDataScope(secondUserInfoWithPermission, dataScope, userRoleWithPermissions, extraTeamIds, userClassification.get(primaryUserInfo.getId()), roleId2ExtraTeamIdMap));
            relateJobFolderUserInfo.setType(getFolderUserType(job, primaryUserInfo, extraTeamIds, otherProjectUser));
            return relateJobFolderUserInfo;
        }).collect(Collectors.toList());
    }

    private List<UserInfoWithPermission> getSecondUserInfoWithPermission(List<UserInfoWithPermission> userInfoWithPermissions) {
        List<UserInfoWithPermission> ret = new ArrayList<>();
        for (UserInfoWithPermission userInfo : userInfoWithPermissions) {
            Boolean userInfoIsPrimary = userInfo.getIsPrimary();
            if (Boolean.FALSE.equals(userInfoIsPrimary)) {
                ret.add(userInfo);
            }
        }
        return ret;
    }

    private UserInfoWithPermission getPrimaryUserInfoWithPermission(List<UserInfoWithPermission> userInfoWithPermissions) {
        //没有主团队只有副团队的返回用户基本信息即可
        UserInfoWithPermission ret = null;
        for (UserInfoWithPermission userInfo : userInfoWithPermissions) {
            Boolean userInfoIsPrimary = userInfo.getIsPrimary();
            //有没有team的user，他的userInfoIsPrimary为null
            if (userInfoIsPrimary == null) {
                return userInfo;
            }
            if (Boolean.TRUE.equals(userInfoIsPrimary)) {
                return userInfo;
            } else {
                ret = userInfo;
            }
        }
        return ret;
    }

    public static Integer getMaxDataScope(List<UserInfoWithPermission> secondUserInfoWithPermission, Integer dataScope, List<UserRoleWithPermission> userRoleWithPermissions, Set<Long> extraTeamIds, List<PermissionExtraUserTeam> permissionExtraUserTeams, Map<Long, List<PermissionExtraRoleTeam>> roleId2ExtraTeamIdMap) {
        if (userRoleWithPermissions == null) {
            userRoleWithPermissions = new ArrayList<>();
        }
        Set<Long> secondTeamId = secondUserInfoWithPermission.stream().map(UserInfoWithPermission::getTeamId).collect(Collectors.toSet());
        //处理用户角色上的dataScope以及extraTeamId
        Integer maxDataScope = dataScope;
        for (UserRoleWithPermission userRoleWithPermission : userRoleWithPermissions) {
            Integer roleDataScope = userRoleWithPermission.getDataScope();
            if (DataScope.PERMISSION_EXTRA_TEAM.toDbValue().equals(roleDataScope)) {
                List<PermissionExtraRoleTeam> permissionExtraRoleTeams = roleId2ExtraTeamIdMap.get(userRoleWithPermission.getRoleId());
                if (permissionExtraRoleTeams != null) {
                    extraTeamIds.addAll(permissionExtraRoleTeams.stream().map(PermissionExtraRoleTeam::getTeamId).collect(Collectors.toSet()));
                }
            }
            if (roleDataScope > maxDataScope) {
                maxDataScope = roleDataScope;
            }
        }
        if (maxDataScope > DataScope.PERMISSION_SELF.toDbValue()) {
            //添加用户辅团队
            extraTeamIds.addAll(secondTeamId);
        }
        //如果dataScope是PERMISSION_EXTRA_TEAM，添加ExtraUserTeams
        if (maxDataScope > DataScope.PERMISSION_TEAM.toDbValue() && permissionExtraUserTeams != null) {
            extraTeamIds.addAll(permissionExtraUserTeams.stream().map(PermissionExtraUserTeam::getTeamId).collect(Collectors.toSet()));
        }

        return maxDataScope;
    }


    @Override
    public List<RelateJobFolderTeamTreeDTO> getRelateJobFolderTreeUsers(Long jobId) {
        Optional<JobV3> jobV3 = baseJobRepository.findById(jobId);
        if (jobV3.isEmpty()) {
            throw new CustomParameterizedException("job not exist!");
        }
        JobV3 job = jobV3.get();
        this.fillDataForPrivateJob(job);
        List<PermissionTeamTreeDTO> plainTeamTree = permissionTeamService.getPlainTeamTree();
        Set<Long> otherProjectUser = getOtherProjectUser(job.getId());
        Set<Long> teamIdList = new HashSet<>();
        getPlainTeamTreeTeamIdList(plainTeamTree, teamIdList);
        //userid，用户角色map
        List<UserRoleWithPermission> userRoleWithPermissionByTenantId = userRepository.findUserRoleWithPermissionByTenantId(SecurityUtils.getTenantId());
        Map<Long, List<UserRoleWithPermission>> userId2UserRoleMap = userRoleWithPermissionByTenantId.stream().collect(Collectors.groupingBy(UserRoleWithPermission::getUserId));
        //用户角色对应的相关extraTeam
        List<Long> roleIdList = userRoleWithPermissionByTenantId.stream().map(UserRoleWithPermission::getRoleId).collect(Collectors.toList());
        Map<Long, List<PermissionExtraRoleTeam>> roleId2ExtraTeamIdMap = permissionExtraRoleTeamRepository.findAllByRoleIdIn(roleIdList).stream().collect(Collectors.groupingBy(PermissionExtraRoleTeam::getRoleId));
        //根据团队组成user相关信息
        Map<Long, RelateJobFolderUserAllInfo> userInfoGroupByTeamId = getRelateJobFolderUserAllInfoGroupByTeam(teamIdList);
        return plainTeamTree.stream().map(p -> {
            return recursionAssemble(p, job, otherProjectUser, userInfoGroupByTeamId, userId2UserRoleMap, roleId2ExtraTeamIdMap);
        }).collect(Collectors.toList());
    }

    @Override
    public List<RelateJobFolderTeamTreeDTO> getTeamTreeUsers() {
        String redisKey = CommonUtils.TEAM_USER_TREE_TENANT + SecurityUtils.getUserId();
        if (cacheIsOpen) {
            String redisValue = commonRedisService.get(redisKey);
            if (StringUtils.isNotBlank(redisValue)) {
                cn.hutool.json.JSONArray array = JSONUtil.parseArray(redisValue);
                List<RelateJobFolderTeamTreeDTO> dto = JSONUtil.toList(array, RelateJobFolderTeamTreeDTO.class);
                return dto;
            }
        }

        List<PermissionTeamTreeDTO> plainTeamTree = permissionTeamService.getPlainTeamTree();
        Set<Long> teamIdList = new HashSet<>();
        getPlainTeamTreeTeamIdList(plainTeamTree, teamIdList);
        //根据团队组成user相关信息
        Map<Long, RelateJobFolderUserAllInfo> userInfoGroupByTeamId = getAllInfoGroupByTeam(teamIdList);
        List<RelateJobFolderTeamTreeDTO> result = plainTeamTree.stream().map(p -> {
            return recursionAssembleSimple(p, userInfoGroupByTeamId);
        }).collect(Collectors.toList());

        if (cacheIsOpen) {
            cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(result);
            commonRedisService.set(redisKey, jsonArray.toString(), userAccountExpireTime.intValue());
        }
        return result;
    }

    @Override
    public List<RelateJobFolderTeamTreeDTO> getTeamTreeUsersWithPermission() {
        TeamDataPermissionRespDTO teamDataPermissionRespDTO = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if (teamDataPermissionRespDTO.getSelf()) {
            RelateJobFolderTeamTreeDTO dto = new RelateJobFolderTeamTreeDTO();
            dto.setId(SecurityUtils.getUserId());
            dto.setName(SecurityUtils.getFullName());
            return Arrays.asList(dto);
        } else if (teamDataPermissionRespDTO.getAll() || SecurityUtils.isAdmin()) {
            return getTeamTreeUsers();
        }

        try {
            //查询用户
            Set<Long> teamIds = teamDataPermissionRespDTO.getNestedTeamIds();
            List<PermissionTeamTreeDTO> teamTreeDTOS = permissionTeamService.getTeamTreeWithPermissionByType(Module.REPORT);
            Map<Long, RelateJobFolderUserAllInfo> userInfoGroupByTeamId = getAllInfoGroupByTeam(teamIds);
            return teamTreeDTOS.stream().map(p -> {
                return recursionAssembleSimple(p, userInfoGroupByTeamId);
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("error,{}", e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional
    public void updateBYSsoUserInfo(SsoUserInfo ssoUserInfo) {
        userRepository.findUser(ssoUserInfo.getEmail()).ifPresent(existUser -> {
            String oldTimeZone = existUser.getCustomTimezone();
            Long oldTenantId = existUser.getTenantId();
            fillWithSsoUser(ssoUserInfo, existUser);
            userRepository.save(existUser);
            if (!Objects.equals(oldTenantId,ssoUserInfo.getTenantId())){
                String oldCreatedBy = ssoUserInfo.getUserId() + "," + oldTenantId;
                companyService.deleteCustomFolderConnectClient(oldCreatedBy);
            }
            if (!Objects.equals(ssoUserInfo.getTimezone(), oldTimeZone)) {
                //第一次设置或者发生变化
                xxlJobService.updateXxlJobByTimezone(new XxlJobUpdateByTimezoneDTO(oldTimeZone, ssoUserInfo.getTimezone(), existUser.getId()));
            }
            cacheUser.deleteBriefUsersByTenantId(existUser.getTenantId());
            commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + existUser.getId());
        });
    }

    @Override
    @Transactional
    public void updateUserActiveBySso(SsoUserActive userActive) {
        userRepository.findOneByEmail(userActive.getEmail()).ifPresent(existUser -> {
            UserStatusDTO userStatusDTO = new UserStatusDTO();
            userStatusDTO.setActivated(userActive.getActive());
            updateUserStatus(existUser.getId(), userStatusDTO);
            cacheUser.deleteBriefUsersByTenantId(existUser.getTenantId());
            cacheUser.deletePermissionUsersByTenantId(existUser.getTenantId());
            commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + existUser.getId());
        });
    }

    @Override
    @Transactional
    public User onSsoUserBinding(SsoUserBinding ssoUserBinding) {
        Optional<User> userOpt = userRepository.findOneByEmail(ssoUserBinding.getEmail());
        // 用户存在
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            // 用户存在，根据绑定、取消绑定更新用户 active
            UserStatusDTO userStatusDTO = new UserStatusDTO();
            userStatusDTO.setActivated(ssoUserBinding.getCreation());
            updateUserStatus(user.getId(), userStatusDTO);
            cacheUser.deleteBriefUsersByTenantId(user.getTenantId());
            cacheUser.deletePermissionUsersByTenantId(user.getTenantId());
            commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + user.getId());
            return user;
        } else {
            if (ssoUserBinding.getCreation()) {
                // 用户不存在并且是绑定，创建用户
                UserDTO userDTO = new UserDTO();
                userDTO.setId(ssoUserBinding.getUserId());
                userDTO.setUsername(ssoUserBinding.getUsername());
                userDTO.setEmail(ssoUserBinding.getEmail());
                userDTO.setFirstName(ssoUserBinding.getFirstName());
                userDTO.setLastName(ssoUserBinding.getLastName());
                userDTO.setImageUrl(ssoUserBinding.getImageUrl());
                userDTO.setPhone(ssoUserBinding.getPhone());
                userDTO.setTenantId(ssoUserBinding.getTenantId());
                userDTO.setCustomTimezone(ssoUserBinding.getTimezone());
                User user = createUser(userDTO);
                cacheUser.deleteBriefUsersByTenantId(ssoUserBinding.getTenantId());
                cacheUser.deletePermissionUsersByTenantId(ssoUserBinding.getTenantId());
                return user;
            }
        }
        return null;
    }

    public void fillWithSsoUser(SsoUserInfo ssoUserInfo, User user) {
        user.setUsername(ssoUserInfo.getUsername());
        user.setFirstName(ssoUserInfo.getFirstName());
        user.setLastName(ssoUserInfo.getLastName());
        user.setPhone(ssoUserInfo.getPhone());
        user.setTenantId(ssoUserInfo.getTenantId());
        user.setUid(user.getId() + "," + ssoUserInfo.getTenantId());
        user.setCustomTimezone(ssoUserInfo.getTimezone());
        user.setImageUrl(ssoUserInfo.getImageUrl());
    }


    private RelateJobFolderTeamTreeDTO recursionAssembleSimple(PermissionTeamTreeDTO p, Map<Long, RelateJobFolderUserAllInfo> userInfoGroupByTeamId) {
        RelateJobFolderTeamTreeDTO relateJobFolderTeamTreeDTO = new RelateJobFolderTeamTreeDTO();
        BeanUtil.copyProperties(p, relateJobFolderTeamTreeDTO);
        List<PermissionTeamTreeDTO> children = p.getChildren();
        if (children != null) {
            List<RelateJobFolderTeamTreeDTO> childrenRelateJobFolderTeamTreeDTO = new ArrayList<>();
            for (PermissionTeamTreeDTO c : children) {
                childrenRelateJobFolderTeamTreeDTO.add(recursionAssembleSimple(c, userInfoGroupByTeamId));
            }
            relateJobFolderTeamTreeDTO.setChildren(childrenRelateJobFolderTeamTreeDTO);
            List<RelateJobFolderUserInfo> data = getUserDataByTeamId(relateJobFolderTeamTreeDTO, userInfoGroupByTeamId);
            if (!data.isEmpty()) {
                relateJobFolderTeamTreeDTO.setData(data);
            }
        } else {
            List<RelateJobFolderUserInfo> data = getUserDataByTeamId(relateJobFolderTeamTreeDTO, userInfoGroupByTeamId);
            if (!data.isEmpty()) {
                relateJobFolderTeamTreeDTO.setData(data);
            }
        }

        return relateJobFolderTeamTreeDTO;
    }

    private List<RelateJobFolderUserInfo> getUserDataByTeamId(RelateJobFolderTeamTreeDTO relateJobFolderTeamTreeDTO, Map<Long, RelateJobFolderUserAllInfo> userInfoGroupByTeamId) {
        RelateJobFolderUserAllInfo relateJobFolderUserAllInfos = userInfoGroupByTeamId.get(relateJobFolderTeamTreeDTO.getId());
        if (relateJobFolderUserAllInfos == null) {
            return new ArrayList<>();
        }
        List<UserInfoWithPermission> userInfoWithPermissionList = relateJobFolderUserAllInfos.getUserInfoWithPermissionList();
        return userInfoWithPermissionList.stream().map(f -> {
            RelateJobFolderUserInfo userInfo = new RelateJobFolderUserInfo();
            userInfo.setId(f.getId());
            userInfo.setFullName(CommonUtils.formatFullNameWithBlankCheck(f.getFirstName(), f.getLastName()));
            userInfo.setActivated(f.getActivated());
            return userInfo;
        }).collect(Collectors.toList());
    }

    private Map<Long, RelateJobFolderUserAllInfo> getAllInfoGroupByTeam(Set<Long> teamIdList) {
        Map<Long, RelateJobFolderUserAllInfo> ret = new HashMap<>();
        List<PermissionTeamUserDTO> activeUsersByPermissionTeamIdIn = userRepository.findUsersWithoutRoleByPermissionTeamIdIn(teamIdList);
        List<Long> userIds = activeUsersByPermissionTeamIdIn.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toList());
        List<UserInfoWithPermission> userInfoWithPermissionByUserIdIn = userRepository.findUserInfoWithPermissionByUserIdIn(userIds);
        Map<Long, List<UserInfoWithPermission>> userInfoMap = userInfoWithPermissionByUserIdIn.stream().filter(UserInfoWithPermission::getIsPrimary).collect(Collectors.groupingBy(UserInfoWithPermission::getId));
        for (PermissionTeamUserDTO teamMemberDTO : activeUsersByPermissionTeamIdIn) {
            if (Boolean.FALSE.equals(teamMemberDTO.getIsPrimaryTeam())) {
                continue;
            }
            Long teamId = teamMemberDTO.getTeamId();
            Long userId = teamMemberDTO.getId();
            RelateJobFolderUserAllInfo relateJobFolderUserAllInfo = ret.get(teamId);
            if (relateJobFolderUserAllInfo == null) {
                relateJobFolderUserAllInfo = new RelateJobFolderUserAllInfo(teamId);
            }
            relateJobFolderUserAllInfo.addUserInfoWithPermissionList(userInfoMap.get(userId));
            ret.put(teamId, relateJobFolderUserAllInfo);
        }
        return ret;
    }

    private void getPlainTeamTreeTeamIdList(List<PermissionTeamTreeDTO> plainTeamTree, Set<Long> teamIdList) {
        for (PermissionTeamTreeDTO plainTeam : plainTeamTree) {
            teamIdList.add(plainTeam.getId());
            List<PermissionTeamTreeDTO> children = plainTeam.getChildren();
            if (children != null) {
                getPlainTeamTreeTeamIdList(children, teamIdList);
            }
        }
    }

    private Map<Long, RelateJobFolderUserAllInfo> getRelateJobFolderUserAllInfoGroupByTeam(Set<Long> teamIdList) {
        Map<Long, RelateJobFolderUserAllInfo> ret = new HashMap<>();
        List<PermissionTeamUserDTO> activeUsersByPermissionTeamIdIn = userRepository.findActiveUsersByPermissionTeamIdIn(teamIdList, true);
        List<Long> userIds = activeUsersByPermissionTeamIdIn.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toList());
        List<UserInfoWithPermission> userInfoWithPermissionByUserIdIn = userRepository.findUserInfoWithPermissionByUserIdIn(userIds, true);
        Map<Long, List<UserInfoWithPermission>> userInfoMap = userInfoWithPermissionByUserIdIn.stream().collect(Collectors.groupingBy(UserInfoWithPermission::getId));
        List<Long> userIdList = userInfoWithPermissionByUserIdIn.stream().map(UserInfoWithPermission::getId).collect(Collectors.toList());
        Map<Long, List<PermissionExtraUserTeam>> userClassification = permissionExtraUserTeamRepository.findAllByUserIdIn(userIdList).stream().collect(Collectors.groupingBy(PermissionExtraUserTeam::getUserId));
        for (PermissionTeamUserDTO teamMemberDTO : activeUsersByPermissionTeamIdIn) {
            if (Boolean.FALSE.equals(teamMemberDTO.getIsPrimaryTeam())) {
                continue;
            }
            Long teamId = teamMemberDTO.getTeamId();
            Long userId = teamMemberDTO.getId();
            RelateJobFolderUserAllInfo relateJobFolderUserAllInfo = ret.get(teamId);
            if (relateJobFolderUserAllInfo == null) {
                relateJobFolderUserAllInfo = new RelateJobFolderUserAllInfo(teamId);
            }
            relateJobFolderUserAllInfo.addUserInfoWithPermissionList(userInfoMap.get(userId));
            relateJobFolderUserAllInfo.addPermissionExtraUserTeamListMap(userId, userClassification.get(userId));
            ret.put(teamId, relateJobFolderUserAllInfo);
        }
        return ret;
    }

    private RelateJobFolderTeamTreeDTO recursionAssemble(PermissionTeamTreeDTO p, JobV3 job, Set<Long> otherProjectUser, Map<Long, RelateJobFolderUserAllInfo> userInfoGroupByTeamId, Map<Long, List<UserRoleWithPermission>> userRoleMap, Map<Long, List<PermissionExtraRoleTeam>> roleId2ExtraTeamIdMap) {
        RelateJobFolderTeamTreeDTO relateJobFolderTeamTreeDTO = new RelateJobFolderTeamTreeDTO();
        BeanUtil.copyProperties(p, relateJobFolderTeamTreeDTO);
        List<PermissionTeamTreeDTO> children = p.getChildren();
        if (children != null) {
            List<RelateJobFolderTeamTreeDTO> childrenRelateJobFolderTeamTreeDTO = new ArrayList<>();
            for (PermissionTeamTreeDTO c : children) {
                childrenRelateJobFolderTeamTreeDTO.add(recursionAssemble(c, job, otherProjectUser, userInfoGroupByTeamId, userRoleMap, roleId2ExtraTeamIdMap));
            }
            relateJobFolderTeamTreeDTO.setChildren(childrenRelateJobFolderTeamTreeDTO);
            List<RelateJobFolderUserInfo> data = getRelateJobFolderUserDataByTeamId(relateJobFolderTeamTreeDTO, job, otherProjectUser, userInfoGroupByTeamId, userRoleMap, roleId2ExtraTeamIdMap);
            if (!data.isEmpty()) {
                relateJobFolderTeamTreeDTO.setData(data);
            }
        } else {
            List<RelateJobFolderUserInfo> data = getRelateJobFolderUserDataByTeamId(relateJobFolderTeamTreeDTO, job, otherProjectUser, userInfoGroupByTeamId, userRoleMap, roleId2ExtraTeamIdMap);
            if (!data.isEmpty()) {
                relateJobFolderTeamTreeDTO.setData(data);
            }
        }

        return relateJobFolderTeamTreeDTO;
    }

    private List<RelateJobFolderUserInfo> getRelateJobFolderUserDataByTeamId(RelateJobFolderTeamTreeDTO relateJobFolderTeamTreeDTO, JobV3 job, Set<Long> otherProjectUser, Map<Long, RelateJobFolderUserAllInfo> userInfoGroupByTeamId, Map<Long, List<UserRoleWithPermission>> userRoleMap, Map<Long, List<PermissionExtraRoleTeam>> roleId2ExtraTeamIdMap) {
        RelateJobFolderUserAllInfo relateJobFolderUserAllInfos = userInfoGroupByTeamId.get(relateJobFolderTeamTreeDTO.getId());
        if (relateJobFolderUserAllInfos == null) {
            return new ArrayList<>();
        }
        List<UserInfoWithPermission> userInfoWithPermissionList = relateJobFolderUserAllInfos.getUserInfoWithPermissionList();
        Map<Long, List<UserInfoWithPermission>> userInfoMap = userInfoWithPermissionList.stream().collect(Collectors.groupingBy(UserInfoWithPermission::getId));
        List<Long> userIdList = new ArrayList<>(userInfoMap.keySet());
        return getRelateJobFoldersUsersByUserId(userIdList, userInfoMap, job, otherProjectUser, relateJobFolderUserAllInfos.getPermissionExtraUserTeamListMap(), userRoleMap, roleId2ExtraTeamIdMap);
    }

    private String getFolderUserType(JobV3 job, UserInfoWithPermission user, Set<Long> extraTeamIds, Set<Long> otherProjectUser) {
        boolean permission;
        if (job.getIsPrivateJob()) {
            permission = job.getPermissionUserId().equals(user.getId()) || job.getAuthorizedUsersForPrivateJob().contains(user.getId());
        } else {
            permission = judgeJobPermission(job, user, extraTeamIds);
        }
        if (!permission) {
            return RelateJobFolderUserType.NO_AUTHORITY.name();
        } else if (otherProjectUser.contains(user.getId())) {
            return RelateJobFolderUserType.JOINED.name();
        } else {
            return RelateJobFolderUserType.NORMAL.name();
        }
    }

    private boolean judgeJobPermission(JobV3 jobV3, UserInfoWithPermission user, Set<Long> extraTeamIds) {
        DataScope dataScope = DataScope.fromDbValue(user.getDataScope());
        return switch (dataScope) {
            case PERMISSION_ALL -> true;
            case PERMISSION_EXTRA_TEAM, PERMISSION_TEAM -> Objects.equals(user.getTeamId(), jobV3.getPermissionTeamId()) || extraTeamIds.contains(jobV3.getPermissionTeamId());
            case PERMISSION_SELF -> Objects.equals(user.getId(), jobV3.getPermissionUserId());
            case PERMISSION_NO -> false;
        };
    }

    @Override
    public void updateSyncLark(Long userId, Integer syncLark) {
        Integer count = userRepository.updateSyncLarkByUserId(userId, syncLark);
        //如果是同步的话，需要将还未开始的日程全部同步到lark
        if (count > 0 && Objects.equals(syncLark, SyncLarkEnum.SYNCHRONIZED.toDbValue())) {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                calendarService.syncOrCancelSyncCalendarEventToLark(syncLark);
            });
        }
    }

    @Override
    public Integer getSyncLark(Long userId) {
        return userRepository.findSyncLarkById(userId);
    }


    @Override
    public Set<Long> findUserIdsInTheSameTeams(List<Long> teamIds) {
        if(CollUtil.isEmpty(teamIds)){
            return Collections.emptySet();
        }
        var userSet = userRepository.findUserIdsByPermissionTeamIdIn(teamIds);
        return userSet;

    }

    @Override
    public Object getAllBriefUsersWithPermissionByType(Module module) {
        TeamDataPermissionRespDTO teamDataPermission = switch (module) {
            case JOB -> initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            case CLIENT_CONTACT -> initiationService.initiateClientContactDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            case REPORT -> initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            case HOME_AND_CALENDAR -> initiationService.initiateHomeAndCalendarDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            case CANDIDATE_PIPELINE_MANAGEMENT -> initiationService.initiateCandidatePipelineManagementDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            case CHINA_INVOICING -> initiationService.initiateChinaInvoicingDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();

        };
        log.info("DataPermission (user: {}) = {}", SecurityUtils.getUserId(), teamDataPermission);
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }
        if (SecurityUtils.isAdmin()) {
            return getAllBriefUsers(SecurityUtils.getTenantId());
        } else if (teamDataPermission.getAll()) {
            return getAllBriefUsers(SecurityUtils.getTenantId());
        } else if (teamDataPermission.getSelf()) {
            return getAllBriefUsersByIds(List.of(SecurityUtils.getUserId()));
        } else {
            Set<Long> teamIds = teamDataPermission.getNestedTeamIds();
            return userRepository.findUsersWithoutRoleByPermissionTeamIdIn(teamIds);
        }
    }

    @Override
    public List<Long> getTeamsByUserId(Long userId) {
        Set<PermissionUserTeamDTO> teamDTOSet = permissionTeamRepository.findTeamVOsByUserId(userId);
        if (CollUtil.isEmpty(teamDTOSet)) {
            return new ArrayList<>();
        }
        return permissionTeamRepository.getChildTeams(teamDTOSet.stream().map(PermissionUserTeamDTO::getId).toList())
                .stream().map(PermissionTeam::getId).toList();
    }

    @Override
    public List<PermissionTeamMemberWithDeliveryLabelDTO> findUsersWithDeliveryLabelByPermissionTeamId(PermissionTeamDeliverySearchVM searchVM) {
        //查询用户列表基础信息
        List<PermissionTeamMemberVM> usersVM = userRepository.buildCustomUserQueryWithNativeSQL(entityManager, searchVM);
        if (CollUtil.isEmpty(usersVM)){
            return List.of();
        }
        //转换为PermissionTeamMemberWithDeliveryLabelDTO列表
        List<PermissionTeamMemberWithDeliveryLabelDTO> users = usersVM.stream()
                .map(user -> {
                    PermissionTeamMemberWithDeliveryLabelDTO dto = new PermissionTeamMemberWithDeliveryLabelDTO();
                    BeanUtil.copyProperties(user, dto);
                    //设置isUpdatable
                    if (user.getCreatedDate() != null && ChronoUnit.DAYS.between(user.getCreatedDate(), Instant.now()) < 180) {
                        dto.setIsUpdatable(true);
                    }
                    //设置teamLeadIds, primaryTeamLeadIds,noPrimaryTeamIds
                    if(StringUtils.isNotBlank(user.getTeamLeadIds())) {
                        dto.setTeamLeadIds(Convert.toList(Long.class, user.getTeamLeadIds().split(",")));
                    }
                    //设置PrimaryTeamLeadIds
                    dto.setPrimaryTeamLeadIds(null);
                    if(StringUtils.isNotBlank(user.getPrimaryTeamLeadIds())) {
                        List<Long> primaryTeamLeadIds = Convert.toList(Long.class, user.getPrimaryTeamLeadIds().split(","));
                        // 如果我是我所在主团队的team leader, 那就取我所在主团队的上级团队team leader ( 如果没有就为空)
                        if (primaryTeamLeadIds.contains(user.getId())) {
                            if(StringUtils.isNotBlank(user.getSuperiorPrimaryTeamLeaderIds())) {
                                List<Long> superiorPrimaryTeamLeaderIds = Convert.toList(Long.class, user.getSuperiorPrimaryTeamLeaderIds().split(","));
                                if(CollUtil.isNotEmpty(superiorPrimaryTeamLeaderIds)) {
                                    dto.setPrimaryTeamLeadIds(superiorPrimaryTeamLeaderIds);
                                }
                            }
                        }else {
                            dto.setPrimaryTeamLeadIds(primaryTeamLeadIds);
                        }
                    }
                    if(StringUtils.isNotBlank(user.getNoPrimaryTeamIds())) {
                        dto.setNoPrimaryTeamIds(Convert.toList(Long.class, user.getNoPrimaryTeamIds().split(",")));
                    }
                    return dto;
                })
                .collect(Collectors.toList());
        Set<Long> userIds = users.stream().map(PermissionTeamMemberWithDeliveryLabelDTO::getId).collect(Collectors.toSet());
        //user role
        CompletableFuture<Map<Long, Object[]>> bulkUserRoleMapFuture =
                CompletableFuture.supplyAsync(() -> {
                    List<Object[]> rawData = roleRepository.findRolesGroupByUserId(
                            SecurityUtils.getTenantId(),
                            new ArrayList<>(userIds)
                    );

                    return rawData.stream()
                            .filter(Objects::nonNull)
                            .filter(arr -> arr.length >= 2)
                            .filter(arr -> arr[0] instanceof BigInteger)
                            .collect(Collectors.toMap(
                                    arr -> ((BigInteger) arr[0]).longValue(),
                                    arr -> arr,
                                    (existing, replacement) -> existing
                            ));
                }, executor);
        CompletableFuture<Map<Long, List<Long>>> bulkLanguagesMapFuture = CompletableFuture.supplyAsync(() -> userLanguageRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserLanguageRelation::getUserId, Collectors.mapping(UserLanguageRelation::getEnumLanguageId, Collectors.toList()))), executor);
        CompletableFuture<Map<Long, List<UserDeliveryCountryRelation>>> bulkDeliveryCountryMapFuture = CompletableFuture.supplyAsync(() -> userDeliveryCountryRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserDeliveryCountryRelation::getUserId)), executor);
        CompletableFuture<Map<Long, List<UserDeliveryProcessRelation>>> bulkProcessIdMapFuture = CompletableFuture.supplyAsync(() -> userDeliveryProcessRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserDeliveryProcessRelation::getUserId)), executor);
        CompletableFuture<Map<Long, List<UserDeliveryIndustryRelation>>> bulkIndustryMapFuture = CompletableFuture.supplyAsync(() -> userDeliveryIndustryRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserDeliveryIndustryRelation::getUserId)), executor);
        CompletableFuture<Map<Long, List<UserDeliveryJobFunctionRelation>>> bulkJobFunctionMapFuture = CompletableFuture.supplyAsync(() -> userDeliveryJobFunctionRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserDeliveryJobFunctionRelation::getUserId)), executor);

        CompletableFuture.allOf(bulkUserRoleMapFuture, bulkLanguagesMapFuture, bulkDeliveryCountryMapFuture, bulkProcessIdMapFuture, bulkIndustryMapFuture, bulkJobFunctionMapFuture)
                .exceptionally(t -> {
                    log.error("Error occurred when fetching user data: ", t);
                    throw new ExternalServiceInterfaceException("Error occurred when fetching user data");
                }).join();
        Map<Long, Object[]> userRoleMap = bulkUserRoleMapFuture.join();
        Map<Long, List<Long>> languagesMap = bulkLanguagesMapFuture.join();
        Map<Long, List<UserDeliveryCountryRelation>> deliveryCountryMap = bulkDeliveryCountryMapFuture.join();
        Map<Long, List<UserDeliveryProcessRelation>> processIdMap = bulkProcessIdMapFuture.join();
        Map<Long, List<UserDeliveryIndustryRelation>> industryMap = bulkIndustryMapFuture.join();
        Map<Long, List<UserDeliveryJobFunctionRelation>> jobFunctionMap = bulkJobFunctionMapFuture.join();

        return users.stream().map(u -> {
            PermissionTeamMemberWithDeliveryLabelDTO result = BeanUtil.toBean(u, PermissionTeamMemberWithDeliveryLabelDTO.class);
            result.setLanguages(languagesMap.getOrDefault(u.getId(), null));
            List<UserDeliveryProcessRelation> processRelations = processIdMap.getOrDefault(u.getId(), null);
            if (CollUtil.isNotEmpty(processRelations)) {
                result.setDeliveryProcessIds(processRelations.stream().filter(t -> result.getIsUpdatable() != null && result.getIsUpdatable() ? Boolean.TRUE.equals(t.getUpdated()) : t.getTop()).map(UserDeliveryProcessRelation::getProcessId).collect(Collectors.toList()));
            }
            List<UserDeliveryIndustryRelation> industryRelations = industryMap.getOrDefault(u.getId(), null);
            if (CollUtil.isNotEmpty(industryRelations)) {
                result.setDeliveryIndustries(industryRelations.stream().filter(t ->  result.getIsUpdatable() != null && result.getIsUpdatable() ? Boolean.TRUE.equals(t.getUpdated()) : t.getTop()).map(UserDeliveryIndustryRelation::getEnumIndustryMappingId).collect(Collectors.toList()));
            }
            List<UserDeliveryJobFunctionRelation> jobFunctionRelations = jobFunctionMap.getOrDefault(u.getId(), null);
            if (CollUtil.isNotEmpty(jobFunctionRelations)) {
                result.setDeliveryJobFunctions(jobFunctionRelations.stream().filter(t -> result.getIsUpdatable() != null && result.getIsUpdatable() ? Boolean.TRUE.equals(t.getUpdated()) : t.getTop()).map(UserDeliveryJobFunctionRelation::getEnumJobFunctionMappingId).collect(Collectors.toList()));
            }
            List<UserDeliveryCountryRelation> countryRelations = deliveryCountryMap.getOrDefault(u.getId(), null);
            if (CollUtil.isNotEmpty(countryRelations)) {
                result.setDeliveryLocations(countryRelations.stream().filter(t -> result.getIsUpdatable() != null && result.getIsUpdatable() ? Boolean.TRUE.equals(t.getUpdated()) : t.getTop()).map(UserDeliveryCountryRelation::getEnumCountryId).collect(Collectors.toList()));
            }
            //user roles
            Object[] userRoles = userRoleMap.getOrDefault(u.getId(), null);
            if (ObjectUtil.isNotEmpty(userRoles)) {
                result.setRoles(StrUtil.toString(userRoles[1]));
            }
            return result;
        }).collect(Collectors.toList());
    }

    @Override
    public void updatePushTime() {
        Optional<User> optUser = userRepository.findById(SecurityUtils.getUserId());
        if(optUser.isPresent()) {
            User user = optUser.get();
            user.setLastPushTime(Instant.now());
            userRepository.save(user);
        }
    }


    @Override
    public Long getUserPushInterval(UserPushIntervalDTO dto) {
        return tenantPushRulesRepository.findMinIntervalWithNullHandlingByUserIdAndTeamId(dto.getUserId(), dto.getTeamId(), Long.MAX_VALUE);
    }

    @Override
    public List<Long> getAllInactiveByidIn(Set<Long> ids) {
        return userRepository.findInactiveByidIn(ids);
    }

    @Override
    public List<TeamInfoVO> getTeamInfoList(List<Long> userIds) {
        List<com.altomni.apn.common.dto.user.TeamInfoProjection> teamInfoProjections = userRepository.getTeamInfosByUserIds(userIds);
        return userRepository.convertToTeamInfoVOs(teamInfoProjections);
    }

}