package com.altomni.apn.user.service.customconfig.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.TalentFormConfig;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.TalentFormConfigRepository;
import com.altomni.apn.user.service.application.ApplicationService;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.customconfig.TalentFormConfigService;
import com.altomni.apn.user.service.dto.customconfig.TalentFormConfigDTO;
import com.altomni.apn.user.service.mapper.customconfig.JsonMapperUtil;
import com.altomni.apn.user.service.mapper.customconfig.SystemConfigDefaultMapper;
import com.altomni.apn.user.service.mapper.customconfig.TalentFormConfigMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class TalentFormConfigServiceImpl implements TalentFormConfigService {
    final static Category TALENT_FORM = Category.TALENT_FORM;
    final static ConfigSubcategory TALENT_DEFAULT_FORM = ConfigSubcategory.TALENT_FORM_DEFAULT;
    @Resource
    private TalentFormConfigRepository talentFormConfigRepository;

    @Resource
    TalentFormConfigMapper talentFormConfigMapper;
    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    ApplicationService applicationService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;



    @Override
    public String getFormConfigByTenantId(Long tenantId) {
        if(tenantId == null){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETFORMCONFIGBYTENANTID_INVALIDTENANTID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        Optional<TalentFormConfig> result = Optional.ofNullable(talentFormConfigRepository.findByTenantId(tenantId));
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            if (result.isPresent() && StringUtils.isNotEmpty(result.get().getCustomConfig())) {
                return objectMapper.writeValueAsString(talentFormConfigMapper.toDto(result.get()));
            } else {
                return objectMapper.writeValueAsString(systemConfigDefaultMapper.toDto(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(TALENT_FORM, TALENT_DEFAULT_FORM)));
            }
        } catch (JsonProcessingException e) {
            throw new CustomParameterizedException("json transfer error!");
        }
    }



    @Override
    public TalentFormConfigDTO saveTalentFormConfig(TalentFormConfigDTO talentFormConfigDTO, Long tenantId) {
        if(tenantId == null){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SAVETALENTFORMCONFIG_INVALIDTENANT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        TalentFormConfig formConfig = talentFormConfigRepository.findByTenantId(tenantId);
        if(formConfig != null){
            formConfig.setCustomConfig(JsonMapperUtil.customFieldListToJsonString(talentFormConfigDTO.getCustomConfig()));
        }else{
            formConfig = talentFormConfigMapper.toEntity(talentFormConfigDTO);
            formConfig.setTenantId(tenantId);
        }

        talentFormConfigRepository.saveAndFlush(formConfig);
        talentFormConfigDTO = talentFormConfigMapper.toDto(formConfig);

        return talentFormConfigDTO;
    }

}
