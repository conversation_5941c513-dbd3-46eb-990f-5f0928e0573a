<?xml version="1.0" encoding="utf-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity JobFormConfig.
    -->
    <changeSet id="1682011226003-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_preference"/>
            </not>
        </preConditions>
        <createTable tableName="user_preference">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="user_id" type="bigint">
                <constraints nullable="false" unique="true" />
            </column>
            <column name="recruitment_process_id" type="bigint">
<!--                recruitment process which the user recently used for creating a job-->
            </column>
            <column name="private_job_recruitment_process_id" type="bigint">
                <!--                recruitment process which the user recently used for creating a private job-->
            </column>

            <column name="job_column_config" type="json"/>
            <column name="private_job_column_config" type="json"/>
            <column name="talent_column_config" type="json"/>
            <column name="talent_database_column_config" type="json"/>
            <column name="talent_pipeline_column_config" type="json"/>
            <column name="talent_association_job_folder_column_config" type="json"/>
            <column name="talent_search_folder_column_config" type="json"/>
            <column name="company_client_column_config" type="json"/>
            <column name="company_prospect_column_config" type="json"/>
            <column name="dashboard_column_config" type="json"/>
            <column name="company_fte_bd_report_column_config" type="json"/>
            <column name="personalization_config" type="json"/>
            <column name="invoicing_column_config" type="json"/>
            <column name="user_search_column_config" type="json"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="index_user_preference_recruitment_process_id" tableName="user_preference">
            <column name="recruitment_process_id"/>
        </createIndex>
        <createIndex indexName="index_user_preference_user_id" tableName="user_preference">
            <column name="user_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>
