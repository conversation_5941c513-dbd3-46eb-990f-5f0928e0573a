package com.altomni.apn.canal.service.changerecord.talent.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.CanalEntry.Column;
import com.altomni.apn.canal.constants.LogEventType;
import com.altomni.apn.canal.entity.DeliveryDiff;
import com.altomni.apn.canal.entity.RecordDataInfo;
import com.altomni.apn.canal.entity.TalentProcessRecord;
import com.altomni.apn.canal.repository.TalentRecruitmentProcessRepository;
import com.altomni.apn.canal.repository.TalentRepository;
import com.altomni.apn.canal.repository.TalentResumeRelationRepository;
import com.altomni.apn.canal.service.changerecord.TalentRecordContextHolder;
import com.altomni.apn.canal.service.changerecord.talent.TalentChangeRecordService;
import com.altomni.apn.common.aop.user.SimpleUserAspect;
import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.common.domain.enumeration.*;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.CommonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Consumer;

import static com.altomni.apn.canal.constants.LogEventType.*;


@Slf4j
@Service
@Data
public class TalentChangeRecordServiceImpl implements TalentChangeRecordService {

    @Resource
    private TalentRecruitmentProcessRepository talentRecruitmentProcessRepository;
    @Resource
    private TalentRepository talentRepository;

    @Resource
    private EnumCommonService enumCommonService;

    private static final String ID = "id";


    //table name
    private static final String TALENT = "talent";

    public static final String TALENT_ADDITIONAL_INFO = "talent_additional_info";

    public static final String TALENT_CONTACT = "talent_contact";

    public static final String TALENT_CURRENT_LOCATION = "talent_current_location";

    public static final String TALENT_INDUSTRY_RELATION = "talent_industry_relation";

    public static final String TALENT_RESUME_RELATION = "talent_resume_relation";

    public static final String TALENT_JOB_FUNCTION_RELATION = "talent_job_function_relation";

    public static final String TALENT_LANGUAGE_RELATION = "talent_language_relation";

    public static final String TALENT_WORK_AUTHORIZATION_RELATION = "talent_work_authorization_relation";

    public static final String TALENT_RECRUITMENT_PROCESS_NODE = "talent_recruitment_process_node";
    public static final String TALENT_RECRUITMENT_PROCESS_INTERVIEW = "talent_recruitment_process_interview";

    public static final String TALENT_NOTE = "talent_note";

    public static final String TALENT_OWNERSHIP = "talent_ownership";
    public static final String TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB = "talent_recruitment_process_submit_to_job";
    public static final String SUBMIT_TO_JOB_RESUME_CHANGE = "submitToJobResumeChange";

    public static final String TALENT_CONFIDENTIAL = "confidential_talent";

    public static final String TALENT_VOIP_CONTACT = "voip_contact";


    //column
    private static final String COMPANY_ID = "company_id";

    private static final String FULL_NAME = "full_name";
    private static final String FIRST_NAME = "first_name";
    private static final String LAST_NAME = "last_name";

    private static final String PHOTO_URL = "photo_url";

    private static final String BIRTHDAY = "birthday";


    public static final String MOTIVATION_ID = "motivation_id";

    //contact
    private static final String PRIMARY_PHONE = "primaryPhone";

    private static final String PRIMARY_EMAIL = "primaryEmail";

    private static final String JHI_TYPE = "jhi_type";

    private static final String CONTACT = "contact";
    private static final String DETAILS = "details";
    private static final String VERIFICATION_STATUS = "verification_status";

    //additional info
    private static final String TALENT_EXTEND_INFO = "extended_info";
    private static final String TALENT_LOCAL_EXTEND_INFO = "local_extended_info";

    private static final String SALARY_RANGE = "salaryRange";
    private static final String SOURCE_OWNERSHIP_PERIOD = "sourceOwnershipPeriod";
    private static final String CURRENCY = "currency";
    private static final String PAY_TYPE = "payType";
//    private static final String PREFERRED_SALARY_RANGE = "preferredSalaryRange";
//    private static final String PREFERRED_CURRENCY = "preferredCurrency";
//    private static final String PREFERRED_PAY_TYPE = "preferredPayType";
//private static final String PREFERRED_LOCATIONS = "preferredLocations";
    private static final String PREFERENCES = "preferences";
    private static final String NICK_NAME = "nickName";
    private static final String NICK_FIRST_NAME = "nickFirstName";
    private static final String NICK_LAST_NAME = "nickLastName";

    private static final String GENDER = "gender";
    private static final String ETHNICITY = "ethnicity";
    private static final String PREFERRED_PRONOUN = "preferredPronoun";
    private static final String DISABILITY = "disability";
    private static final String VETERAN = "veteran";
    private static final String MEMBER_OF_LGBTQ = "memberOfLGBTQ";

    private static final String SOURCE_CHANNEL = "source";
    public static final String DODAX_CANDIDATE_ID = "dodaXCandidateID";
    public static final String DODAX_OWNERSHIP_PERIOD = "dodaXOwnershipPeriod";
    private static final String SKILLS = "skills";

    private static final String EDUCATIONS = "educations";

    private static final String EXPERIENCES = "experiences";

    private static final String PROJECTS = "projects";


    private static final String LAST_MODIFIED_DATE = "last_modified_date";

    private static final String LAST_MODIFIED_BY = "last_modified_by";
    private static final String CREATED_BY = "created_by";
    private static final String TENANT_ID = "tenant_id";

    private static final String LAST_EDITED_TIME = "last_edited_time";

    private static final String LAST_SYNC_TIME = "last_sync_time";


    //location
    private static final String ORIGINAL_LOC = "original_loc";
    private static final String ZIP_CODE = "zip_code";

    //industry
    private static final String TALENT_INDUSTRY_ID = "industry_id";

    //job_function
    private static final String TALENT_JOB_FUNCTION_ID = "job_function_id";

    //language
    private static final String TALENT_LANGUAGE_ID = "language_id";

    //work authorization
    private static final String TALENT_WORK_AUTHORIZATION_ID = "work_authorization_id";

    //resume_id
    private static final String TALENT_RESUME_ID = "resume_id";
    private static final String STATUS = "status";

    public static final String TALENT_ID = "talent_id";
    public static final String TALENT_RESUME_RELATION_ID = "talent_resume_relation_id";

    //talent recruitment process node
    public static final String TALENT_RECRUITMENT_PROCESS_ID = "talent_recruitment_process_id";
    public static final String PROGRESS = "progress";

    public static final String NODE_TYPE = "node_type";

    public static final String NODE_STATUS = "node_status";

    //talent note
    public static final String NOTE = "note";
    public static final String NOTE_STATUS = "note_status";
    public static final String NOTE_MOTIVATION_ID = "note_motivation_id";
    public static final String NOTE_TITLE = "title";
    public static final String NOTE_PRIORITY = "priority";
    public static final String NOTE_TYPE = "note_type";
    public static final String ADDITIONAL_INFO = "additional_info";

    //talent ownership
    public static final String OWNERSHIP_TYPE = "ownership_type";
    //代表share的变化，这样可以兼容v2.5日志
    public static final String OWNERSHIP = "ownership";
    //talent_owner的变化
    public static final String OWNERSHIP_TALENT_OWNER = "ownership_talent_owner";

    // 候选人保密
    public static final String CONFIDENTIAL = "confidential";
    public static final String DECLASSIFY = "declassify";
    public static final String CONFIDENTIAL_HANDOVER = "confidential_handover";

    //候选人通话
    public static final String TALENT_VOIP_CONTACT_PHONE = "phone_number";
    public static final String TALENT_VOIP_CALL = "voipCall";
    public static final String TALENT_NAME = "talent_name";


    private void esInfo(JSONObject esDocumentJson, Column preColumn, Column afterColumn, Boolean update) {
        if (update) {
            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
            esDocumentJson.put("changedFrom", preColumn.getValue());
        } else {
            esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
            esDocumentJson.put("changedFrom", "");
        }
        esDocumentJson.put("changedTo", afterColumn.getValue());
    }

    private void esInfo(JSONObject esDocumentJson, Boolean update) {
        if (update) {
            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
        } else {
            esDocumentJson.put("eventType", EVENT_TYPE_INSERT);

        }
        esDocumentJson.put("changedFrom", "");
        esDocumentJson.put("changedTo", "");
    }

    private void checkColumnMatch(List<Column> preColumns, List<Column> afterColumns, Boolean update) {
        if (update) {
            if (preColumns.size() != afterColumns.size()) {
                log.error("The amount of changed columns is not same before changing and after.");
            }
        }
    }

    public static Instant formatTime(String time) {
        log.info("format time input : {}", time);
        log.info("format time current zone: {}", ZoneId.systemDefault());
        List<DateTimeFormatter> formatters = Arrays.asList(
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        );

        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(time, formatter);
                ZoneId currentZone = ZoneId.systemDefault();
                log.info("current zone:{}", currentZone);
                ZonedDateTime zonedDateTime = localDateTime.atZone(currentZone);
                return zonedDateTime.toInstant();
            } catch (DateTimeParseException e) {
                // Continue to try the next formatter
            }
        }

        log.error("Cannot parse current time for the talent log: time:{}", time);
        throw new IllegalArgumentException("Unsupported date-time format: " + time);
    }


    public static String convertSnakeCaseToCamelCase(String snakeCase) {
        StringBuilder camelCase = new StringBuilder();
        boolean capitalizeNext = false;

        for (char ch : snakeCase.toCharArray()) {
            if (ch == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                camelCase.append(Character.toUpperCase(ch));
                capitalizeNext = false;
            } else {
                camelCase.append(ch);
            }
        }

        return camelCase.toString();
    }

    private void recordTalent(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobFields = new HashSet<>(new ArrayList<>(List.of(FULL_NAME, PHOTO_URL, BIRTHDAY, MOTIVATION_ID)));
        if (TALENT.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
                        case FULL_NAME:
                            esDocumentJson.put("key", convertSnakeCaseToCamelCase(FULL_NAME));
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case MOTIVATION_ID:
                            esDocumentJson.put("key", convertSnakeCaseToCamelCase(MOTIVATION_ID));
                            String motivation = findMotivationName(afterColumn.getValue());
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", "");
                                esDocumentJson.put("changedTo", motivation);
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                                esDocumentJson.put("changedTo", motivation);
                            }
                            break;
                        default:
                            esDocumentJson.put("key", convertSnakeCaseToCamelCase(columnName));
                            esInfo(esDocumentJson, update);
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private String findMotivationName(String value) {
        List<EnumMotivation> allEnumMotivation = enumCommonService.findAllEnumMotivation();
        if(StringUtils.isEmpty(value)) {
            return null;
        }
        Integer id = Integer.parseInt(value);
        for(EnumMotivation motivation : allEnumMotivation) {
            if(Objects.equals(motivation.getId(), id)) {
                return motivation.getName();
            }
        }
        return null;
    }

    private Boolean recordNewTalent(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobFields = new HashSet<>(new ArrayList<>(List.of(ID)));
        if (TALENT.equals(tableName)) {

            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobFields.contains(columnName) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", TALENT);
                    esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", afterColumn.getValue());
                    esDocuments.add(esDocumentJson);
                    return true;
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public List<JSONObject> generateNewTalent(Long talentId) {
        JSONObject esDocumentJson = new JSONObject();
        List<JSONObject> esDocuments = new ArrayList<>();
        esDocumentJson.put("key", TALENT);
        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
        esDocumentJson.put("changedFrom", "");
        esDocumentJson.put("changedTo", talentId);
        esDocuments.add(esDocumentJson);
        return esDocuments;
    }


    private void recordTalentContact(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> talentLocationFields = new HashSet<>(new ArrayList<>(List.of(JHI_TYPE, CONTACT, DETAILS, STATUS, VERIFICATION_STATUS)));
        Set<ContactType> contactTypeSet = new HashSet<>(new ArrayList<>(List.of(
                ContactType.PHONE, ContactType.CELL_PHONE, ContactType.HOME_PHONE, ContactType.PRIMARY_PHONE, ContactType.PRIMARY_EMAIL, ContactType.EMAIL)));
        if (TALENT_CONTACT.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            ContactType contactType = null;
            String preColumnValue = null;
            String afterColumnValue = null;
            String preDetailsValue = null;
            String afterDetailsValue = null;
            Integer afterStatusValue = null;
            Integer afterVerificationStatusValue = null;
            boolean anyUpdated = false;
            boolean detailUpdated = false;
            for (int i = 0; i < afterColumns.size(); i++) {
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (talentLocationFields.contains(columnName)) {
                    switch (columnName) {
                        case JHI_TYPE:
                            contactType = ContactType.fromDbValue(Integer.valueOf(afterColumn.getValue()));
                            anyUpdated |= afterColumn.getUpdated();
                            break;
                        case CONTACT:
                            preColumnValue = preColumn != null ? preColumn.getValue() : "";
                            afterColumnValue = afterColumn.getValue();
                            anyUpdated |= afterColumn.getUpdated();
                            break;
                        case DETAILS:
                            preDetailsValue = preColumn != null ? preColumn.getValue() : "";
                            afterDetailsValue = afterColumn.getValue();
                            detailUpdated |= afterColumn.getUpdated();
                            break;

                        case STATUS:
                            afterStatusValue = Integer.valueOf(afterColumn.getValue());
                            anyUpdated |= afterColumn.getUpdated();
                            detailUpdated |= afterColumn.getUpdated();
                            break;
                        case VERIFICATION_STATUS:
                            if (afterColumn.getUpdated()) { //only record verification status when it's been updated
                                String value = afterColumn.getValue();
                                if(StringUtils.isNotEmpty(value)) {
                                    afterVerificationStatusValue = Integer.valueOf(value);
                                }
                            }
                            break;
                    }
                }
            }


            if (ContactType.DODAX.equals(contactType)) {
                if(anyUpdated) {
                    JSONObject dodaXIdObj = new JSONObject();
                    dodaXIdObj.put("key", DODAX_CANDIDATE_ID);
                    if(StringUtils.isBlank(preColumnValue)) {
                        dodaXIdObj.put("eventType", EVENT_TYPE_INSERT);
                        dodaXIdObj.put("changedFrom", "");
                        dodaXIdObj.put("changedTo", afterColumnValue);
                    } else {
                        if(Objects.equals(TalentContactStatus.AVAILABLE.toDbValue(), afterStatusValue)) {
                            dodaXIdObj.put("eventType", EVENT_TYPE_UPDATE);
                            dodaXIdObj.put("changedFrom", "");
                            dodaXIdObj.put("changedTo", afterColumnValue);
                        } else {
                            dodaXIdObj.put("eventType", EVENT_TYPE_UPDATE);
                            dodaXIdObj.put("changedFrom", preColumnValue);
                            dodaXIdObj.put("changedTo", "");
                        }
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent dodax id change: {}", JSON.toJSON(dodaXIdObj));
                    esDocuments.add(dodaXIdObj);
                }
                if(detailUpdated) {
                    JSONObject dodaXPeriodObj = new JSONObject();
                    dodaXPeriodObj.put("key", DODAX_OWNERSHIP_PERIOD);
                    if(StringUtils.isBlank(preDetailsValue)) {
                        dodaXPeriodObj.put("eventType", EVENT_TYPE_INSERT);
                        dodaXPeriodObj.put("changedFrom", "");
                        dodaXPeriodObj.put("changedTo", afterDetailsValue);
                    } else {
                        if(Objects.equals(TalentContactStatus.AVAILABLE.toDbValue(), afterStatusValue)) {
                            dodaXPeriodObj.put("eventType", EVENT_TYPE_UPDATE);
                            dodaXPeriodObj.put("changedFrom", "");
                            dodaXPeriodObj.put("changedTo", afterDetailsValue);
                        } else {
                            dodaXPeriodObj.put("eventType", EVENT_TYPE_UPDATE);
                            dodaXPeriodObj.put("changedFrom", preDetailsValue);
                            dodaXPeriodObj.put("changedTo", "");
                        }
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent dodax period change: {}", JSON.toJSON(dodaXPeriodObj));
                    esDocuments.add(dodaXPeriodObj);
                }

            } else {
                if (ContactType.PHONE.equals(contactType) && Objects.nonNull(afterVerificationStatusValue) && TalentContactVerificationStatus.WRONG_CONTACT.toDbValue().equals(afterVerificationStatusValue)) {
                    JSONObject esDocumentJson = new JSONObject();
                    esDocumentJson.put("key", "WrongContact:" + afterColumnValue);
                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", "WrongContact");

                    esDocuments.add(esDocumentJson);
                }

                if (contactType != null && anyUpdated) {
                    JSONObject esDocumentJson = new JSONObject();
                    esDocumentJson.put("key", contactType.name());
                    if (contactTypeSet.contains(contactType)) {
                        if(StringUtils.isBlank(preColumnValue)) {
                            esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                            esDocumentJson.put("changedFrom", "");
                            esDocumentJson.put("changedTo", afterColumnValue);
                        } else {
                            if(Objects.equals(TalentContactStatus.AVAILABLE.toDbValue(), afterStatusValue)) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", "");
                                esDocumentJson.put("changedTo", afterColumnValue);
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumnValue);
                                esDocumentJson.put("changedTo", "");
                            }
                        }
                    } else {
                        esDocumentJson.put("eventType", StringUtils.isBlank(preColumnValue) ? EVENT_TYPE_INSERT : EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", "");
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent contact change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordJobFunction(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> fields = new HashSet<>(new ArrayList<>(List.of(TALENT_JOB_FUNCTION_ID, TALENT_ID)));
        if (TALENT_JOB_FUNCTION_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (Column column : afterColumns) {
                JSONObject esDocumentJson = new JSONObject();
                String columnName = column.getName();
                if (fields.contains(columnName) && column.getUpdated()) {
                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_JOB_FUNCTION_ID));
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                    } else {
                        //esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE); // insert also regarded as update
                    }
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", "");

                    log.info("[APN: EsFillerTalentRecordService] record talent job function change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordTalentCurrentLocation(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> talentLocationFields = new HashSet<>(new ArrayList<>(List.of(ORIGINAL_LOC, ZIP_CODE)));
        if (TALENT_CURRENT_LOCATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (talentLocationFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
                        case ORIGINAL_LOC:
                            String oldVal = null;
                            if(preColumn != null) {
                                oldVal = formatOriginalLoc(preColumn.getValue());

                            }
                            String newVal = formatOriginalLoc(afterColumn.getValue());
                            if(newVal != null && !newVal.equals(oldVal)) {
                                esDocumentJson.put("key", "currentLocation");
                                if (update) {
                                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                } else {
                                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE); // insert also regarded as update
                                }
                                esDocumentJson.put("changedFrom", "");
                                esDocumentJson.put("changedTo", "");
                            }
                            break;
                        case ZIP_CODE:
                            if (update) {
                                esDocumentJson.put("key", "zipCode");
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", "");
                                esDocumentJson.put("changedTo", "");
                            } else {
                                if(StringUtils.isNotEmpty(afterColumn.getValue())) {
                                    esDocumentJson.put("key", "zipCode");
                                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                    esDocumentJson.put("changedFrom", "");
                                    esDocumentJson.put("changedTo", "");
                                }
                            }
                            break;
                        default:
//                            esDocumentJson.put("key", "currentLocation");
//                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                    }

                    log.info("[APN: EsFillerTalentRecordService] record talent current location change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private String formatOriginalLoc(String val) {
        if(val == null) {
            return val;
        }
        JSONObject jsonObject = JSONUtil.parseObj(val);
        jsonObject.entrySet().removeIf(entry -> entry.getValue().equals(""));
        jsonObject.remove("id");
        JSONObject sortedJsonObject = JSONUtil.parseObj(jsonObject.toString(), true);
        return sortedJsonObject.toString();
    }

    private void recordTalentResume(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (TALENT_RESUME_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (Column column : afterColumns) {
                JSONObject esDocumentJson = new JSONObject();
                String columnName = column.getName();
                if (STATUS.equals(columnName) && column.getUpdated()) {
                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_RESUME_ID));
                    if (update) {
                        String value = column.getValue();
                        if (Objects.equals(CommonDataStatus.INVALID.toDbValue(), Byte.valueOf(value))) {
                            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        } else {
                            esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        }
                    } else {
                        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                    }
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", "");
                    log.info("[APN: EsFillerTalentRecordService] record talent resume change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }


    private void recordTalentLanguage(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> fields = new HashSet<>(new ArrayList<>(List.of(TALENT_LANGUAGE_ID, TALENT_ID)));
        if (TALENT_LANGUAGE_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (fields.contains(columnName) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_LANGUAGE_ID));
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                    } else {
                        //esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE); // insert also regarded as update
                    }
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", "");

                    log.info("[APN: EsFillerTalentRecordService] record talent language change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordTalentWorkAuthorization(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> fields = new HashSet<>(new ArrayList<>(List.of(TALENT_WORK_AUTHORIZATION_ID, TALENT_ID)));
        if (TALENT_WORK_AUTHORIZATION_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (Column column : afterColumns) {
                JSONObject esDocumentJson = new JSONObject();
                String columnName = column.getName();
                if (fields.contains(columnName) && column.getUpdated()) {
                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_WORK_AUTHORIZATION_ID));
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                    } else {
                        //esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE); // insert also regarded as update
                    }
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", "");

                    log.info("[APN: EsFillerTalentRecordService] record talent work authorization info change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }


    private String jsonRangeTransfer(JSONObject json) {
        Long lte = json.getLong("lte");
        Long gte = json.getLong("gte");
        return gte <= lte ? gte + " - " + lte : lte + " - " + gte;
    }

    private void jsonComparor(JSONObject prev, JSONObject post, String column, List<JSONObject> esDocuments) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("key", column);
        if (prev == null && post != null) {
            jsonObject.put("eventType", EVENT_TYPE_INSERT);
            jsonObject.put("changedFrom", "");
            //jsonObject.put("changedTo", jsonRangeTransfer(post));
            jsonObject.put("changedTo", "");
            esDocuments.add(jsonObject);
        } else if (prev != null && post != null) {
            if (!Objects.equals(prev.get("gte", String.class), post.get("gte", String.class)) || !Objects.equals(prev.get("lte", String.class), post.get("lte", String.class))) {
                jsonObject.put("eventType", EVENT_TYPE_UPDATE);
//                jsonObject.put("changedFrom", jsonRangeTransfer(prev));
//                jsonObject.put("changedTo", jsonRangeTransfer(post));
                jsonObject.put("changedFrom", "");
                jsonObject.put("changedTo", "");
                esDocuments.add(jsonObject);
            }
        } else if (prev != null && post == null) {
            jsonObject.put("eventType", EVENT_TYPE_UPDATE);
            //jsonObject.put("changedFrom", jsonRangeTransfer(prev));
            jsonObject.put("changedFrom", "");
            jsonObject.put("changedTo", "");
            esDocuments.add(jsonObject);
        }
    }

    private String formatSolve(String input) {
        if(input == null) {
            return input;
        }
        JSONArray jsonArray = JSONUtil.parseArray(input);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            jsonObject.remove("id");
            //experiences中的deletable不用比较
            jsonObject.remove("deletable");
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                if (entry.getValue() instanceof Number) {
                    jsonObject.put(entry.getKey(), entry.getValue().toString());
                }
            }
            JSONObject sortedJsonObject = JSONUtil.parseObj(jsonObject.toString(), true);
            jsonArray.set(i, sortedJsonObject);
        }
       return jsonArray.toString();
    }

    private void recordTalentAdditionInfo(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobAdditionalInfoFields = new HashSet<>(new ArrayList<>(List.of(TALENT_EXTEND_INFO)));
        Set<String> additionalInfoFields = new HashSet<>(new ArrayList<>(List.of(SALARY_RANGE, CURRENCY, PAY_TYPE, PREFERENCES, EXPERIENCES, PROJECTS, EDUCATIONS, SOURCE_CHANNEL, SKILLS)));
        Set<String> delivery = new HashSet<>(new ArrayList<>(List.of(GENDER, ETHNICITY, PREFERRED_PRONOUN, DISABILITY, VETERAN, MEMBER_OF_LGBTQ)));
        Set<String> formatSolve = new HashSet<>(new ArrayList<>(List.of(EXPERIENCES, PROJECTS, EDUCATIONS, SKILLS, PREFERENCES)));
        if (TALENT_ADDITIONAL_INFO.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            String postNickFirstName = "";
            String postNickLastName = "";
            String prevNickFirstName = "";
            String prevNickLastName = "";
            String postDodaXId = "";
            String postDodaXPeriod = "";
            String prevDodaXId = "";
            String prevDodaXPeriod = "";
            Map<String, DeliveryDiff> deliveryDiffMap = new HashMap<>();

            for (int i = 0; i < afterColumns.size(); i++) {
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobAdditionalInfoFields.contains(columnName) && afterColumn.getUpdated()) {
                    JSONObject prevJson = preColumn == null ? null : JSONUtil.parseObj(preColumn.getValue());
                    JSONObject postJson = JSONUtil.parseObj(afterColumn.getValue());
                    for (String key : postJson.keySet()) {
                        if(additionalInfoFields.contains(key)) {
                            String prevField = prevJson == null ? null : prevJson.getStr(key);
                            String postField = postJson.getStr(key);
                            //educations 回填了学校排名和标签信息 需要排除下 不记日期
                            if(EDUCATIONS.contains(key)) {
                                prevField = excludeCollegeInfo(prevField);
                                postField = excludeCollegeInfo(postField);
                            }
                            //experiences 回填了禁猎相关公司id 需要排除下 不记日期
                            String originalPrevField = null;
                            String originalPostField = null;
                            if(EXPERIENCES.contains(key)) {
                                originalPrevField = prevField;
                                originalPostField = postField;
                                prevField = excludeCompanyRelationsInfo(prevField);
                                postField = excludeCompanyRelationsInfo(postField);
                            }
                            if(formatSolve.contains(key)) {
                                prevField = formatSolve(prevField);
                                postField = formatSolve(postField);
                            }
                            if(postField != null && !postField.equals(prevField)) {
                                log.info("additionalInfoFields change key: {}, changedFrom:{}, changed to : {}", key, prevField, postField);
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("key", key);
                                jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                                //禁猎客户功能：特殊处理experience，需要返回给前端用于判断是否是禁猎改动
                                if(EXPERIENCES.equals(key)) {
                                    jsonObject.put("changedFrom", originalPrevField);
                                    jsonObject.put("changedTo", originalPostField);
                                } else if(PREFERENCES.equals(key)) {
                                    jsonObject.put("changedTo", postField);
                                } else {
                                    jsonObject.put("changedFrom", "");
                                    jsonObject.put("changedTo", "");
                                }
                                esDocuments.add(jsonObject);
                            }
                        } else if(NICK_FIRST_NAME.equalsIgnoreCase(key)) {
                            postNickFirstName = postJson.getStr(key);
                        } else if(NICK_LAST_NAME.equalsIgnoreCase(key)) {
                            postNickLastName = postJson.getStr(key);
                        } else if(DODAX_CANDIDATE_ID.equalsIgnoreCase(key)) {
                            postDodaXId = postJson.getStr(key);
                        } else if(DODAX_OWNERSHIP_PERIOD.equalsIgnoreCase(key)) {
                            postDodaXPeriod = postJson.getStr(key);
                        } else if(delivery.contains(key)) {
                            DeliveryDiff deliveryDiff = deliveryDiffMap.get(key);
                            if(deliveryDiff == null) {
                                deliveryDiff = new DeliveryDiff();
                            }
                            deliveryDiff.setKey(key);
                            deliveryDiff.setPost(postJson.getStr(key));
                            deliveryDiffMap.put(key, deliveryDiff);
                        }
                    }
                    if(prevJson != null) {
                        for (String key : prevJson.keySet()) {
                            if(additionalInfoFields.contains(key)) {
                                String prevField = prevJson.getStr(key);
                                String postField = postJson.getStr(key);
                                //educations 回填了学校排名和标签信息 需要排除下 不记日期
                                if(EDUCATIONS.contains(key)) {
                                    prevField = excludeCollegeInfo(prevField);
                                    postField = excludeCollegeInfo(postField);
                                }
                                //experiences 回填了禁猎相关公司id 需要排除下 不记日期
                                String originalPrevField = null;
                                String originalPostField = null;
                                if(EXPERIENCES.contains(key)) {
                                    originalPrevField = prevField;
                                    originalPostField = postField;
                                    prevField = excludeCompanyRelationsInfo(prevField);
                                    postField = excludeCompanyRelationsInfo(postField);
                                }
                                if(formatSolve.contains(key)) {
                                    prevField = formatSolve(prevField);
                                    postField = formatSolve(postField);
                                }
                                if(prevField != null && !prevField.equals(postField)) {
                                    log.info("additionalInfoFields change key: {}, changedFrom:{}, changed to : {}", key, prevField, postField);
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("key", key);
                                    jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                                    //禁猎客户功能：特殊处理experience，需要返回给前端用于判断是否是禁猎改动
                                    if(EXPERIENCES.equals(key)) {
                                        jsonObject.put("changedFrom", originalPrevField);
                                        jsonObject.put("changedTo", originalPostField);
                                    } else if(PREFERENCES.equals(key)) {
                                        jsonObject.put("eventType", EVENT_TYPE_DELETE);
                                    } else {
                                        jsonObject.put("changedFrom", "");
                                        jsonObject.put("changedTo", "");
                                    }
                                    esDocuments.add(jsonObject);
                                }
                            } else if(NICK_FIRST_NAME.equalsIgnoreCase(key)) {
                                prevNickFirstName = prevJson.getStr(key);
                            } else if(NICK_LAST_NAME.equalsIgnoreCase(key)) {
                                prevNickLastName = prevJson.getStr(key);
                            } else if(DODAX_CANDIDATE_ID.equalsIgnoreCase(key)) {
                                prevDodaXId = prevJson.getStr(key);
                            } else if(DODAX_OWNERSHIP_PERIOD.equalsIgnoreCase(key)) {
                                prevDodaXPeriod = prevJson.getStr(key);
                            } else if(delivery.contains(key)) {
                                DeliveryDiff deliveryDiff = deliveryDiffMap.get(key);
                                if(deliveryDiff == null) {
                                    deliveryDiff = new DeliveryDiff();
                                }
                                deliveryDiff.setKey(key);
                                deliveryDiff.setPrev(prevJson.getStr(key));
                                deliveryDiffMap.put(key, deliveryDiff);
                            }
                        }
                    }
                } else if(TALENT_LOCAL_EXTEND_INFO.equals(columnName) && afterColumn.getUpdated()) {
                    JSONObject prevJson = preColumn == null ? new JSONObject() : JSONUtil.parseObj(preColumn.getValue());
                    JSONObject postJson = JSONUtil.parseObj(afterColumn.getValue());
                    JSONArray prevPeriod = prevJson.getJSONArray("sourceOwnershipPeriod");
                    JSONArray postPeriod = postJson.getJSONArray("sourceOwnershipPeriod");

                    boolean periodsEqual = false;

                    // 检查两个数组是否存在且长度相同
                    if (prevPeriod != null && postPeriod != null && prevPeriod.size() == postPeriod.size()) {
                        periodsEqual = true;
                        // 比较数组中的每个元素
                        for (int j = 0; j < prevPeriod.size(); j++) {
                            String prevValue = prevPeriod.getStr(j, "");
                            String postValue = postPeriod.getStr(j, "");

                            if (!prevValue.equals(postValue)) {
                                periodsEqual = false;
                                break;
                            }
                        }
                    }

                    // 如果 sourceOwnershipPeriod 的值不相等，执行相应操作
                    if (!periodsEqual) {
                        log.info("sourceOwnershipPeriod changed from {} to {}", prevPeriod, postPeriod);

                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("key", SOURCE_OWNERSHIP_PERIOD);
                        jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                        //禁猎客户功能：特殊处理experience，需要返回给前端用于判断是否是禁猎改动
                        jsonObject.put("changedFrom", "");
                        jsonObject.put("changedTo", "");
                        esDocuments.add(jsonObject);
                    }


                }
            }
            String prevNickName = CommonUtils.formatFullNameWithBlankCheck(prevNickFirstName, prevNickLastName);
            String postNickName = CommonUtils.formatFullNameWithBlankCheck(postNickFirstName, postNickLastName);
            log.info("[APN: EsFillerTalentRecordService] record talent nickname from:{} to:{}", prevNickName, postNickName);
            if(!prevNickName.equalsIgnoreCase(postNickName)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("key", NICK_NAME);
                jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                jsonObject.put("changedFrom", prevNickName);
                jsonObject.put("changedTo", postNickName);
                esDocuments.add(jsonObject);
            }
            log.info("[APN: EsFillerTalentRecordService] record talent dodaXid from:{} to:{}", prevDodaXId, postDodaXId);
            if(!StringUtils.equals(prevDodaXId, postDodaXId)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("key", DODAX_CANDIDATE_ID);
                jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                if(prevDodaXId != null) {
                    jsonObject.put("changedFrom", prevDodaXId);
                }
                if(postDodaXId != null) {
                    jsonObject.put("changedTo", postDodaXId);
                }
                esDocuments.add(jsonObject);
            }
            log.info("[APN: EsFillerTalentRecordService] record talent dodaXPeriod from:{} to:{}", prevDodaXPeriod, postDodaXPeriod);
            if(!StringUtils.equals(prevDodaXPeriod, postDodaXPeriod)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("key", DODAX_OWNERSHIP_PERIOD);
                jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                if(prevDodaXPeriod != null) {
                    jsonObject.put("changedFrom", prevDodaXPeriod);
                }
                if(postDodaXPeriod != null) {
                    jsonObject.put("changedTo", postDodaXPeriod);
                }
                esDocuments.add(jsonObject);
            }
            for(String key : deliveryDiffMap.keySet()) {
                DeliveryDiff deliveryDiff = deliveryDiffMap.get(key);
                if(deliveryDiff == null) {
                    continue;
                }
                if(!ObjectUtil.equal(deliveryDiff.getPrev(), deliveryDiff.getPost())) {
                    log.info("[APN: EsFillerTalentRecordService] record talent delivery from:{} to:{}", deliveryDiff.getPrev(), deliveryDiff.getPost());
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("key", key);
                    jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                    //不记录具体
                    jsonObject.put("changedFrom", "");
                    jsonObject.put("changedTo", "");
//                    if(deliveryDiff.getPrev() != null) {
//                        jsonObject.put("changedFrom", deliveryDiff.getPrev());
//                    }
//                    if(deliveryDiff.getPost() != null) {
//                        jsonObject.put("changedTo", deliveryDiff.getPost());
//                    }
                    esDocuments.add(jsonObject);
                }
            }
        }
    }

    private String excludeCollegeInfo(String input) {
        if(input == null) {
            return input;
        }
        JSONArray jsonArray = JSONUtil.parseArray(input);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            jsonObject.remove("collegeInfo");
        }
        return jsonArray.toString();
    }

    private String excludeCompanyRelationsInfo(String input) {
        if(input == null) {
            return input;
        }
        JSONArray jsonArray = JSONUtil.parseArray(input);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            jsonObject.remove("businessInfoCompanyId");
            jsonObject.remove("bdCompanyId");
            jsonObject.remove("recogLeadsCompanyId");
            jsonObject.remove("recogCRMAccountId");
            jsonObject.remove("recogCompanyId");
        }
        return jsonArray.toString();
    }

    private boolean recordTalentRecruitmentProcessNode(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<TalentProcessRecord> talentProcessRecords) {
        Set<String> talentLocationFields = new HashSet<>(new ArrayList<>(List.of(NODE_TYPE, NODE_STATUS, TALENT_RECRUITMENT_PROCESS_ID)));
        TalentProcessRecord talentProcessRecord = new TalentProcessRecord();
        if (TALENT_RECRUITMENT_PROCESS_NODE.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (talentLocationFields.contains(columnName)) {
                    switch (columnName) {
                        case NODE_TYPE:
                            NodeType currentNodeType = NodeType.fromDbValue(Integer.valueOf(afterColumn.getValue()));
                            talentProcessRecord.setCurrentNodeType(currentNodeType);
                            break;
                        case NODE_STATUS:
                            if(preColumn != null) {
                                NodeStatus prevStatus = NodeStatus.fromDbValue(Integer.valueOf(preColumn.getValue()));
                                talentProcessRecord.setPrevStatus(prevStatus);
                            }
                            NodeStatus nextStatus = NodeStatus.fromDbValue(Integer.valueOf(afterColumn.getValue()));
                            talentProcessRecord.setNextStatus(nextStatus);
                            break;
                        case TALENT_RECRUITMENT_PROCESS_ID:
                            talentProcessRecord.setTalentProcessId(Long.parseLong(afterColumn.getValue()));
                            break;
                    }
                }
            }
            talentProcessRecords.add(talentProcessRecord);
        }
        return !talentProcessRecords.isEmpty();
    }

    @Override
    public Boolean recordInsertTalentRows(List<Column> preColumns, List<Column> afterColumns, String tableName, Long talentId, String createdTime, String createdBy, List<JSONObject> esDocuments, List<TalentProcessRecord> talentProcessRecords, TalentRecordContextHolder talentRecordContextHolder) {
        Boolean initialTalent = recordNewTalent(preColumns, afterColumns, tableName, false, esDocuments);
        if (initialTalent) {
            return true;
        }
        recordTalentContact(preColumns, afterColumns, tableName, false, esDocuments);
        recordIndustry(preColumns, afterColumns, tableName, false, esDocuments);
        recordJobFunction(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentCurrentLocation(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentLanguage(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentWorkAuthorization(preColumns, afterColumns, tableName, false, esDocuments);
        boolean processNodeChange = recordTalentRecruitmentProcessNode(preColumns, afterColumns, tableName, false, talentProcessRecords);
        recordTalentRecruitmentProcessInterview(preColumns, afterColumns, tableName, false, esDocuments, processNodeChange, talentRecordContextHolder);
        recordTalentResume(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentAdditionInfo(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentNote(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentOwnership(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentConfidential(preColumns, afterColumns, tableName, false, esDocuments);
        recordTalentVoipContact(preColumns, afterColumns, tableName, false, esDocuments);
        return false;
    }


    private void recordTalentRecruitmentProcessInterview(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments, boolean processNodeChange, TalentRecordContextHolder talentRecordContextHolder) {
        Set<String> talentLocationFields = new HashSet<>(new ArrayList<>(List.of(PROGRESS, TALENT_RECRUITMENT_PROCESS_ID)));
        if (TALENT_RECRUITMENT_PROCESS_INTERVIEW.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("eventType", LogEventType.EVENT_STATUS_UPDATE);
            jsonObject.put("changedFrom", "");
            for (Column afterColumn : afterColumns) {
                String columnName = afterColumn.getName();
                if (talentLocationFields.contains(columnName)) {
                    switch (columnName) {
                        case PROGRESS:
                            jsonObject.put("changedTo", NodeType.INTERVIEW.name() + "-" + afterColumn.getValue());
                            break;
                        case TALENT_RECRUITMENT_PROCESS_ID:
                            Long processId = Long.valueOf(afterColumn.getValue());
                            String jobName = talentRecordContextHolder.getTalentRecruitmentProcessJobNameMap().get(processId);
                            jsonObject.put("key", jobName);
                            jsonObject.put("fieldId", processId);
                            break;
                    }
                }
            }
            if(!processNodeChange) {
                esDocuments.add(jsonObject);
            }
        }
    }

    @Override
    public List<JSONObject> recordUpdateTalentRows(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, Long talentId, String createdTime, String createdBy, List<JSONObject> esDocuments, List<TalentProcessRecord> talentProcessRecords, TalentRecordContextHolder talentRecordContextHolder) {
        recordTalent(preColumns, afterColumns, tableName, update, esDocuments);
        recordTalentContact(preColumns, afterColumns, tableName, update, esDocuments);
        recordIndustry(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobFunction(preColumns, afterColumns, tableName, update, esDocuments);
        recordTalentCurrentLocation(preColumns, afterColumns, tableName, update, esDocuments);
        recordTalentLanguage(preColumns, afterColumns, tableName, update, esDocuments);
        recordTalentWorkAuthorization(preColumns, afterColumns, tableName, update, esDocuments);
        boolean processNodeChange = recordTalentRecruitmentProcessNode(preColumns, afterColumns, tableName, update, talentProcessRecords);
        recordTalentRecruitmentProcessInterview(preColumns, afterColumns, tableName, update, esDocuments, processNodeChange, talentRecordContextHolder);
        recordTalentResume(preColumns, afterColumns, tableName, update, esDocuments);
        recordTalentAdditionInfo(preColumns, afterColumns, tableName, update, esDocuments);
        recordTalentNote(preColumns, afterColumns, tableName, update, esDocuments);
        recordTalentOwnership(preColumns, afterColumns, tableName, update, esDocuments);
        recordSubmitToJobTalentResume(preColumns, afterColumns, tableName, update, esDocuments, talentRecordContextHolder);
        recordTalentConfidential(preColumns, afterColumns, tableName, update, esDocuments);
        return esDocuments;
    }

    private void recordSubmitToJobTalentResume(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments, TalentRecordContextHolder talentRecordContextHolder) {
        if (TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (TALENT_RESUME_RELATION_ID.contains(columnName) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", SUBMIT_TO_JOB_RESUME_CHANGE);
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", getResumeFileName(preColumn, talentRecordContextHolder));
                        esDocumentJson.put("changedTo", getResumeFileName(afterColumn, talentRecordContextHolder));

                        log.info("[APN: EsFillerTalentRecordService] record talent submit to job resume change: {}", JSON.toJSON(esDocumentJson));
                        esDocuments.add(esDocumentJson);
                    }
                }
            }
        }
    }

    @Resource
    private TalentResumeRelationRepository talentResumeRelationRepository;

    private String getResumeFileName(Column column, TalentRecordContextHolder talentRecordContextHolder) {
        if(column == null) {
            return "";
        }
        Long talentResumeRelationId = Long.valueOf(column.getValue());
        String relation = talentRecordContextHolder.getSubmitToJobResumeFileNameMap().get(talentResumeRelationId);
        return relation != null ? relation : "";
    }

    private void recordTalentOwnership(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (TALENT_OWNERSHIP.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (OWNERSHIP_TYPE.equals(columnName) && afterColumn.getUpdated()) {
                    if(Integer.parseInt(afterColumn.getValue()) == TalentOwnershipType.TALENT_OWNER.toDbValue()) {
                        esDocumentJson.put("key", OWNERSHIP_TALENT_OWNER);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", "");
                    } else if(Integer.parseInt(afterColumn.getValue()) == TalentOwnershipType.SHARE.toDbValue()) {
                        esDocumentJson.put("key", OWNERSHIP);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", "");
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent ownership change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordTalentConfidential(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (!TALENT_CONFIDENTIAL.equals(tableName)) {
           return;
        }
        checkColumnMatch(preColumns, afterColumns, update);
        Consumer<Void> recordEnterConfidential = unused -> {
            JSONObject esDocumentJson = new JSONObject();
            Optional<String> confidentialStartTime = afterColumns.stream()
                    .filter(column -> "confidential_start_time".equals(column.getName())).findAny()
                    .map(Column::getValue);
            Optional<String> confidentialEndTime = afterColumns.stream()
                    .filter(column -> "confidential_end_time".equals(column.getName())).findAny()
                    .map(Column::getValue);
            if (confidentialStartTime.isPresent() && confidentialEndTime.isPresent()) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");

                // 解析字符串为 LocalDateTime 对象
                LocalDateTime startDateTime = LocalDateTime.parse(confidentialStartTime.get(), formatter);
                LocalDateTime endDateTime = LocalDateTime.parse(confidentialEndTime.get(), formatter);

                // 计算两个日期之间的天数差
                long confidentialDays = ChronoUnit.DAYS.between(startDateTime, endDateTime);
                esDocumentJson.put("key", "confidentialDays");
                esDocumentJson.put("eventType", CONFIDENTIAL);
                esDocumentJson.put("changedFrom", "");
                esDocumentJson.put("changedTo", confidentialDays);
                esDocuments.add(esDocumentJson);
            }
        };
        // insert, 进入保密状态
        if (!update) {
            recordEnterConfidential.accept(null);
        } else {
            for (Column afterColumn : afterColumns) {
                String columnName = afterColumn.getName();
                if ("active".equals(columnName) && afterColumn.getUpdated()) {
                    if (BooleanUtils.toBoolean(afterColumn.getValue())) {
                        recordEnterConfidential.accept(null);
                    } else {
                        afterColumns.stream()
                            .filter(column -> "declassify_reason".equals(column.getName()))
                            .findAny().ifPresent(declassifyReasonColumn -> {
                                JSONObject esDocumentJson = new JSONObject();
                                esDocumentJson.put("key", "declassifyReason");
                                esDocumentJson.put("eventType", DECLASSIFY);
                                esDocumentJson.put("changedFrom", "");
                                esDocumentJson.put("changedTo", declassifyReasonColumn.getValue());
                                esDocuments.add(esDocumentJson);
                            });
                    }
                } else if ("confidential_owner".equals(columnName) && afterColumn.getUpdated()) {
                    JSONObject esDocumentJson = new JSONObject();
                    esDocumentJson.put("key", "confidential_owner");
                    esDocumentJson.put("eventType", CONFIDENTIAL_HANDOVER);
                    esDocumentJson.put("changedFrom", preColumns.stream().filter(c -> "confidential_owner".equals(c.getName())).map(Column::getValue).findFirst().orElse(""));
                    esDocumentJson.put("changedTo", afterColumn.getValue());
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordIndustry(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> fields = new HashSet<>(new ArrayList<>(List.of(TALENT_INDUSTRY_ID, TALENT_ID)));
        if (TALENT_INDUSTRY_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (Column column : afterColumns) {
                JSONObject esDocumentJson = new JSONObject();
                String columnName = column.getName();
                if (fields.contains(columnName) && column.getUpdated()) {
                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_INDUSTRY_ID));
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);

                    } else {
                        //esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE); // insert also regarded as update
                    }
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", "");

                    log.info("[APN: EsFillerTalentRecordService] record talent job function change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordTalentNote(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (TALENT_NOTE.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            List<JSONObject> notes = new ArrayList<>();
            int noteId = -1;
            TalentNoteType noteTypeFrom = null;
            String otherContactInfoFrom = "";
            TalentNoteType noteTypeTo = null;
            String otherContactInfoTo = "";
            boolean noteTypeChange = false;

            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (NOTE.equals(columnName) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", NOTE);
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", preColumn.getValue());
                        esDocumentJson.put("changedTo", afterColumn.getValue());
                    } else {
                        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", afterColumn.getValue());
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent note change: {}", JSON.toJSON(esDocumentJson));
                    notes.add(esDocumentJson);
                }
                if (NOTE_STATUS.equals(columnName) && afterColumn.getUpdated()) {
                    if (!update) {
                        String value = afterColumn.getValue();
                        String motivationName = findMotivationName(value);
                        esDocumentJson.put("key", convertSnakeCaseToCamelCase(NOTE_MOTIVATION_ID));
                        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", motivationName);
                        log.info("[APN: EsFillerTalentRecordService] record talent note motivation change: {}", JSON.toJSON(esDocumentJson));
                        notes.add(esDocumentJson);
                    }
                }
                if (NOTE_TITLE.equals(columnName) && afterColumn.getUpdated()) {
                    if(update) {
                        if(!ObjectUtil.equal(preColumn.getValue(), afterColumn.getValue())) {
                            esDocumentJson.put("key", convertSnakeCaseToCamelCase(columnName));
                            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                            esDocumentJson.put("changedFrom", preColumn.getValue());
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            notes.add(esDocumentJson);
                        }
                    } else {
                        if(StringUtils.isNotEmpty(afterColumn.getValue())) {
                            esDocumentJson.put("key", convertSnakeCaseToCamelCase(columnName));
                            esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                            esDocumentJson.put("changedFrom", "");
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            notes.add(esDocumentJson);
                        }
                    }
                }
                if (NOTE_PRIORITY.equals(columnName) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(columnName));
                    esDocumentJson.put("eventType", update ? EVENT_TYPE_UPDATE : EVENT_TYPE_INSERT);
                    esDocumentJson.put("changedFrom", update ? NotePriority.fromDbValue(Integer.parseInt(preColumn.getValue())) : "");
                    esDocumentJson.put("changedTo", NotePriority.fromDbValue(Integer.parseInt(afterColumn.getValue())));
                    notes.add(esDocumentJson);
                }
                if (NOTE_TYPE.equals(columnName)) {
                    noteTypeChange = noteTypeChange || afterColumn.getUpdated();
                    if(preColumn != null) {
                        String preVal = preColumn.getValue();
                        if(StringUtils.isNotEmpty(preVal)) {
                            noteTypeFrom = TalentNoteType.fromDbValue(Integer.parseInt(preVal));
                        }
                    }
                    String afterValue = afterColumn.getValue();
                    if(StringUtils.isNotEmpty(afterValue)) {
                        noteTypeTo = TalentNoteType.fromDbValue(Integer.parseInt(afterValue));
                    }
                }
                if (ADDITIONAL_INFO.equals(columnName)) {
                    noteTypeChange = noteTypeChange || afterColumn.getUpdated();
                    if(preColumn != null) {
                        String preVal = preColumn.getValue();
                        if(StringUtils.isNotEmpty(preVal)) {
                            JSONObject preObject = JSONUtil.parseObj(preVal);
                            otherContactInfoFrom = preObject.getStr("otherContactInfo");
                        }
                    }
                    String afterValue = afterColumn.getValue();
                    if(StringUtils.isNotEmpty(afterValue)) {
                        JSONObject afterObject = JSONUtil.parseObj(afterValue);
                        otherContactInfoTo = afterObject.getStr("otherContactInfo");
                    }
                }

                if (ID.equals(columnName)) {
                    String value = afterColumn.getValue();
                    if(StringUtils.isNotEmpty(value)) {
                        noteId = Integer.parseInt(value);
                    }
                }
            }
            if(noteId > 0) {
                for(JSONObject jsonObject : notes) {
                    jsonObject.put("fieldId", noteId);
                }
            }
            if(noteTypeChange) {
                JSONObject esDocumentJson = new JSONObject();
                esDocumentJson.put("key", convertSnakeCaseToCamelCase(NOTE_TYPE));
                esDocumentJson.put("eventType", update ? EVENT_TYPE_UPDATE : EVENT_TYPE_INSERT);
                esDocumentJson.put("changedFrom", update ? getNoteTypeChangedDesc(noteTypeFrom, otherContactInfoFrom) : "");
                esDocumentJson.put("changedTo", getNoteTypeChangedDesc(noteTypeTo, otherContactInfoTo));
                if(!Objects.equals(esDocumentJson.getStr("changedFrom"), esDocumentJson.getStr("changedTo"))) {
                    notes.add(esDocumentJson);
                }
            }
            esDocuments.addAll(notes);
        }
    }

    private void recordTalentVoipContact(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (TALENT_VOIP_CONTACT.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            List<JSONObject> notes = new ArrayList<>();
            int voipPhoneCallId = -1;


            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (TALENT_VOIP_CONTACT_PHONE.equals(columnName) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", TALENT_VOIP_CALL);
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", preColumn.getValue());
                        esDocumentJson.put("changedTo", afterColumn.getValue());
                    } else {
                        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", afterColumn.getValue());
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent voip contact change: {}", JSON.toJSON(esDocumentJson));
                    notes.add(esDocumentJson);
                }
                if (ID.equals(columnName)) {
                    String value = afterColumn.getValue();
                    if(StringUtils.isNotEmpty(value)) {
                        voipPhoneCallId = Integer.parseInt(value);
                    }
                }
            }
            if(voipPhoneCallId > 0) {
                for(JSONObject jsonObject : notes) {
                    jsonObject.put("fieldId", voipPhoneCallId);
                }
            }
            esDocuments.addAll(notes);
        }
    }

    private String getNoteTypeChangedDesc(TalentNoteType noteType, String otherContactInfo) {
        if(noteType == null) {
            return "";
        }
        if(!TalentNoteType.OTHERS.name().equals(noteType.name())) {
            return noteType.name();
        }
        return StringUtils.isEmpty(otherContactInfo) ? noteType.name() : noteType.name() + "(" + otherContactInfo + ")";
    }


    @Override
    public Long recordTalentId(List<Column> preColumns, List<Column> afterColumns, String tableName, TalentRecordContextHolder talentRecordContextHolder) {
        Set<String> talentTables = new HashSet<>(new ArrayList<>(List.of(
                TALENT_CONTACT,
                TALENT_CURRENT_LOCATION,
                TALENT_INDUSTRY_RELATION,
                TALENT_JOB_FUNCTION_RELATION,
                TALENT_LANGUAGE_RELATION,
                TALENT_RESUME_RELATION,
                TALENT_WORK_AUTHORIZATION_RELATION,
                TALENT_NOTE,
                TALENT_OWNERSHIP,
                TALENT_CONFIDENTIAL,
                TALENT_VOIP_CONTACT)));
        if (tableName.equals(TALENT)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(ID) && !column.getValue().isEmpty()) {
                    return Long.valueOf(column.getValue());
                }
            }
            for (Column column : preColumns) {
                String columnName = column.getName();
                if (columnName.equals(ID) && !column.getValue().isEmpty()) {
                    return Long.valueOf(column.getValue());
                }
            }
        }

        if (talentTables.contains(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(TALENT_ID) && !column.getValue().isEmpty()) {
                    return Long.valueOf(column.getValue());
                }
            }
            for (Column column : preColumns) {
                String columnName = column.getName();
                if (columnName.equals(TALENT_ID) && !column.getValue().isEmpty()) {
                    return Long.valueOf(column.getValue());
                }
            }
        }
        if (TALENT_RECRUITMENT_PROCESS_INTERVIEW.equals(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(TALENT_RECRUITMENT_PROCESS_ID) && !column.getValue().isEmpty()) {
                    return talentRecordContextHolder.getRecruitmentProcessTalentIdMap().get(Long.valueOf(column.getValue()));
                }
            }
        }
        if (TALENT_RECRUITMENT_PROCESS_NODE.equals(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(TALENT_RECRUITMENT_PROCESS_ID) && !column.getValue().isEmpty()) {
                    return talentRecordContextHolder.getRecruitmentProcessTalentIdMap().get(Long.valueOf(column.getValue()));
                }
            }
        }
        if (TALENT_ADDITIONAL_INFO.equals(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(ID) && !column.getValue().isEmpty()) {
                    return talentRecordContextHolder.getTalentAdditional2TalentIdMap().get(Long.valueOf(column.getValue()));
                }
            }
        }
        if (TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB.equals(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(TALENT_RECRUITMENT_PROCESS_ID) && !column.getValue().isEmpty()) {
                    return talentRecordContextHolder.getRecruitmentProcessTalentIdMap().get(Long.valueOf(column.getValue()));
                }
            }
        }
        return null;
    }

    @Override
    public String recordCreatedTime(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdTime) {
        Set<String> talentTables = new HashSet<>(new ArrayList<>(List.of(
                TALENT,
                TALENT_ADDITIONAL_INFO,
                TALENT_CONTACT,
                TALENT_CURRENT_LOCATION,
                TALENT_INDUSTRY_RELATION,
                TALENT_JOB_FUNCTION_RELATION,
                TALENT_LANGUAGE_RELATION,
                TALENT_RESUME_RELATION,
                TALENT_WORK_AUTHORIZATION_RELATION,
                TALENT_RECRUITMENT_PROCESS_NODE,
                TALENT_RECRUITMENT_PROCESS_INTERVIEW,
                TALENT_NOTE,
                TALENT_OWNERSHIP,
                TALENT_CONFIDENTIAL)));
        if (talentTables.contains(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(LAST_MODIFIED_DATE)) {
                    log.info("TraceId SpanId record CreatedTime get last modify date format table: {} value: {}", tableName, column.getValue());
                    if(StringUtils.isEmpty(column.getValue())) {
                        return null;
                    }
                    Instant change = formatTime(column.getValue());
                    if(Objects.equals(createdTime, "")) {
                        createdTime = change.toString();
                    } else {
                        Instant old = Instant.parse(createdTime);
                        if(old.compareTo(change) < 0) {
                            createdTime = change.toString();
                        }
                    }
                    log.info("TraceId SpanId record CreatedTime get last modify date format {}", createdTime);
                }
            }
        }
        return createdTime;
    }

    @Override
    public String recordCreatedBy(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdBy, RecordDataInfo recordDataInfo) {
        Set<String> talentTables = new HashSet<>(new ArrayList<>(List.of(
                TALENT,
                TALENT_ADDITIONAL_INFO,
                TALENT_CONTACT,
                TALENT_CURRENT_LOCATION,
                TALENT_INDUSTRY_RELATION,
                TALENT_JOB_FUNCTION_RELATION,
                TALENT_LANGUAGE_RELATION,
                TALENT_RESUME_RELATION,
                TALENT_WORK_AUTHORIZATION_RELATION,
                TALENT_RECRUITMENT_PROCESS_NODE,
                TALENT_RECRUITMENT_PROCESS_INTERVIEW,
                TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB,
                TALENT_NOTE,
                TALENT_OWNERSHIP,
                TALENT_CONFIDENTIAL,
                TALENT_VOIP_CONTACT)));
        if (StringUtils.isEmpty(createdBy) && talentTables.contains(tableName)) {
            boolean modified = false;
            String tempCreatedBy = "";
            String lastModifiedBy = "";
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(LAST_MODIFIED_BY)) {
                    lastModifiedBy = column.getValue();
                } else if (columnName.equals(LAST_MODIFIED_DATE) && column.getUpdated() && (!column.getIsNull() && StringUtils.isNotBlank(column.getValue()))) {
                    modified = true;
                } else if (columnName.equals(TENANT_ID)) {
                    if(StringUtils.isNotEmpty(column.getValue())) {
                        recordDataInfo.setTenantId(Long.valueOf(column.getValue()));
                    }
                } else if (columnName.equals(CREATED_BY)) {
                    if(StringUtils.isNotEmpty(column.getValue())) {
                        recordDataInfo.setTenantId(getTenantIdFromCreatedBy(column.getValue()));
                        tempCreatedBy = column.getValue();
                    }
                }
            }
            log.info("recordCreatedBy, modified:{}, tempCreatedBy: {}, lastModifiedBy: {}, createdBy:{}, tablename:{}", modified, tempCreatedBy, lastModifiedBy, createdBy, tableName);
            if(StringUtils.isEmpty(lastModifiedBy)) {
                lastModifiedBy = tempCreatedBy;
            }
            createdBy = modified ? lastModifiedBy : createdBy;
        }

        return createdBy;
    }

    private Long getTenantIdFromCreatedBy(String createdBy) {
        //看是1965650_20014_0格式还是1154,20014格式
        if (StringUtils.isNotEmpty(createdBy)) {
            if (SimpleUserAspect.isTimesheetUser(createdBy)) {
                String[] idStrings = createdBy.split("_");
                if (idStrings.length == 3) {
                    return Long.parseLong(idStrings[1]);
                }
            } else {
                String[] idStrings = createdBy.split(",");
                if (idStrings.length == 2) {
                    return Long.parseLong(idStrings[1]);
                }
            }
        }

        return null;
    }

    @Override
    public boolean isMonitoredTable(String tableName) {
        Set<String> talentTables = new HashSet<>(new ArrayList<>(List.of(
                TALENT,
                TALENT_ADDITIONAL_INFO,
                TALENT_CONTACT,
                TALENT_CURRENT_LOCATION,
                TALENT_INDUSTRY_RELATION,
                TALENT_JOB_FUNCTION_RELATION,
                TALENT_LANGUAGE_RELATION,
                TALENT_RESUME_RELATION,
                TALENT_WORK_AUTHORIZATION_RELATION,
                TALENT_RECRUITMENT_PROCESS_NODE)));
        return talentTables.contains(tableName);
    }

    @Override
    public void recordDeleteTalentRows(List<Column> beforeColumnsList, List<Column> afterColumnsList, String tableName, List<JSONObject> esDocuments) {
        if (TALENT_NOTE.equals(tableName)) {
            JSONObject esDocumentJson = new JSONObject();
            for (int i = 0; i < beforeColumnsList.size(); i++) {
                Column preColumn = beforeColumnsList.get(i);
                String columnName = preColumn.getName();
                if (NOTE.equals(columnName)) {
                    esDocumentJson.put("changedFrom", HtmlUtil.cleanHtmlTag(preColumn.getValue()));
                } else if(ID.equals(columnName)) {
                    esDocumentJson.put("fieldId", preColumn.getValue());
                }
            }
            esDocumentJson.put("key", convertSnakeCaseToCamelCase(NOTE));
            esDocumentJson.put("eventType", EVENT_TYPE_DELETE);
            esDocumentJson.put("changedTo", "");
            log.info("[APN: EsFillerTalentRecordService] record talent note delete: {}", JSON.toJSON(esDocumentJson));
            esDocuments.add(esDocumentJson);
        }
        if (TALENT_INDUSTRY_RELATION.equals(tableName)) {
            JSONObject esDocumentJson = new JSONObject();
            esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_INDUSTRY_ID));
            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
            esDocumentJson.put("changedFrom", "");
            esDocumentJson.put("changedTo", "");
            log.info("[APN: EsFillerTalentRecordService] record talent industry delete: {}", JSON.toJSON(esDocumentJson));
            esDocuments.add(esDocumentJson);
        }
        if (TALENT_JOB_FUNCTION_RELATION.equals(tableName)) {
            JSONObject esDocumentJson = new JSONObject();
            esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_JOB_FUNCTION_ID));
            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
            esDocumentJson.put("changedFrom", "");
            esDocumentJson.put("changedTo", "");
            log.info("[APN: EsFillerTalentRecordService] record talent job function delete: {}", JSON.toJSON(esDocumentJson));
            esDocuments.add(esDocumentJson);
        }
        if (TALENT_LANGUAGE_RELATION.equals(tableName)) {
            JSONObject esDocumentJson = new JSONObject();
            esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_LANGUAGE_ID));
            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
            esDocumentJson.put("changedFrom", "");
            esDocumentJson.put("changedTo", "");
            log.info("[APN: EsFillerTalentRecordService] record talent language delete: {}", JSON.toJSON(esDocumentJson));
            esDocuments.add(esDocumentJson);
        }
        if (TALENT_WORK_AUTHORIZATION_RELATION.equals(tableName)) {
            JSONObject esDocumentJson = new JSONObject();
            esDocumentJson.put("key", convertSnakeCaseToCamelCase(TALENT_WORK_AUTHORIZATION_ID));
            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
            esDocumentJson.put("changedFrom", "");
            esDocumentJson.put("changedTo", "");
            log.info("[APN: EsFillerTalentRecordService] record talent work authorization delete: {}", JSON.toJSON(esDocumentJson));
            esDocuments.add(esDocumentJson);
        }
        if (TALENT_OWNERSHIP.equals(tableName)) {
            for (int i = 0; i < beforeColumnsList.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = beforeColumnsList.get(i);
                String columnName = preColumn.getName();
                if (OWNERSHIP_TYPE.equals(columnName)) {
                    if(Integer.parseInt(preColumn.getValue()) == TalentOwnershipType.TALENT_OWNER.toDbValue()) {
                        esDocumentJson.put("key", OWNERSHIP_TALENT_OWNER);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", "");
                    } else if(Integer.parseInt(preColumn.getValue()) == TalentOwnershipType.SHARE.toDbValue()) {
                        esDocumentJson.put("key", OWNERSHIP);
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", "");
                    }
                    log.info("[APN: EsFillerTalentRecordService] record talent ownership change: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

}
