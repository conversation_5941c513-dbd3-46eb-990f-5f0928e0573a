package com.altomni.apn.common.service.cache.crmenums;

import cn.hutool.core.collection.CollectionUtil;
import com.altomni.apn.common.domain.crmenums.*;
import com.altomni.apn.common.repository.crmenums.*;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@EnableCaching
@CacheConfig(cacheNames = {"enum_dict"}, cacheManager = "concurrentMapCacheManager")
public class EnumCrmCommonService {

    @Resource
    private EnumBusinessProgressRepository enumBusinessProgressRepository;

    @Resource
    private EnumCompanyClientLevelRepository enumCompanyClientLevelRepository;

    @Resource
    private EnumCompanyContactCategoryRepository enumCompanyContactCategoryRepository;

    @Resource
    private EnumContactTypeRepository enumContactTypeRepository;

    @Resource
    private EnumFollowUpContactTypeRepository enumFollowUpContactTypeRepository;

    @Cacheable(key = "'enumBusinessProgress:'", unless = "#result == null")
    public List<EnumBusinessProgress> findAllEnumBusinessProgress() {
        List<EnumBusinessProgress> result = enumBusinessProgressRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'companyClientLevel'", unless = "#result == null")
    public List<EnumCompanyClientLevel> findAllEnumCompanyClientLevel() {
        List<EnumCompanyClientLevel> result = enumCompanyClientLevelRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'companyContactCategory'", unless = "#result == null")
    public List<EnumCompanyContactCategory> findAllEnumCompanyContactCategory() {
        List<EnumCompanyContactCategory> result = enumCompanyContactCategoryRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'contactType'", unless = "#result == null")
    public List<EnumContactType> findAllEnumContactType() {
        List<EnumContactType> result = enumContactTypeRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'contactTypeMap'", unless = "#result == null")
    public Map<Integer, EnumContactType> findAllEnumContactTypeMap() {
        Map<Integer, EnumContactType> result = enumContactTypeRepository.findAll().stream().collect(Collectors.toMap(EnumContactType::getId, Function.identity()));
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'followUpContactType'", unless = "#result == null")
    public List<EnumFollowUpContactType> findAllEnumFollowUpContactType() {
        List<EnumFollowUpContactType> result = enumFollowUpContactTypeRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

}
