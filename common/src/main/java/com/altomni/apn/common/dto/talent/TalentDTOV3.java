package com.altomni.apn.common.dto.talent;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.talent.CreationTalentType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.company.ClientContactDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.persistence.Transient;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;



@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentDTOV3 extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    private Long tenantId;

    @ApiModelProperty(value = "common talent id in elastic search")
    private String esId;

    @ApiModelProperty(value = "first name. required in US.")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.")
    private String lastName;

    @ApiModelProperty(value = "nick first name.")
    private String nickFirstName;

    @ApiModelProperty(value = "nick last name.")
    private String nickLastName;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
            "the full name can not be null.")
    private String fullName;

    @ApiModelProperty(value = "Talent gender enum name.")
    private String gender;
    //种族/族裔
    private List<String> ethnicity;
    //偏好的人称代词
    private String preferredPronoun;
    //是否为残障人士
    private String disability;
    //退伍军人身份
    private String veteran;
    //是否为 LGBTQ+ 群体
    private String memberOfLGBTQ;

    @ApiModelProperty(value = "Talent birthday(yyyy-MM-dd).")
    private String birthday;

    @ApiModelProperty(value = "Talent job motivation enum id.")
    private Integer motivationId;

    @ApiModelProperty(value = "url link to photo")
    private String photoUrl;

    @ApiModelProperty(value = "talent creation types", allowableValues = "UPLOAD_WITH_RESUME, CREATE_WITHOUT_RESUME, BULK_UPLOAD_RESUMES")
    private CreationTalentType creationTalentType;

    @ApiModelProperty(value = "Talent current location. Save to talent basic information table.")
    private LocationDTO currentLocation;


    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> skills;

    /**
     * Additional Info
     */
    @ApiModelProperty(value = "Talent expected salary currency")
    private String currency;


    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "Talent current salary range")
    private RangeDTO salaryRange;

    //猎聘生成简历使用
    private Double payTimes;

    private List<TalentPreference> preferences;

//    @ApiModelProperty(value = "Talent preferred locations to work. May include the current location.")
//    private List<LocationDTO> preferredLocations;
//    @ApiModelProperty(value = "Talent preferred salary currency")
//    private String preferredCurrency;
//    @ApiModelProperty(value = "salary rate unit")
//    private RateUnitType preferredPayType;
//
//    private Double preferredPayTimes;
//
//    @ApiModelProperty(value = "preferred salary range")
//    private RangeDTO preferredSalaryRange;

//    @ApiModelProperty(value = "preferred salary range only use in common pool")
//    private RangeDTO preferredAnnualSalaryInUSD;

    @ApiModelProperty(value = "sourcing channel")
    private ResumeSourceType source;

    //source为Arrivals或Softsource的话 需要【Ownership Period】
    private List<String> sourceOwnershipPeriod;

    //如果source是agency时，此项有值
    private Long agencyId;

    @ApiModelProperty(value = "salary range only use in common pool")
    private RangeDTO annualSalaryInUSD;


    @ApiModelProperty(value = "work authorization")
    private List<EnumRelationDTO> workAuthorization;

    @ApiModelProperty(value = "job functions, load data in biz_dict service")
    private List<EnumRelationDTO> jobFunctions;

    @ApiModelProperty(value = "languages, load data in biz_dict service")
    private List<EnumRelationDTO> languages;

    @ApiModelProperty(value = "industries talent specified in same type")
    private List<EnumRelationDTO> industries;

    @ApiModelProperty(value = "list of talent contacts. Show up when fetch a single talent profile. Read Only. Create/update using the TalentContact entity.")
    private List<TalentContactDTO> contacts;

    @ApiModelProperty(value = "list of talent experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentExperienceDTO> experiences;

    @ApiModelProperty(value = "list of talent educations. Show up when fetch a single talent profile. Read Only. Create/update using the TalentEducationDTO entity.")
    private List<TalentEducationDTO> educations;

    @ApiModelProperty(value = "list of talent project experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentProjectDTO> projects;

    @ApiModelProperty(value = "list of talent notes created by users. Show up when fetch a single talent profile. Read Only. Create/update using the TalentNot entity.")
    private List<TalentNoteDTO> notes;

    @ApiModelProperty(value = "list of talent review notes created by users.")
    private List<TalentReviewNoteDTO> reviewNotes;

    @ApiModelProperty(value = "list of talent resumes. Show up when fetch a single talent profile. Read Only. Create/update using the TalentResume entity.")
    private List<TalentResumeDTO> resumes;

    @ApiModelProperty(value = "The person who have ownership for this talent will get commission when this talent on board")
    private List<TalentOwnershipDTO> ownerships;

    @ApiModelProperty(value = "list of talent certificate")
    private List<TalentCertificateDTO> certificates;

    @ApiModelProperty(value = "The number of applications.")
    private Integer applicationCount;

    @ApiModelProperty(value = "whether talent is purchased from common db. Default is false.")
    private Boolean purchased = false;

    /**
     * only use in timesheet profile
     */
    @ApiModelProperty(value = "only use in jobDiva MyProfile, addressLine")
    private String addressLine;

    @ApiModelProperty(value = "only use in jobDiva MyProfile, zipCode")
    private String zipCode;

    @ApiModelProperty(value = "only use in Portal Account Management")
    private Boolean isAM;

    @ApiModelProperty(value = "only use for common pool")
    private Long creditTransactionId;

    private Long additionalInfoId;

    @ApiModelProperty(value = "hotListId big health")
    private Long hotListId;

    @ApiModelProperty(value = "id for company location")
    private Long companyLocationId;

    //客户联系人的公司id
    private Long clientContactCompanyId;

    //公司联系人在所属公司下是否活跃
    private List<ClientContactDTO> companyAffiliations;

    //创建/编辑候选人时，入参pop出关系型数据后，和redis/db合并后生成的extendedinfo，为了保留parser中后端用不到的key
    private String extendedInfo;

    //自我评价
    private String selfEvaluation;

    //从推荐添加至我的候选人时使用
    private RecommendFeedback recommendFeedback;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    @ApiModelProperty(value = "periodOfStay")
    private LocalDate periodOfStay;

    // 候选人保密归属信息
    private ConfidentialInfoDto confidentialInfo;
    // 当前候选人是否有保密查看权限
    private Boolean confidentialTalentViewAble;

    @Transient
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SimpleUser createdUser;

    @Transient
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SimpleUser lastModifiedUser;
    public static TalentDTOV3 fromTalent(TalentV3 t) {
        return fromTalent(t, new HashSet<>());
    }

    public static TalentDTOV3 fromTalent(TalentV3 t, Set<Long> resignedApplicationIds) {
        TalentDTOV3 dto = Convert.convert(TalentDTOV3.class, t);
        Set<String> skips = Arrays.stream(ReflectUtil.getFieldsDirectly(TalentV3.class, true)).map(Field::getName).collect(Collectors.toSet());
        TalentDTOV3 additionalInfoExpand = Convert.convert(TalentDTOV3.class, JSONObject.parseObject(t.getTalentExtendedInfo()));
        if (additionalInfoExpand != null) {
            additionalInfoExpand.setExperiences(new ArrayList<>(removeFutureExperiences(additionalInfoExpand.getExperiences(), resignedApplicationIds)));
            ServiceUtils.myCopyProperties(additionalInfoExpand, dto, skips);
        }
        TalentDTOV3 additionalInfoLocalExpand = Convert.convert(TalentDTOV3.class, JSONObject.parseObject(t.getTalentLocalExtendedInfo()));
        if (additionalInfoLocalExpand != null) {
            ServiceUtils.myCopyProperties(additionalInfoLocalExpand, dto, skips);
        }

        if (ObjectUtils.isNotEmpty(dto.getOwnerships())) {
            dto.getOwnerships().removeIf(o -> Instant.now().compareTo(o.getExpireTime()) > 0); //remove expired ownerships (i.e., more than 3 days for APN)
        }
        dto.setAdditionalInfoId(t.getAdditionalInfoId());

        return dto;
    }

    /**
     * 如果当前时间早于start date，则不返回给前端
     * @param experiences 所有的experience
     * @return 没有start date或者start date不晚于当前时间的experience
     */
    private static List<TalentExperienceDTO> removeFutureExperiences(List<TalentExperienceDTO> experiences, Set<Long> resignedApplicationIds){
        if (CollectionUtils.isEmpty(experiences)){
            return new ArrayList<>();
        }
        return experiences.stream()
                .filter(e -> Objects.isNull(e.getStartDate()) || e.getStartDate().isBefore(LocalDate.now()) || e.getStartDate().isEqual(LocalDate.now()))
                // 如果 experience 里没有流程 ID，说明不是系统自动增加的 experience， 可以删除
                // 如果该流程有离职记录，则可以删除该 experience
                .map(e -> e.setDeletable(Objects.isNull(e.getTalentRecruitmentProcessId()) || resignedApplicationIds.contains(e.getTalentRecruitmentProcessId())))
                .peek(e -> System.out.println("test...="+e))
                .toList();
    }

    public static TalentDTOV3 getNonRelationData(TalentDTOV3 dto) {
        //set extended info
        TalentDTOV3 nonRelationData = new TalentDTOV3();
        nonRelationData.setProjects(dto.getProjects());
        nonRelationData.setSource(dto.getSource());
//        nonRelationData.setPreferredCurrency(dto.getPreferredCurrency());
//        nonRelationData.setPreferredPayType(dto.getPreferredPayType());
//        nonRelationData.setPreferredSalaryRange(dto.getPreferredSalaryRange());
//        nonRelationData.setPreferredLocations(dto.getPreferredLocations());
        nonRelationData.setEducations(dto.getEducations());
        nonRelationData.setSkills(dto.getSkills());
        nonRelationData.setPayType(dto.getPayType());
//        nonRelationData.setPayTimes(dto.getPayTimes());
        nonRelationData.setCurrency(dto.getCurrency());
//        nonRelationData.setPreferredAnnualSalaryInUSD(dto.getPreferredAnnualSalaryInUSD());
        nonRelationData.setSalaryRange(dto.getSalaryRange());
        //排序后传入，为了report模块的搜索，只去experience[0]作为公司和职位显示
        List<TalentExperienceDTO> experiences = dto.getExperiences();
        if(experiences != null) {
            List<TalentExperienceDTO> sortExperience = experiences.stream()
                    .sorted(TalentExperienceDTO.COMPARATOR)
                    .collect(Collectors.toList());
            nonRelationData.setExperiences(sortExperience);
        }
        nonRelationData.setAnnualSalaryInUSD(dto.getAnnualSalaryInUSD());
        nonRelationData.setCertificates(dto.getCertificates());
        nonRelationData.setNickFirstName(dto.getNickFirstName());
        nonRelationData.setNickLastName(dto.getNickLastName());
        nonRelationData.setCreatedDate(null);
        nonRelationData.setLastModifiedDate(null);
        nonRelationData.setPurchased(null);
        nonRelationData.setGender(dto.getGender());
        nonRelationData.setEthnicity(dto.getEthnicity());
        nonRelationData.setPreferredPronoun(dto.getPreferredPronoun());
        nonRelationData.setDisability(dto.getDisability());
        nonRelationData.setVeteran(dto.getVeteran());
        nonRelationData.setMemberOfLGBTQ(dto.getMemberOfLGBTQ());
        nonRelationData.setSelfEvaluation(dto.getSelfEvaluation());
        return nonRelationData;
    }

    public static TalentDTOV3 confidentialResult(TalentV3 talent, ConfidentialInfoDto confidentialInfo) {
        TalentDTOV3 dto = new TalentDTOV3();
        dto.setId(talent.getId());
        dto.setCreatedBy(talent.getCreatedBy());
        dto.setCreatedDate(talent.getCreatedDate());
        dto.setCreatedUser(talent.getCreatedUser());
        dto.setLastModifiedBy(talent.getLastModifiedBy());
        dto.setLastModifiedDate(talent.getLastModifiedDate());
        dto.setLastModifiedUser(talent.getLastModifiedUser());
        dto.setLastName(talent.getLastName());
        dto.setFullName(CommonUtils.formatFullName("***", talent.getLastName()));
        dto.setFirstName("***");
        dto.setConfidentialInfo(confidentialInfo);
        dto.setConfidentialTalentViewAble(false);
        return dto;
    }

    /**
     * This should only happen for English
     * Chinese should just set full name, ignore the first & last name
     */
    public void setFullName() {
        if (this.firstName == null && this.lastName == null) {
            throw new CustomParameterizedException("Either full name or first name and last name are required");
        }
        if(this.firstName == null) {
            this.fullName = this.lastName;
        } else if(this.lastName == null) {
            this.fullName = this.firstName;
        } else {
            this.fullName = CommonUtils.formatFullName(firstName, lastName);
        }
    }
}
