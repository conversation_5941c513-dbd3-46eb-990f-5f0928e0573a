package com.altomni.apn.common.dto.talent;


import cn.hutool.json.JSONObject;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentESDocument {

    @ApiModelProperty(value = "list of talent contacts. Show up when fetch a single talent profile. Read Only. Create/update using the TalentContact entity.")
    private List<TalentContactDTO> contacts = new ArrayList<>();

    private List<String> emails;

    private TalentESEmailStatusDTO emailStatus;

    private List<String> phonesDisplay;

    /**
     * common pool
     */
    @ApiModelProperty(value = "current salary range only use in common pool")
    private RangeDTO annualSalaryInUSD;

    @ApiModelProperty(value = "preferred salary range only use in common pool")
    private RangeDTO preferredAnnualSalaryInUSD;

    /**
     * salary
     */
    @ApiModelProperty(value = "Talent current salary range")
    private RangeDTO salaryRange;

    @ApiModelProperty(value = "pay type")
    private String payType;

    @ApiModelProperty(value = "currency")
    private String currency;

    private String gender;

    private String birthDate;

    private Double currencyUSDExchangeRate;

    private Double payTimes;

    /**
     * preferred salary
     */
    @ApiModelProperty(value = "preferred salary range")
    private RangeDTO preferredSalaryRange;

    @ApiModelProperty(value = "preferred pay type")
    private String preferredPayType;

    private String preferredCurrency;

    private Double preferredCurrencyUSDExchangeRate;

    private Double preferredPayTimes;

    @ApiModelProperty(value = "list of talent educations. Show up when fetch a single talent profile. Read Only. Create/update using the TalentEducationDTO entity.")
    private List<TalentEducationDTO> educations = new ArrayList<>();

    private TalentTopEducationDTO topEducation;

    private List<TalentTopEducationDTO> nonTopEducations;

    @ApiModelProperty(value = "list of talent experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentExperienceDTO> experiences = new ArrayList<>();

    private Long experienceYears;

    private LocalDate experienceBenchmarkStartDate;

    private List<TalentCurrentExperiencesDTO> pastExperiences;

    private List<TalentCurrentExperiencesDTO> currentExperiences;

    @ApiModelProperty(value = "first name. required in US.")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.")
    private String lastName;

    private String fullName;

    private String nickName;
    private String nickFirstName;
    private String nickLastName;

    @ApiModelProperty(value = "industries talent specified in same type")
    private List<String> industries;

    @ApiModelProperty(value = "industries talent specified in same type")
    private List<String> industryDisplays;

    @ApiModelProperty(value = "job functions, load data in biz_dict service")
    private List<String> jobFunctions;

    @ApiModelProperty(value = "job functions, load data in biz_dict service")
    private List<String> jobFunctionDisplays;

    @ApiModelProperty(value = "languages, load data in biz_dict service")
    private List<String> languages;

    @ApiModelProperty(value = "Talent current location. Save to talent basic information table.")
    private LocationDTO currentLocation;

    @ApiModelProperty(value = "Talent preferred locations to work. May include the current location.")
    private List<LocationDTO> preferredLocations = new ArrayList<>();

    @ApiModelProperty(value = "list of talent project experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentProjectDTO> projects = new ArrayList<>();

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<JSONObject> skills;

    @ApiModelProperty(value = "The JD in text format, to save in DB", required = true)
    private String text;

    private String skillsText;

    private List<String> tenantLabels;

    private String workAuthorization;

    @ApiModelProperty(value = "The group of assigned user ids, one or more user role JsonStrings ")
    private JSONObject assignedUsers;

    private Instant createdDate;

    @ApiModelProperty(value = "check whether talent has a LinkedIn account ")
    private String formattedLinkedIn;

    @ApiModelProperty(value = "whether talent is purchased from common db. Default is false.")
    private Boolean purchased = false;

    private List<UserResponsibility> userResponsibility0;
    private List<UserResponsibility> userResponsibility1;
    private List<UserResponsibility> userResponsibility2;
    private List<UserResponsibility> userResponsibility3;
    private List<UserResponsibility> userResponsibility4;
    private List<UserResponsibility> userResponsibility5;
    private List<UserResponsibility> userResponsibility6;
    private List<UserResponsibility> userResponsibility7;
    private List<UserResponsibility> userResponsibility8;
    private List<UserResponsibility> userResponsibility9;
    private List<UserResponsibility> userResponsibility10;
    private List<UserResponsibility> userResponsibility11;
    private List<UserResponsibility> userResponsibility12;
    private List<UserResponsibility> userResponsibility13;
    private List<UserResponsibility> userResponsibility14;
    private List<UserResponsibility> userResponsibility15;
    private List<UserResponsibility> userResponsibility16;
    private List<UserResponsibility> userResponsibility17;
    private List<UserResponsibility> userResponsibility18;
    private List<UserResponsibility> userResponsibility19;
    private List<UserResponsibility> userResponsibility20;

    @ApiModelProperty(value = "common pool esId")
    private String esId;

    @ApiModelProperty(value = "common pool contacts domains")
    private List<String> domains;


}
