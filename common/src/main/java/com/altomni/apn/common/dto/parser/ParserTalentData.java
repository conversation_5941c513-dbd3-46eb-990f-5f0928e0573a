package com.altomni.apn.common.dto.parser;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.talent.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ParserTalentData {
    @ApiModelProperty(value = "first name. required in US.")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.")
    private String lastName;

    @ApiModelProperty(value = "nick first name.")
    private String nickFirstName;

    @ApiModelProperty(value = "nick last name.")
    private String nickLastName;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
            "the full name can not be null.")
    private String fullName;

    @ApiModelProperty(value = "Talent gender enum id.")
    private String gender;

    @ApiModelProperty(value = "only use in jobDiva MyProfile, zipCode")
    private String zipCode;

    @ApiModelProperty(value = "Talent birthday(yyyy-MM-dd).")
    private String birthday;


    @ApiModelProperty(value = "id for the company location")
    private Long companyLocationId;

    @ApiModelProperty(value = "Talent current location. Save to talent basic information table.")
    private LocationDTO currentLocation;


    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> skills;

    /**
     * Additional Info
     */
    @ApiModelProperty(value = "Talent expected salary currency")
    private String currency;


    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "Talent current salary range")
    private RangeDTO salaryRange;

    private List<TalentPreference> preferences;
//    @ApiModelProperty(value = "Talent preferred salary currency")
//    private String preferredCurrency;
//    @ApiModelProperty(value = "salary rate unit")
//    private RateUnitType preferredPayType;
//
//    @ApiModelProperty(value = "preferred salary range")
//    private RangeDTO preferredSalaryRange;

    //    @ApiModelProperty(value = "Talent preferred locations to work. May include the current location.")
//    private List<LocationDTO> preferredLocations;

    @ApiModelProperty(value = "sourcing channel")
    private ResumeSourceType source;

    @ApiModelProperty(value = "salary range only use in common pool")
    private RangeDTO annualSalaryInUSD;

    @ApiModelProperty(value = "preferred salary range only use in common pool")
    private RangeDTO preferredAnnualSalaryInUSD;

    @ApiModelProperty(value = "work authorization enum name list")
    private List<Integer> workAuthorization;

    @ApiModelProperty(value = "job functions enum id list, load data in biz_dict service")
    private List<Integer> jobFunctions;

    @ApiModelProperty(value = "languages enum name list, load data in biz_dict service")
    private List<String> languages;

    @ApiModelProperty(value = "industries enum id list. talent specified in same type")
    private List<Integer> industries;

    @ApiModelProperty(value = "list of talent contacts. Show up when fetch a single talent profile. Read Only. Create/update using the TalentContact entity.")
    private List<TalentContactDTO> contacts;

    @ApiModelProperty(value = "list of talent experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentExperienceDTO> experiences;

    @ApiModelProperty(value = "list of talent educations. Show up when fetch a single talent profile. Read Only. Create/update using the TalentEducationDTO entity.")
    private List<TalentEducationDTO> educations;

    @ApiModelProperty(value = "list of talent project experiences. Show up when fetch a single talent profile. Read Only. Create/update using the TalentExperience entity.")
    private List<TalentProjectDTO> projects;



}
