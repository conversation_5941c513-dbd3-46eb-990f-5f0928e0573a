package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.CurrencyRateDay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface CurrencyRateDayRepository extends JpaRepository<CurrencyRateDay, Long>, QuerydslPredicateExecutor<CurrencyRateDay> {
}