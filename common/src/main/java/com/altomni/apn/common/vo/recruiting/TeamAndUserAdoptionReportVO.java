package com.altomni.apn.common.vo.recruiting;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeamAndUserAdoptionReportVO {
    private Long userId;

    private String userName;

    private Long teamId;

    private String teamName;

    // for teamView
    private Long numberOfUsers;
    // for teamView
    private Long averageActiveDuration;
    // for teamView
    private Long noUsageUsersCount;
    // for teamView
    private Long lowAverageUserCount;

    private Integer callCount;

    private Integer uniqueCalledTalentCount;

    private Long noteCount;

    private Integer uniqueNotedTalentCount;

    private Long emailCount;

    private Long uniqueEmailedTalentCount;

    private Long submitToJobCount;

    private Long interviewCount;

    private Long uniqueInterviewedTalentCount;

    private Long onboardTalentCount;

    // for userView
    private Long activationDuration;

}
