package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;

/**
 * Service Interface for managing TalentRecruitmentProcessSubmitToJob.
 */
public interface TalentRecruitmentProcessSubmitToJobService {

    void preProcessValidation(TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    TalentRecruitmentProcessSubmitToJobVO create(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    TalentRecruitmentProcessSubmitToJobVO update(Long talentRecruitmentProcessId, TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    TalentRecruitmentProcessSubmitToJob<PERSON> updateRecommendCommentsOnly(TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    TalentRecruitmentProcessSubmitToJobVO updateRecommendCommentsOnly(Long talentRecruitmentProcessId, String note);

    TalentRecruitmentProcessSubmitToJobVO findOneByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

}
