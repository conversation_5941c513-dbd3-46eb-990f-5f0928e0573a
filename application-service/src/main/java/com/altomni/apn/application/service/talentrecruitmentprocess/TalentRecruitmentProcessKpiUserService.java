package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.TalentRecruitmentProcessKpiUser;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;

import java.util.List;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessKpiUser}.
 */
public interface TalentRecruitmentProcessKpiUserService {

    List<TalentRecruitmentProcessKpiUserVO> save(Long talentRecruitmentProcessId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers);

    List<TalentRecruitmentProcessKpiUserVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessKpiUserVO> findAllPlainByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessKpiUserVO> findAllByTalentRecruitmentProcessIdExcludingOwners(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessKpiUserVO> findAllByJobId(Long jobId);

    List<TalentRecruitmentProcessKpiUserVO> findAllByTalentIdAndTalentRecruitmentProcessIdNotIn(Long talentId, Long talentRecruitmentProcessId);


    void recalculateOwnerCommission(Long resignedUserId);
}
