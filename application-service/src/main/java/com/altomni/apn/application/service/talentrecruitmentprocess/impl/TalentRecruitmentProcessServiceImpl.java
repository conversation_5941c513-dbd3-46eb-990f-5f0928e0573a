package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.config.env.ApplicationProperties;
import com.altomni.apn.application.config.env.TalentOnboardMQProperties;
import com.altomni.apn.application.domain.*;
import com.altomni.apn.application.domain.aiRecommend.JobCommonTalentAiRecommend;
import com.altomni.apn.application.domain.aiRecommend.JobTalentAiRecommend;
import com.altomni.apn.application.domain.enumeration.AIRecommendType;
import com.altomni.apn.application.domain.enumeration.EmploymentStatus;
import com.altomni.apn.application.dto.ApplicationUpdateStatusOfferAcceptedEmailDTO;
import com.altomni.apn.application.dto.CongratsContent;
import com.altomni.apn.application.dto.CongratsUserTeam;
import com.altomni.apn.application.dto.EliminateDTO;
import com.altomni.apn.application.repository.*;
import com.altomni.apn.application.repository.aiRecommend.JobCommonTalentAiRecommendRepository;
import com.altomni.apn.application.repository.aiRecommend.JobTalentAiRecommendRepository;
import com.altomni.apn.application.repository.transactionrecord.CommonMqTransactionRecordRepository;
import com.altomni.apn.application.service.common.CommonClient;
import com.altomni.apn.application.service.company.CompanyClient;
import com.altomni.apn.application.service.company.CompanyService;
import com.altomni.apn.application.service.finance.FinanceClient;
import com.altomni.apn.application.service.finance.FinanceService;
import com.altomni.apn.application.service.job.JobClient;
import com.altomni.apn.application.service.job.JobService;
import com.altomni.apn.application.service.jobdiva.JobdivaClient;
import com.altomni.apn.application.service.mail.MailService;
import com.altomni.apn.application.service.management.ManagementClient;
import com.altomni.apn.application.service.talent.TalentClient;
import com.altomni.apn.application.service.talent.TalentService;
import com.altomni.apn.application.service.talentrecruitmentprocess.*;
import com.altomni.apn.application.service.user.UserClient;
import com.altomni.apn.application.service.user.UserService;
import com.altomni.apn.application.service.xxljob.XxlJobService;
import com.altomni.apn.application.utils.PdfUtil;
import com.altomni.apn.application.web.rest.vm.*;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.CommonDataStatus;
import com.altomni.apn.common.domain.enumeration.RecommendFeedbackReason;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.calendar.*;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.domain.talent.TalentResumeRelation;
import com.altomni.apn.common.domain.timezone.EnumTimezone;
import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.altomni.apn.common.dto.calendar.*;
import com.altomni.apn.common.dto.company.CompanyBriefDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.message.MessageCreateWithNoPoachingSubmitDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.repository.talent.ResumeRepository;
import com.altomni.apn.common.repository.timezone.EnumTimezoneRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import com.altomni.apn.common.vo.talent.TenantPublicVO;
import com.altomni.apn.company.domain.enumeration.company.NoPoachingCompanyType;
import com.altomni.apn.company.domain.vo.company.NoPoachingAccountResultVO;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.vo.company.NoCompanyAffiliatesVO;
import com.altomni.apn.company.vo.company.NoPoachingAccountVO;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.repository.start.StartRepository;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.talent.repository.talent.TalentResumeRelationRepository;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.repository.user.CreditTransactionRepository;
import com.altomni.apn.user.service.calendar.CalendarService;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URISyntaxException;
import java.text.NumberFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.constants.AuthConstants.AUTHORIZATION_HEADER;
import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

/**
 * Service Implementation for managing TalentRecruitmentProcess.
 */
@Service
@Transactional
public class TalentRecruitmentProcessServiceImpl implements TalentRecruitmentProcessService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessServiceImpl.class);
    private final static Long IPG_TENANT_ID = 4L;
    @Resource
    private TalentRecruitmentProcessRepository talentRecruitmentProcessRepository;
    @Resource
    private TalentRecruitmentProcessNodeRepository talentRecruitmentProcessNodeRepository;
    @Resource
    private TalentRecruitmentProcessNodeService talentRecruitmentProcessNodeService;
    @Resource
    private TalentRecruitmentProcessInterviewRepository talentRecruitmentProcessInterviewRepository;
    @Resource
    private TalentService talentService;
    @Resource
    private JobService jobService;
    @Resource
    private XxlJobService xxlJobService;
    @Resource
    private RecruitmentProcessRepository recruitmentProcessRepository;
    @Resource
    private StartRepository startRepository;
    @Resource
    private TalentRecruitmentProcessSubmitToJobService talentRecruitmentProcessSubmitToJobService;
    @Resource
    private TalentRecruitmentProcessSubmitToClientService talentRecruitmentProcessSubmitToClientService;
    @Resource
    private TalentRecruitmentProcessInterviewService talentRecruitmentProcessInterviewService;
    @Resource
    private TalentRecruitmentProcessOfferService talentRecruitmentProcessOfferService;
    @Resource
    private TalentRecruitmentProcessIpgOfferAcceptService ipgOfferAcceptService;
    @Resource
    private TalentRecruitmentProcessCommissionService talentRecruitmentProcessCommissionService;
    @Resource
    private TalentRecruitmentProcessOnboardService talentRecruitmentProcessOnboardService;
    @Resource
    private TalentRecruitmentProcessEliminateService talentRecruitmentProcessEliminateService;
    @Resource
    private TalentRecruitmentProcessKpiUserRepository talentRecruitmentProcessKpiUserRepository;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateService agreedPayRateService;
    @Resource
    private TalentRecruitmentProcessFeeChargeService feeChargeService;
    @Resource
    private TalentRecruitmentProcessOfferSalaryPackageService offerSalaryPackageService;
    @Resource
    private TalentRecruitmentProcessIpgContractFeeChargeService contractFeeChargeService;
    @Resource
    private TalentRecruitmentProcessOnboardClientInfoService clientInfoService;
    @Resource
    private UserService userService;
    @Resource
    private UserClient userClient;
    @Resource
    private TalentClient talentClient;
    @Resource
    private JobdivaClient jobdivaClient;
    @Resource
    private JobClient jobClient;
    @Resource
    private CompanyService companyService;
    @Resource
    private FinanceClient financeClient;
    @Resource
    private ManagementClient managementClient;

    @Resource
    private FinanceService financeService;

    @Resource
    private MailService mailService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private TalentOnboardMQProperties talentOnboardMQProperties;

    @Resource
    CommonMqTransactionRecordRepository commonMqTransactionRecordRepository;

    @Resource
    EnumTimezoneRepository enumTimezoneRepository;

    @Resource(name = "talentOnboardMQRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private CommonClient commonClient;

    @Resource
    private CompanyClient companyClient;

    @Resource
    private ProcessNativeRepository processNativeRepository;

    @Resource
    private TalentRecruitmentProcessResignationRepository resignationRepository;

    @Resource
    private TalentRecruitmentProcessSubmitToClientRepository talentRecruitmentProcessSubmitToClientRepository;

    @Resource
    private TalentRecruitmentProcessOfferRepository talentRecruitmentProcessOfferRepository;

    @Resource
    private TalentRecruitmentProcessIpgOfferAcceptRepository talentRecruitmentProcessIpgOfferAcceptRepository;

    @Resource
    private TalentRecruitmentProcessOnboardRepository talentRecruitmentProcessOnboardRepository;

    @Resource
    private TalentRecruitmentProcessOnboardDateRepository talentRecruitmentProcessOnboardDateRepository;

    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateRepository talentRecruitmentProcessIpgAgreedPayRateRepository;

    @Resource
    private TalentRecruitmentProcessEliminateRepository talentRecruitmentProcessEliminateRepository;

    @Resource
    private TalentRecruitmentProcessOfferSalaryPackageRepository talentRecruitmentProcessOfferSalaryPackageRepository;

    @Resource
    private TalentRecruitmentProcessOfferFeeChargeRepository talentRecruitmentProcessOfferFeeChargeRepository;

    @Resource
    private TalentRecruitmentProcessOnboardClientInfoRepository talentRecruitmentProcessOnboardClientInfoRepository;

    @Resource
    private TalentRecruitmentProcessIpgContractFeeChargeRepository talentRecruitmentProcessIpgContractFeeChargeRepository;

    @Resource
    private TalentRecruitmentProcessMessageService talentRecruitmentProcessMessageService;

    @Resource
    CommonRedisService commonRedisService;

    @Resource
    private JobTalentAiRecommendRepository jobTalentAiRecommendRepository;

    @Resource
    private JobCommonTalentAiRecommendRepository jobCommonTalentAiRecommendRepository;

    @Resource
    private CreditTransactionRepository creditTransactionRepository;

    @Resource
    private HttpService httpService;

    @Resource
    private CongratsOfferNotificationRepository congratsOfferNotificationRepository;

    private final String CRM_GET_NO_POACHING_COMPANIES_LIST_URL = "/account/api/v1/companies/no-poaching-companies/list";



    private RecruitmentProcess validateRecruitmentProcess(Long recruitmentProcessId) {
        if (recruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        Optional<RecruitmentProcess> recruitmentProcess = recruitmentProcessRepository.findById(recruitmentProcessId); //TODO: add cache
        if (recruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(recruitmentProcessId), applicationApiPromptProperties.getAppl()));
        }
        if (!recruitmentProcess.get().getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBNOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (!recruitmentProcess.get().getStatus().equals(ActiveStatus.ACTIVE)) {
            throw new CustomParameterizedException("Recruitment Process is INACTIVE, id: " + recruitmentProcessId);
        }
        return recruitmentProcess.get();
    }

    private RecruitmentProcess validateRecruitmentProcessForAuto(Long recruitmentProcessId) {
        if (recruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        Optional<RecruitmentProcess> recruitmentProcess = recruitmentProcessRepository.findById(recruitmentProcessId); //TODO: add cache
        if (recruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(recruitmentProcessId), applicationApiPromptProperties.getAppl()));
        }
        if (!recruitmentProcess.get().getStatus().equals(ActiveStatus.ACTIVE)) {
            throw new CustomParameterizedException("Recruitment Process is INACTIVE, id: " + recruitmentProcessId);
        }
        return recruitmentProcess.get();
    }

    private TalentRecruitmentProcess validateTalentRecruitmentProcess(Long talentRecruitmentProcessId) {
        if (talentRecruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBTALENTPROCESSIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        Optional<TalentRecruitmentProcess> talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId);
        if (talentRecruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBTALENTPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(talentRecruitmentProcessId), applicationApiPromptProperties.getAppl()));
        }
        if (!talentRecruitmentProcess.get().getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBNOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        return talentRecruitmentProcess.get();
    }

    private TalentRecruitmentProcess validateTalentRecruitmentProcessForAuto(Long talentRecruitmentProcessId) {
        if (talentRecruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBTALENTPROCESSIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        Optional<TalentRecruitmentProcess> talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId);
        if (talentRecruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBTALENTPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(talentRecruitmentProcessId), applicationApiPromptProperties.getAppl()));
        }
        return talentRecruitmentProcess.get();
    }

    private TalentRecruitmentProcessVO toVO(Long talentRecruitmentProcessId) {
        TalentRecruitmentProcess talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId).orElse(null);
        if (Objects.nonNull(talentRecruitmentProcess)) {
            this.verifyPrivateJobPermission(talentRecruitmentProcess.getJobId());
        }
        return toVO(talentRecruitmentProcess);
    }

    private TalentRecruitmentProcessVO toVOForAuto(Long talentRecruitmentProcessId) {
        TalentRecruitmentProcess talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId).orElse(null);
        TalentRecruitmentProcessVO result = new TalentRecruitmentProcessVO();
        BeanUtil.copyProperties(talentRecruitmentProcess, result);
        result.setTalentRecruitmentProcessNodes(toVOs(talentRecruitmentProcessNodeRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId)));

        return result;
    }

    private List<TalentRecruitmentProcessNodeVO> toVOs(List<TalentRecruitmentProcessNode> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return ServiceUtils.convert2DTOList(entities, TalentRecruitmentProcessNodeVO.class);
    }

    private void verifyPrivateJobPermission(Long jobId) {
        Set<Long> allUnauthorizedPrivateJobIds = recruitmentProcessRepository.findAllUnauthorizedPrivateJobIds(SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        if (allUnauthorizedPrivateJobIds.contains(jobId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDINFONOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
    }

    private TalentRecruitmentProcessVO toVO(TalentRecruitmentProcess talentRecruitmentProcess) { //TODO: remove duplicate objects/variables
        if (talentRecruitmentProcess == null) {
            return null;
        }
        StopWatch stopWatch = new StopWatch("echocheng8 start");
        stopWatch.start("get user map and job and talent");

        Long processId = talentRecruitmentProcess.getId();
        Authentication authentication = SecurityUtils.getAuthentication();
        TalentRecruitmentProcessVO result = TalentRecruitmentProcess.fromEntity(talentRecruitmentProcess);
        Long createdUserId = SecurityUtils.getUserIdFromCreatedBy(talentRecruitmentProcess.getCreatedBy());
        Long modifyUserId = SecurityUtils.getUserIdFromCreatedBy(talentRecruitmentProcess.getLastModifiedBy());

        var userMapFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return userService.findBriefUsers(Arrays.asList(createdUserId, modifyUserId))
                    .stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));
        }, executor);
        var jobFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return jobService.getJob(talentRecruitmentProcess.getJobId());
        }, executor).thenApplyAsync(job -> {
            SecurityUtils.setAuthentication(authentication);
            CompanyDTO company = companyService.getCompany(job.getCompanyId());
            if (ObjectUtil.isNotEmpty(company)) {
                CompanyBriefDTO companyBriefDTO = new CompanyBriefDTO();
                companyBriefDTO.setId(company.getId());
                companyBriefDTO.setName(company.getName());
                job.setCompany(companyBriefDTO);
            }
            return job;
        }, executor);
        var talentFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return talentService.getTalentBrief(talentRecruitmentProcess.getTalentId());
        }, executor);

        CompletableFuture.allOf(userMapFuture, jobFuture, talentFuture).exceptionally(FutureExceptionUtil::handleFutureException);

        result.setCreatedDate(talentRecruitmentProcess.getCreatedDate());
        result.setCreatedUser(userMapFuture.join().get(createdUserId)); //TODO: remove
        result.setCreatedBy(talentRecruitmentProcess.getCreatedBy());

        result.setLastModifiedDate(talentRecruitmentProcess.getLastModifiedDate());
        result.setLastModifiedUser(userMapFuture.join().get(modifyUserId)); //TODO: remove
        result.setLastModifiedBy(talentRecruitmentProcess.getLastModifiedBy());

        stopWatch.stop();
        stopWatch.start("get process nodes and currentNode");
        result.setEliminate(talentRecruitmentProcessEliminateService.findByTalentRecruitmentProcessId(processId));
        result.setResigned(CollectionUtils.isNotEmpty(resignationRepository.findByTalentRecruitmentProcessIdAndNonConvertToFte(processId)));
        result.setTalentRecruitmentProcessNodes(talentRecruitmentProcessNodeService.findAll(processId));
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(processId);
        //Compatible historical data, and notifying users
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (ObjectUtil.isNotEmpty(currentNode)) {
            result.setLastNodeStatus(currentNode.getNodeStatus());
            result.setLastNodeType(currentNode.getNodeType());
        }

        result.setJob(jobFuture.join());
        if (ObjectUtil.isNotEmpty(result.getJob()) && ObjectUtil.isNotEmpty(result.getJob().getDefaultCommission())) {
            result.setDefaultCommission(result.getJob().getDefaultCommission());
        }
        result.setTalent(talentFuture.join());
        stopWatch.stop();
        stopWatch.start("get agreed pay rate amd kpi users");
        result.setAgreedPayRate(agreedPayRateService.findByTalentRecruitmentProcessId(processId));
        result.setKpiUsers(kpiUserService.findAllByTalentRecruitmentProcessId(processId));
        stopWatch.stop();
        log.info("[apn @{}] echocheng8 time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return result;
    }

    private TalentRecruitmentProcessVO toVOBrief(TalentRecruitmentProcess talentRecruitmentProcess) {
        if (talentRecruitmentProcess == null) {
            return null;
        }
        TalentRecruitmentProcessVO result = TalentRecruitmentProcess.fromEntity(talentRecruitmentProcess);
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(talentRecruitmentProcess.getId());
        //Compatible historical data, and notifying users
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (ObjectUtil.isNotEmpty(currentNode)) {
            result.setLastNodeStatus(currentNode.getNodeStatus());
            result.setLastNodeType(currentNode.getNodeType());
        }
        return result;
    }

    private Boolean isTheFirstJobToChangeToSubmitToClient(Long talentId, Long talentRecruitmentProcessId) {
        List<TalentOwnership> talentOwnerships = talentService.getAllTalentOwners(talentId, List.of(TalentOwnershipType.OWNER));
        if (CollectionUtils.isNotEmpty(talentOwnerships)) {
            return Objects.equals(talentOwnerships.get(0).getTalentRecruitmentProcessId(), talentRecruitmentProcessId);
        }
        return Boolean.FALSE;
    }

    private TalentRecruitmentProcessVO toVONoPermission(TalentRecruitmentProcess talentRecruitmentProcess) {
        if (talentRecruitmentProcess == null) {
            return null;
        }
        TalentRecruitmentProcessVO result = TalentRecruitmentProcess.fromEntity(talentRecruitmentProcess);
        result.setCreatedUser(userService.getUserById(com.altomni.apn.common.utils.SecurityUtils.getUserIdFromCreatedBy(talentRecruitmentProcess.getCreatedBy())));
        result.setLastModifiedUser(userService.getUserById(com.altomni.apn.common.utils.SecurityUtils.getUserIdFromCreatedBy(talentRecruitmentProcess.getLastModifiedBy())));
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(result.getId());
        if (currentNode != null) {
            result.setLastNodeType(currentNode.getNodeType());
            result.setLastNodeStatus(currentNode.getNodeStatus());
            result.setLastNodeDate(currentNode.getCreatedDate());
        }
        result.setKpiUsers(kpiUserService.findAllByTalentRecruitmentProcessId(talentRecruitmentProcess.getId()));
        return result;
    }

    private void updateTalentRecruitmentProcess(TalentRecruitmentProcess process, String note) {
        if (StringUtils.isNotEmpty(note)) {
            process.setNote(note);
        }
        process.setLastModifiedDate(Instant.now());
        process.setLastModifiedBy(SecurityUtils.getUserUid());
        talentRecruitmentProcessRepository.saveAndFlush(process);
    }

    private void updateTalentRecruitmentProcessNoteOnly(Long talentRecruitmentProcessId, String note) {

        Optional<TalentRecruitmentProcess> talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId);
        if (talentRecruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBTALENTPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(talentRecruitmentProcessId), applicationApiPromptProperties.getAppl()));
        }
        if (!talentRecruitmentProcess.get().getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDINFONOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }

        TalentRecruitmentProcess process = talentRecruitmentProcess.get();
        if (StringUtils.isNotEmpty(note)) {
            process.setNote(note);
        }
        talentRecruitmentProcessRepository.saveAndFlush(process);
    }

    @Override
    public TalentRecruitmentProcessVO submitToJob(TalentRecruitmentProcessSubmitToJobVO submitToJobVO, boolean noteOnly) {
        StopWatch stopWatch = new StopWatch("[RecruitmentProcess: submit to job] talentId: " + submitToJobVO.getTalentId() + ", jobId: " + submitToJobVO.getJobId());

        if (Boolean.FALSE.equals(talentClient.confidentialTalentViewAble(submitToJobVO.getTalentId()).getBody())) {
           throw new ForbiddenException("The talent is confidential and cannot be viewed by this user.");
        }

        stopWatch.start("[1] Check if recruitment process already exists, update only");
        if (submitToJobVO.getTalentRecruitmentProcessId() != null) {
            // update only
            TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(submitToJobVO.getTalentRecruitmentProcessId());
            //Compatible historical data, and notifying users
            if (ObjectUtil.isEmpty(currentNode)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
            if (noteOnly || NodeType.SUBMIT_TO_JOB.toDbValue() < currentNode.getNodeType().toDbValue() || NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                // this node is a complete node or the application has been terminated here, only note is allowed to update
                talentRecruitmentProcessSubmitToJobService.updateRecommendCommentsOnly(submitToJobVO);
                if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) ||
                        (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                                && NodeType.OFFER.toDbValue() >= currentNode.getNodeType().toDbValue())) {
                    kpiUserService.save(submitToJobVO.getTalentRecruitmentProcessId(), submitToJobVO.getKpiUsers());
                }
                if (Objects.equals(NodeType.SUBMIT_TO_JOB.toDbValue(), currentNode.getNodeType().toDbValue()) && !NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                    updateTalentRecruitmentProcessNoteOnly(submitToJobVO.getTalentRecruitmentProcessId(), submitToJobVO.getRecommendComments());
                }
            } else {
                talentRecruitmentProcessSubmitToJobService.update(submitToJobVO.getTalentRecruitmentProcessId(), submitToJobVO);
                talentRecruitmentProcessRepository.updateLastModifiedDateAndLastModifiedBy(submitToJobVO.getTalentRecruitmentProcessId(), SecurityUtils.getUserUid());
            }
            return toVO(submitToJobVO.getTalentRecruitmentProcessId());
        }
        stopWatch.stop();

        // 验证当前登录用户是否有提交该候选人的权限
        TalentEmploymentStatusVO talentEmploymentStatus = this.getTalentEmploymentStatus(submitToJobVO.getTalentId());
        if (BooleanUtils.isFalse(talentEmploymentStatus.getHasPermission())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_CHECKOTHERRECRUITMENTPROCESSFORTALENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }

        stopWatch.start("[2] Validate talent, job and recruitment process");
        validateRecruitmentProcess(submitToJobVO.getRecruitmentProcessId());
        stopWatch.stop();

        stopWatch.start("[3] Check duplicate recruitment process by talent id and job id");
        TalentRecruitmentProcess exist = talentRecruitmentProcessRepository.findByTalentIdAndJobId(submitToJobVO.getTalentId(), submitToJobVO.getJobId());
        if (exist != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        stopWatch.stop();

        stopWatch.start("[4] Pre process validation");
        talentRecruitmentProcessSubmitToJobService.preProcessValidation(submitToJobVO);
        stopWatch.stop();

        stopWatch.start("[5] Construct and save talent recruitment process");

        TalentRecruitmentProcess instant = TalentRecruitmentProcess.fromTalentRecruitmentProcessSubmitToJobVO(submitToJobVO);
        //提交至职位增加ai recommend查询判断
        addAiSource(instant,submitToJobVO);

        TalentRecruitmentProcess talentRecruitmentProcess = talentRecruitmentProcessRepository.save(instant);
        stopWatch.stop();

        stopWatch.start("[6] Initialize all nodes");
        talentRecruitmentProcessNodeService.init(talentRecruitmentProcess);
        stopWatch.stop();

        stopWatch.start("[7] Create submit to job");
        talentRecruitmentProcessSubmitToJobService.create(talentRecruitmentProcess, submitToJobVO);
        stopWatch.stop();

        //send applied email
        stopWatch.start("[8] Send applied email");
        sendAppliedEmail(talentRecruitmentProcess);
        stopWatch.stop();

        stopWatch.start("[9] Construct result DTO");
        TalentRecruitmentProcessVO res = toVO(talentRecruitmentProcess);
        stopWatch.stop();
        
        addRecommendFeedback(submitToJobVO.getRecommendFeedback());
        
        //异步处理，判断候选人的经验，是否为禁猎客户、禁猎客户联系人，如果是，则发送邮件给公司AM ,Team leader
        NoPoachingAccountResultVO noPoachingAccountResultVO = getNoPoachingResult();
        SecurityContext context = SecurityContextHolder.getContext();
        if(ObjectUtil.isNotNull(noPoachingAccountResultVO)) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                checkTalentExperienceAndRemind(context, submitToJobVO.getTalentId(), submitToJobVO.getJobId(), SecurityUtils.getUserId(), SecurityUtils.getTenantId(), noPoachingAccountResultVO);
            });
        }
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(submitToJobVO.getJobId(), CalendarRelationEnum.JOB, List.of(CalendarTypeEnum.NO_SUBMIT_TALENT));
        });
        log.info("[Application timelapse result] submitToJob: {}", stopWatch.prettyPrint());
        return res;
    }

    private void addAiSource(TalentRecruitmentProcess instant,TalentRecruitmentProcessSubmitToJobVO submitToJobVO){
        List<JobTalentAiRecommend> aiRecommends = jobTalentAiRecommendRepository.findByTenantIdAndTalentIdAndJobId(SecurityUtils.getTenantId(), submitToJobVO.getTalentId(), submitToJobVO.getJobId());
        if (!aiRecommends.isEmpty()) {
            JobTalentAiRecommend aiRecommend = aiRecommends.get(0);
            instant.setAiScore(aiRecommend.getAiScore());
        }else{
            CreditTransaction creditTransaction = creditTransactionRepository.findByTenantIdAndStatusAndTalentIdIs(instant.getTenantId(),Status.Available,instant.getTalentId());
            if(null != creditTransaction){
                List<JobCommonTalentAiRecommend> jobCommonTalentAiRecommends = jobCommonTalentAiRecommendRepository.findByTenantIdAndJobIdAndTalentId(instant.getTenantId(),instant.getJobId(),creditTransaction.getProfileId());
                if (!jobCommonTalentAiRecommends.isEmpty()) {
                    JobCommonTalentAiRecommend aiRecommend = jobCommonTalentAiRecommends.get(0);
                    instant.setAiScore(aiRecommend.getAiScore());
                }
            }
        }
    }

    private void checkTalentExperienceAndRemind(SecurityContext context, Long talentId, Long jobId, Long userId, Long tenantId, NoPoachingAccountResultVO noPoachingAccountResultVO) {
        //1.查询所有禁猎公司，和候选人经验
        CompletableFuture<Map<Long, JobType>> jobTypeMapFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitmentProcessRepository.findAllIdAndJobTypeByTenantId(tenantId);
        });
        CompletableFuture<JobDTOV3> jobFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return jobClient.getJob(jobId).getBody();
        });
        //查询需要提醒的租户
        CompletableFuture<TenantPublicVO> tenantFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return managementClient.queryTenant(tenantId).getBody();
        });

        JobDTOV3 job = jobFuture.join();
        Map<Long, JobType> jobTypeMap = jobTypeMapFuture.join();
        TenantPublicVO tenant = tenantFuture.join();
        //从tenant中获取配置信息,拿到url
        String url = null;
        if(ObjectUtil.isNotNull(tenant)) {
            String extendedInfo = tenant.getExtendedInfo();
            if (ObjectUtil.isNotNull(extendedInfo)) {
                JSONObject extendedInfoObject = JSONUtil.parseObj(extendedInfo);
                JSONObject remindInfoObj = extendedInfoObject.getJSONObject("noPoachingRemind");
                if (remindInfoObj != null) {
                    url = remindInfoObj.getStr("url");
                }
            }
        }
        //拿不到url则返回
        if(ObjectUtil.isNull(url)) {
            return;
        }
        //查询职位信息，如果类型是payrolling职位，不提醒
        if(CollUtil.isNotEmpty(jobTypeMap) && ObjectUtil.isNotNull(job)) {
            if(JobType.PAY_ROLL.equals(jobTypeMap.get(job.getRecruitmentProcess().getId()))) {
                return;
            }
        }else {
            return;
        }
        CompletableFuture<TalentDTOV3> talentFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return talentClient.getTalent(talentId).getBody();
        });

        TalentDTOV3 talent = talentFuture.join();
        //如果禁猎公司或者经验为空，则直接返回
        if(talent == null || CollUtil.isEmpty(talent.getExperiences())) {
            return;
        }

        //noPoaching的集合
        List<NoPoachingAccountVO> noPoachingAccountList = noPoachingAccountResultVO.getNoPoaching();
        //从noPoaching中提取所有id到Set中，用于快速查找
        Set<Long> noPoachingIds = noPoachingAccountList.stream()
                .map(NoPoachingAccountVO::getId)
                .collect(Collectors.toSet());
        //过滤noCompanyAffiliates：id不在noPoachingIds中且companyType 是 ACCOUNT类型
        List<NoCompanyAffiliatesVO> filteredAffiliatesList = noPoachingAccountResultVO.getNoCompanyAffiliates().stream()
                .filter(item -> item.getCompanyType() == NoPoachingCompanyType.ACCOUNT)
                .filter(item -> !noPoachingIds.contains(item.getId()))
                .collect(Collectors.toList());

        //3.工作经验中的公司id是否匹配, 筛选当前的工作经验
        Set<Long> needRemindContactCompanyIds = new HashSet<>();
        Set<Long> needRemindTalentCompanyIds = new HashSet<>();
        Set<Long> needRemindTalentAffiliatesCompanyIds = new HashSet<>();
        // 检查是否存在当前工作经验
        Optional<TalentExperienceDTO> optionalTalentExperienceDTO = talent.getExperiences().stream()
                .filter(Objects::nonNull)
                .filter(exp -> Boolean.TRUE.equals(exp.getCurrent()))
                .findAny();

        if (optionalTalentExperienceDTO.isEmpty()) {
            return;
        }

        // 处理当前工作经验
        talent.getExperiences().stream().filter(Objects::nonNull)
                // 再次检查 current 是否为 null
                .filter(current -> Optional.ofNullable(current.getCurrent()).orElse(false)).forEach(current -> {
                    if (ObjectUtil.isNotNull(current.getActiveCompanyId())) {
                        needRemindContactCompanyIds.add(current.getActiveCompanyId());
                    }
                    if (ObjectUtil.isNotNull(current.getActiveCRMAccountId())) {
                        needRemindContactCompanyIds.add(current.getActiveCRMAccountId());
                    }

                    // 确保 noPoachingList 不为 null
                    noPoachingAccountList.forEach(noPoachingCompany -> {
                        if (noPoachingCompany != null && noPoachingCompany.getId() != null) {
                            if(noPoachingCompany.getId().equals(current.getActiveCompanyId())) {
                                needRemindTalentCompanyIds.add(current.getActiveCompanyId());
                            }
                            if(noPoachingCompany.getId().equals(current.getCompanyId())) {
                                needRemindTalentCompanyIds.add(current.getCompanyId());
                            }
                            if(current.getRecogCompanyId() != null) {
                                Long recogCompanyIdLong = Long.parseLong(current.getRecogCompanyId());
                                if (noPoachingCompany.getId().equals(recogCompanyIdLong)) {
                                    needRemindTalentCompanyIds.add(recogCompanyIdLong);
                                }
                            }
                        }

                    });
                    //关联禁猎公司的判断
                    filteredAffiliatesList.forEach(noPoachingAffiliatesCompany -> {
                        if (noPoachingAffiliatesCompany != null && noPoachingAffiliatesCompany.getId() != null) {
                            if(noPoachingAffiliatesCompany.getId().equals(StrUtil.toString(current.getActiveCompanyId()))) {
                                needRemindTalentAffiliatesCompanyIds.add(current.getActiveCompanyId());
                            }
                            if(noPoachingAffiliatesCompany.getId().equals(StrUtil.toString(current.getCompanyId()))) {
                                needRemindTalentAffiliatesCompanyIds.add(current.getCompanyId());
                            }
                            if(current.getRecogCompanyId() != null) {
                                Long recogCompanyIdLong = Long.parseLong(current.getRecogCompanyId());
                                if (noPoachingAffiliatesCompany.getId().equals(StrUtil.toString(recogCompanyIdLong))) {
                                    needRemindTalentAffiliatesCompanyIds.add(recogCompanyIdLong);
                                }
                            }
                        }
                    });
                });


        //发送客户联系人被推荐到职位提醒
        if (CollUtil.isNotEmpty(needRemindContactCompanyIds) || CollUtil.isNotEmpty(needRemindTalentCompanyIds) || CollUtil.isNotEmpty(filteredAffiliatesList)) {
            NoPoachingRemindDTO remindDTO = new NoPoachingRemindDTO();
            remindDTO.setUrl(url);

            //获取clientContactIds和noPoachingCompanyIds的所有哦你公司id
            Set<Long> companyIds = new HashSet<>();
            companyIds.addAll(needRemindContactCompanyIds);
            companyIds.addAll(needRemindTalentCompanyIds);
            companyIds.addAll(needRemindTalentAffiliatesCompanyIds);


            //异步查询需要拼接的基础数据
            CompletableFuture<List<AccountCompanyVO>> companyFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return companyClient.queryCompanyByIdsFromCRM(companyIds.stream().toList()).getBody();
            });
            CompletableFuture<UserBriefDTO> userFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return userClient.findBriefUserById(userId).getBody();
            });
            CompletableFuture<Set<Long>> needRemindLeadersFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return userClient.getUpperTeamLeadersByUserId(userId).getBody();
            });

            List<AccountCompanyVO> companies = companyFuture.join();
            //如果找不到对应的公司信息，则不发送
            if(CollUtil.isEmpty(companies)) {
                return;
            }
            UserBriefDTO user = userFuture.join();
            //查询需要提醒的用户
            Set<Long> needRemindLeaders = needRemindLeadersFuture.join();

            //跳转到候选人详情的链接
            String talentUrl = applicationProperties.getBaseUrl() + "/candidates/detail/" + talentId;

            //构建需要发送sse message的key
            MessageCreateWithNoPoachingSubmitDTO messageDTO = new MessageCreateWithNoPoachingSubmitDTO();
            messageDTO.setTalentFullName(talent.getFullName());
            messageDTO.setTalentId(talentId);
            messageDTO.setSubmitUserId(userId);
            messageDTO.setSubmitUserFullName(user.getFullName());
            messageDTO.setJobId(jobId);
            messageDTO.setJobTitle(job.getTitle());
            messageDTO.setTenantId(tenantId);

            //联系人组装sse、webhook参数并发送
            processNoPoachingRemind(needRemindContactCompanyIds, needRemindLeaders, job, talent, user, talentUrl, NoPoachingRemindType.CONTACT, companies,
                        context, messageDTO, remindDTO);

            //候选人组装sse、webhook参数并发送
            processNoPoachingRemind(needRemindTalentCompanyIds, needRemindLeaders, job, talent, user, talentUrl, NoPoachingRemindType.TALENT, companies,
                    context, messageDTO, remindDTO);

            //候选人关联禁猎组装sse、webhook参数并发送
            processNoPoachingAffiliatesRemind(filteredAffiliatesList, needRemindTalentAffiliatesCompanyIds, needRemindLeaders, job, talent, user, talentUrl, context, messageDTO, remindDTO, NoPoachingRemindType.TALENT_AFFILIATE);

        }
    }

    /**
     * 组装sse、webhook参数并发送(关联禁猎)
     * @param filteredAffiliatesList
     * @param needRemindTalentAffiliatesCompanyIds
     * @param needRemindLeaders
     * @param job
     * @param talent
     * @param user
     * @param talentUrl
     * @param context
     * @param messageDTO
     * @param remindDTO
     */
    private void processNoPoachingAffiliatesRemind(
            List<NoCompanyAffiliatesVO> filteredAffiliatesList,
            Set<Long> needRemindTalentAffiliatesCompanyIds,
            Set<Long> needRemindLeaders,
            JobDTOV3 job,
            TalentDTOV3 talent,
            UserBriefDTO user,
            String talentUrl,
            SecurityContext context,
            MessageCreateWithNoPoachingSubmitDTO messageDTO,
            NoPoachingRemindDTO remindDTO,
            NoPoachingRemindType remindType
    ) {

        needRemindTalentAffiliatesCompanyIds.forEach(needRemindTalentAffiliatesCompanyId -> {
            // 公共逻辑：设置公司信息
            filteredAffiliatesList.stream()
                    .filter(c -> needRemindTalentAffiliatesCompanyId != null
                            && c.getId() != null
                            && needRemindTalentAffiliatesCompanyId == Long.parseLong(c.getId()))
                    .findFirst()
                    .ifPresent(affiliatesVO -> {
                        messageDTO.setCompanyId(needRemindTalentAffiliatesCompanyId);
                        messageDTO.setCompanyName(affiliatesVO.getFullBusinessName());
                        remindDTO.setCompanyId(needRemindTalentAffiliatesCompanyId);
                        remindDTO.setCompanyName(affiliatesVO.getFullBusinessName());
                    });

            // 公共逻辑：异步获取 AM IDs
            CompletableFuture<List<Long>> amIdsFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return companyService.getAllAmIdsByCompanyId(needRemindTalentAffiliatesCompanyId);
            });
            List<Long> amIds = amIdsFuture.join();

            // 合并需要提醒的用户 ID（公共逻辑）
            Set<Long> allNeedRemindUserIds = new HashSet<>(needRemindLeaders);
            allNeedRemindUserIds.addAll(amIds);

            // 公共逻辑：异步获取用户信息
            CompletableFuture<List<UserBriefDTO>> needRemindUserFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return userClient.getAllBriefUsersByIds(allNeedRemindUserIds.stream().toList()).getBody();
            });
            List<UserBriefDTO> users = needRemindUserFuture.join();

            // 公共逻辑：提取用户邮箱
            List<String> needSendLarkMessageEmails = users.stream()
                    .map(UserBriefDTO::getEmail)
                    .toList();

            // 设置差异化参数
            messageDTO.setRemindType(remindType);
            messageDTO.setNeedRemindUserIds(allNeedRemindUserIds);

            // 发送 SSE 消息（公共逻辑）
            talentRecruitmentProcessMessageService.sendNoPoachingTalentSubmitNotification(messageDTO);

            // 设置差异化提醒类型
            remindDTO.setRemindType(remindType);
            remindDTO.setTalentId(talent.getId());
            remindDTO.setTalentFullName(talent.getFullName());
            remindDTO.setJobId(job.getId());
            remindDTO.setJobTitle(job.getTitle());
            remindDTO.setSubmitUserFullName(user.getFullName());
            remindDTO.setEmails(needSendLarkMessageEmails);
            remindDTO.setTalentDetailLink(talentUrl);

            // 发送消息（公共逻辑）
            commonClient.noPoachingSendMessageByUserEmails(remindDTO);
        });
    }

    /**
     * 组装sse、webhook参数并发送
     * @param companyIds
     * @param needRemindLeaders
     * @param job
     * @param talent
     * @param user
     * @param talentUrl
     * @param remindType
     * @param companies
     * @param context
     * @param messageDTO
     * @param remindDTO
     */
    private void processNoPoachingRemind(
            Set<Long> companyIds,
            Set<Long> needRemindLeaders,
            JobDTOV3 job,
            TalentDTOV3 talent,
            UserBriefDTO user,
            String talentUrl,
            NoPoachingRemindType remindType,
            List<AccountCompanyVO> companies,
            SecurityContext context,
            MessageCreateWithNoPoachingSubmitDTO messageDTO,
            NoPoachingRemindDTO remindDTO
    ) {
        companyIds.forEach(companyId -> {
            // 公共逻辑：设置公司信息
            companies.stream()
                    .filter(c -> c.getId().equals(companyId))
                    .findFirst()
                    .ifPresent(companyInfo -> {
                        messageDTO.setCompanyId(companyId);
                        messageDTO.setCompanyName(companyInfo.getFullBusinessName());
                        remindDTO.setCompanyId(companyId);
                        remindDTO.setCompanyName(companyInfo.getFullBusinessName());
                    });

            // 公共逻辑：异步获取 AM IDs
            CompletableFuture<List<Long>> amIdsFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return companyService.getAllAmIdsByCompanyId(companyId);
            });
            List<Long> amIds = amIdsFuture.join();

            // 合并需要提醒的用户 ID（公共逻辑）
            Set<Long> allNeedRemindUserIds = new HashSet<>(needRemindLeaders);
            allNeedRemindUserIds.addAll(amIds);

            // 公共逻辑：异步获取用户信息
            CompletableFuture<List<UserBriefDTO>> needRemindUserFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return userClient.getAllBriefUsersByIds(allNeedRemindUserIds.stream().toList()).getBody();
            });
            List<UserBriefDTO> users = needRemindUserFuture.join();

            // 公共逻辑：提取用户邮箱
            List<String> needSendLarkMessageEmails = users.stream()
                    .map(UserBriefDTO::getEmail)
                    .toList();

            // 设置差异化参数
            messageDTO.setRemindType(remindType);
            messageDTO.setNeedRemindUserIds(allNeedRemindUserIds);

            // 发送 SSE 消息（公共逻辑）
            talentRecruitmentProcessMessageService.sendNoPoachingTalentSubmitNotification(messageDTO);

            // 设置差异化提醒类型
            remindDTO.setRemindType(remindType);
            remindDTO.setTalentId(talent.getId());
            remindDTO.setTalentFullName(talent.getFullName());
            remindDTO.setJobId(job.getId());
            remindDTO.setJobTitle(job.getTitle());
            remindDTO.setSubmitUserFullName(user.getFullName());
            remindDTO.setEmails(needSendLarkMessageEmails);
            remindDTO.setTalentDetailLink(talentUrl);

            // 发送消息（公共逻辑）
            commonClient.noPoachingSendMessageByUserEmails(remindDTO);
        });
    }

    private void addRecommendFeedback(RecommendFeedback recommendFeedback) {
        if(recommendFeedback == null) {
            return;
        }
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            recommendFeedback.setReason(RecommendFeedbackReason.ADD_TO_POSITION);
            talentClient.recordTalentJobRecommend(recommendFeedback);
        });
    }

    /**
     * 获取所有禁猎公司（调CRM接口）
     * @return
     */
    private NoPoachingAccountResultVO getNoPoachingResult() {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", getAuthorizationHeader());
        httpHeaders.set("Content-Type", "application/json");

        try {
            HttpResponse response = httpService.get(applicationProperties.getCrmUrl() + CRM_GET_NO_POACHING_COMPANIES_LIST_URL, convertToOkHttpHeaders(httpHeaders));
            if (response != null &&  HttpStatus.OK.value() == response.getCode()) {
                return JSONUtil.toBean(response.getBody(), NoPoachingAccountResultVO.class);
            } else {
                log.error("find all noPoaching company result, response: {}", response);
            }
        } catch (Exception e) {
            log.error("find all noPoaching company result error: {}", e.getMessage());
        }
        return null;
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    public static Headers convertToOkHttpHeaders(HttpHeaders httpHeaders) {
        Headers.Builder builder = new Headers.Builder();
        httpHeaders.forEach((key, values) -> {
            for (String value : values) {
                builder.add(key, value);
            }
        });
        return builder.build();
    }

    private void sendAppliedEmail(TalentRecruitmentProcess talentRecruitmentProcess) {
        List<TalentRecruitmentProcessKpiUser> kpiUserList = talentRecruitmentProcessKpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(talentRecruitmentProcess.getId(), Arrays.asList(UserRole.AM,UserRole.CO_AM, UserRole.SOURCER, UserRole.RECRUITER));
        if (CollUtil.isNotEmpty(kpiUserList)) {
            List<Long> userIds = kpiUserList.stream().map(TalentRecruitmentProcessKpiUser::getUserId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(userIds)) {
                List<String> emailAddressList = userClient.getAllBriefUsersByIds(userIds).getBody().stream().map(UserBriefDTO::getEmail).collect(Collectors.toList());
                sendEmail(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getJobId(), emailAddressList, null, true, null);
            }
        }
    }

    private void sendStatusChangeEmail(TalentRecruitmentProcess process, NodeType oldStatus, NodeType newStatus) {
        List<String> emailAddressList = new ArrayList<>();
        List<TalentRecruitmentProcessKpiUser> kpiUserList = talentRecruitmentProcessKpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(
                process.getId(), Arrays.asList(UserRole.AM, UserRole.CO_AM, UserRole.SOURCER, UserRole.RECRUITER));
        if (CollUtil.isNotEmpty(kpiUserList)) {
            emailAddressList.addAll(userService.findBriefUsers(kpiUserList.stream().map(TalentRecruitmentProcessKpiUser::getUserId).collect(Collectors.toList()))
                    .stream().map(UserBriefDTO::getEmail).collect(Collectors.toList()));
        }
        String fromStatusTo = "from [" + oldStatus.getDescription() + "] to [" + newStatus.getDescription() + "]";
        sendEmail(process.getTalentId(), process.getJobId(), emailAddressList, fromStatusTo, false, null);
    }

    private void sendEliminateEmail(TalentRecruitmentProcess process, NodeType status, String eliminateNote) {
        List<String> emailAddressList = new ArrayList<>();
        List<TalentRecruitmentProcessKpiUser> kpiUserList = talentRecruitmentProcessKpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(
                process.getId(), Arrays.asList(UserRole.AM, UserRole.CO_AM, UserRole.RECRUITER, UserRole.SOURCER, UserRole.DM, UserRole.AC));
        log.info("[TalentRecruitmentProcessService] Ready to send eliminated application email to: {}", kpiUserList);
        if (CollUtil.isNotEmpty(kpiUserList)) {
            emailAddressList.addAll(userService.findBriefUsers(kpiUserList.stream().map(TalentRecruitmentProcessKpiUser::getUserId).collect(Collectors.toList()))
                    .stream().map(UserBriefDTO::getEmail).collect(Collectors.toList()));
        }
        String fromStatusTo = "from [" + status.getDescription() + "] to [Eliminated]";
        sendEmail(process.getTalentId(), process.getJobId(), emailAddressList, fromStatusTo, false, eliminateNote);
    }

    private void sendEliminateEmailForAuto(TalentRecruitmentProcess process, NodeType status, String eliminateNote) {
        List<String> emailAddressList = new ArrayList<>();
        List<TalentRecruitmentProcessKpiUser> kpiUserList = talentRecruitmentProcessKpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(
                process.getId(), Arrays.asList(UserRole.AM, UserRole.CO_AM, UserRole.RECRUITER, UserRole.SOURCER, UserRole.DM, UserRole.AC));
        log.info("[TalentRecruitmentProcessService] Ready to send eliminated application email to: {}", kpiUserList);
        if (CollUtil.isNotEmpty(kpiUserList)) {
            emailAddressList.addAll(userService.findBriefUsers(kpiUserList.stream().map(TalentRecruitmentProcessKpiUser::getUserId).collect(Collectors.toList()))
                    .stream().map(UserBriefDTO::getEmail).collect(Collectors.toList()));
        }
        String fromStatusTo = "from [" + status.getDescription() + "] to [Eliminated]";
        sendEmailForAuto(process.getTalentId(), process.getJobId(), emailAddressList, fromStatusTo, false, eliminateNote);
    }

    private void sendEmail(Long talentId, Long jobId, List<String> emailAddressList, String fromStatusTo, boolean isAppliedEmail, String eliminateNote) {
        boolean isPrivateJob = CollectionUtils.isNotEmpty(recruitmentProcessRepository.checkPrivateJob(jobId));
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String talentName = talentClient.findFullNameByTalentId(talentId).getBody();
                JobDTOV3 job = jobClient.getJob(jobId).getBody();
                String jobTitle = (job != null) ? job.getTitle() : "";
                CompanyDTO company = companyService.getCompany(job.getCompanyId());
                String jobCompany = (company != null) ? company.getName() : "";
                String jobUrl = applicationProperties.getBaseUrl() + "/jobs/detail/" + jobId;
                if (isPrivateJob) {
                    jobUrl = applicationProperties.getBaseUrl() + "/jobs/detail/private/" + jobId;
                }
                String talentUrl = applicationProperties.getBaseUrl() + "/candidates/detail/" + talentId;
                String subject;
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                if (isAppliedEmail) {
                    UserBriefDTO userVO = userService.getUserById(SecurityUtils.getUserId());
                    subject = CommonUtils.formatFullName(userVO.getFirstName(), userVO.getLastName()) + " Submitted " + talentName + " To " + jobTitle;
                } else {
                    subject = "Status Update：" + talentName + "'s status updated " + fromStatusTo;
                }
                HtmlUtil.appendParagraphCell(sb, " Candidate: " + talentName + "<a href=\"" + talentUrl + "\">" + " #" + talentId + "</a>");
                HtmlUtil.appendParagraphCell(sb, " Job Title: " + jobTitle + "<a href=\"" + jobUrl + "\">" + " #" + jobId + "</a>");
                if (job.getCode() != null) {
                    HtmlUtil.appendParagraphCell(sb, " Job Code: " + job.getCode());
                }
                HtmlUtil.appendParagraphCell(sb, " Job Company: " + jobCompany);
                if (eliminateNote != null) {
                    HtmlUtil.appendParagraphCell(sb, " Eliminated Reason: " + eliminateNote);
                }
                HtmlUtil.appendParagraphCell(sb, " Date(US): " + DateUtil.utcTimeToTargetZoneTime(DateUtil.currentTime("yyyy-MM-dd HH:mm:ss"), DateUtil.US_LA_TIMEZONE));
                HtmlUtil.appendParagraphCell(sb, " Date(CN): " + DateUtil.utcTimeToTargetZoneTime(DateUtil.currentTime("yyyy-MM-dd HH:mm:ss"), DateUtil.CN_BJ_TIMEZONE));
                HtmlUtil.appendParagraphCell(sb, " Date(UTC): " + DateUtil.currentTime());
                sb.append("</body>");
                MailVM mailVm = new MailVM(applicationProperties.getSupportSender(), emailAddressList, null, null, subject, sb.toString(), null, true);
                try {
                    LoginUtil.simulateLoginWithClient();
                    mailService.sendHtmlMail(mailVm);
                } catch (Exception e) {
                    log.error("[MailGenerateService] Send email {} Error:{}", mailVm.toString(), e);
                }
            }
        });
    }

    private void sendEmailForAuto(Long talentId, Long jobId, List<String> emailAddressList, String fromStatusTo, boolean isAppliedEmail, String eliminateNote) {
        boolean isPrivateJob = CollectionUtils.isNotEmpty(recruitmentProcessRepository.checkPrivateJob(jobId));
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String talentName = talentClient.findFullNameByTalentId(talentId).getBody();
                JobDTOV3 job = jobClient.findOneBasicInfoNoToken(jobId).getBody();
                String jobTitle = (job != null) ? job.getTitle() : "";
                CompanyDTO company = companyService.getCompany(job.getCompanyId());
                String jobCompany = (company != null) ? company.getName() : "";
                String jobUrl = applicationProperties.getBaseUrl() + "/jobs/detail/" + jobId;
                if (isPrivateJob) {
                    jobUrl = applicationProperties.getBaseUrl() + "/jobs/detail/private/" + jobId;
                }
                String talentUrl = applicationProperties.getBaseUrl() + "/candidates/detail/" + talentId;
                String subject;
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                if (isAppliedEmail) {
                    UserBriefDTO userVO = userService.getUserById(SecurityUtils.getUserId());
                    subject = CommonUtils.formatFullName(userVO.getFirstName(), userVO.getLastName()) + " Submitted " + talentName + " To " + jobTitle;
                } else {
                    subject = "Status Update：" + talentName + "'s status updated " + fromStatusTo;
                }
                HtmlUtil.appendParagraphCell(sb, " Candidate: " + talentName + "<a href=\"" + talentUrl + "\">" + " #" + talentId + "</a>");
                HtmlUtil.appendParagraphCell(sb, " Job Title: " + jobTitle + "<a href=\"" + jobUrl + "\">" + " #" + jobId + "</a>");
                if (job.getCode() != null) {
                    HtmlUtil.appendParagraphCell(sb, " Job Code: " + job.getCode());
                }
                HtmlUtil.appendParagraphCell(sb, " Job Company: " + jobCompany);
                if (eliminateNote != null) {
                    HtmlUtil.appendParagraphCell(sb, " Eliminated Reason: " + eliminateNote);
                }
                HtmlUtil.appendParagraphCell(sb, " Date(US): " + DateUtil.utcTimeToTargetZoneTime(DateUtil.currentTime("yyyy-MM-dd HH:mm:ss"), DateUtil.US_LA_TIMEZONE));
                HtmlUtil.appendParagraphCell(sb, " Date(CN): " + DateUtil.utcTimeToTargetZoneTime(DateUtil.currentTime("yyyy-MM-dd HH:mm:ss"), DateUtil.CN_BJ_TIMEZONE));
                HtmlUtil.appendParagraphCell(sb, " Date(UTC): " + DateUtil.currentTime());
                sb.append("</body>");
                MailVM mailVm = new MailVM(applicationProperties.getSupportSender(), emailAddressList, null, null, subject, sb.toString(), null, true);
                try {
                    mailService.sendHtmlMail(mailVm);
                } catch (Exception e) {
                    log.error("[MailGenerateService] Send email {} Error:{}", mailVm.toString(), e);
                }
            }
        });
    }

    @Override
    @Transactional
    public TalentRecruitmentProcessVO submitToClient(TalentRecruitmentProcessSubmitToClientVO submitToClientVO, boolean noteOnly) {
        StopWatch stopWatch = new StopWatch("echocheng7 start " + submitToClientVO.getTalentRecruitmentProcessId());

        stopWatch.start("[0] Validate recruitment process, talent recruitment process and first party");
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(submitToClientVO.getTalentRecruitmentProcessId());
        validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());
        stopWatch.stop();

        stopWatch.start("[1] Filter out owner in kpi users");
        if (CollectionUtils.isNotEmpty(submitToClientVO.getKpiUsers())) {
            submitToClientVO.setKpiUsers(submitToClientVO.getKpiUsers().stream().filter(u -> !UserRole.OWNER.equals(u.getUserRole())).collect(Collectors.toList()));
        }
        stopWatch.stop();

        stopWatch.start("[2] Check data integrity, if talent recruitment process has current node(active/eliminate)");
        //get current node status
        Boolean sendEmailFlag = false;
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(submitToClientVO.getTalentRecruitmentProcessId());
        //Compatible historical data, and notifying users
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        stopWatch.stop();

        stopWatch.start("[3] Check process status, if update note only");
        if (NodeType.SUBMIT_TO_CLIENT.toDbValue() > currentNode.getNodeType().toDbValue()) {
            // new to this node, create one and send status change email
            sendEmailFlag = true;
        } else if (noteOnly || NodeType.SUBMIT_TO_CLIENT.toDbValue() < currentNode.getNodeType().toDbValue() || NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
            // this node is a complete node or the application has been terminated here, only note is allowed to update
            talentRecruitmentProcessSubmitToClientService.updateNoteOnly(submitToClientVO);
            if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) ||
                    (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                            && NodeType.OFFER.toDbValue() >= currentNode.getNodeType().toDbValue())) {
                kpiUserService.save(submitToClientVO.getTalentRecruitmentProcessId(), submitToClientVO.getKpiUsers());
            }
            if (Objects.equals(NodeType.SUBMIT_TO_CLIENT.toDbValue(), currentNode.getNodeType().toDbValue()) && !NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                updateTalentRecruitmentProcessNoteOnly(submitToClientVO.getTalentRecruitmentProcessId(), submitToClientVO.getNote());
            }
            return toVO(submitToClientVO.getTalentRecruitmentProcessId());
        }
        stopWatch.stop();

        stopWatch.start("[4] Save submit to client");
        talentRecruitmentProcessSubmitToClientService.save(submitToClientVO);
        stopWatch.stop();

        stopWatch.start("[5] Complete current node and active next node");
        talentRecruitmentProcessNodeService.completeCurrentNodeAndActiveNextNode(talentRecruitmentProcess, NodeType.SUBMIT_TO_CLIENT);
        stopWatch.stop();

        stopWatch.start("[6] Update talent recruitment process last node note and auditing info");
        updateTalentRecruitmentProcess(talentRecruitmentProcess, submitToClientVO.getNote());
        stopWatch.stop();

        //send status change email
        if (sendEmailFlag) {
            stopWatch.start("[7] Send status change email");
            sendStatusChangeEmail(talentRecruitmentProcess, currentNode.getNodeType(), NodeType.SUBMIT_TO_CLIENT);
            stopWatch.stop();
        }

        stopWatch.start("[8] Construct result DTO");
        TalentRecruitmentProcessVO res = toVO(submitToClientVO.getTalentRecruitmentProcessId());
        stopWatch.stop();

        log.info("[Application timelapse result] submitToClient: {}", stopWatch.prettyPrint());
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(submitToClientVO.getTalentRecruitmentProcessId(), CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS, List.of(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS));
        });
        return res;
    }

    @Resource
    private CalendarService calendarService;

    private void notifyCompleteSystemCalendar(Long talentRecruitmentProcessId, CalendarRelationEnum calendarRelation, List<CalendarTypeEnum> type) {
        if(talentRecruitmentProcessId == null) {
            return;
        }
        CompleteSystemCalendarDTO dto = new CompleteSystemCalendarDTO();
        dto.setRelationType(calendarRelation);
        dto.setType(type);
        dto.setUniqueReferenceId(talentRecruitmentProcessId);
        calendarService.completeSystemCalendar(dto);
    }

    @Override
    public void checkPermission(Long talentRecruitmentProcessId) {
        if (BooleanUtils.isTrue(SecurityUtils.isAdmin())) {
            return;
        }
        if (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            return;
        }
        Long companyId = recruitmentProcessRepository.getCompanyIdByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (Objects.isNull(companyId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_CHECKPERMISSIONCOMPANYIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        List<Long> allAmIdsByCompanyId = companyService.getAllAmIdsByCompanyId(companyId);
        boolean isCurrentCompanyAm = allAmIdsByCompanyId.contains(SecurityUtils.getUserId());
        if (BooleanUtils.isFalse(isCurrentCompanyAm)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_CHECKPERMISSIONISCURRENTCOMPANYAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
    }

    @Override
    public TalentRecruitmentProcessVO interview(TalentRecruitmentProcessInterviewVO interviewVO, boolean noteOnly) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(interviewVO.getTalentRecruitmentProcessId());
        validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());

        //get current node status
        Boolean sendEmailFlag = false;
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(interviewVO.getTalentRecruitmentProcessId());
        //Compatible historical data, and notifying users
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (NodeType.INTERVIEW.toDbValue() > currentNode.getNodeType().toDbValue()) {
            // new to this node, create one and send status change email
            sendEmailFlag = true;
        } else if (noteOnly || NodeType.INTERVIEW.toDbValue() < currentNode.getNodeType().toDbValue() || NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
            // this node is a complete node or the application has been terminated here, only note is allowed to update
            talentRecruitmentProcessInterviewService.updateNoteOnly(interviewVO);
            if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) ||
                    (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                            && NodeType.OFFER.toDbValue() >= currentNode.getNodeType().toDbValue())) {
                kpiUserService.save(interviewVO.getTalentRecruitmentProcessId(), interviewVO.getKpiUsers());
            }
            if (Objects.equals(NodeType.INTERVIEW.toDbValue(), currentNode.getNodeType().toDbValue()) && !NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                updateTalentRecruitmentProcessNoteOnly(interviewVO.getTalentRecruitmentProcessId(), interviewVO.getNote());
            }
            return toVO(interviewVO.getTalentRecruitmentProcessId());
        }

        TalentRecruitmentProcessInterviewVO vo = talentRecruitmentProcessInterviewService.save(interviewVO);
        talentRecruitmentProcessNodeService.completeCurrentNodeAndActiveNextNode(talentRecruitmentProcess, NodeType.INTERVIEW);
        updateTalentRecruitmentProcess(talentRecruitmentProcess, interviewVO.getNote());
        //send status change email
        if (sendEmailFlag) {
            sendStatusChangeEmail(talentRecruitmentProcess, currentNode.getNodeType(), NodeType.INTERVIEW);
        }
        TalentRecruitmentProcessVO res = toVO(interviewVO.getTalentRecruitmentProcessId());

        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            addCalendar(res, vo,null);
        });

        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(interviewVO.getTalentRecruitmentProcessId(), CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS, List.of(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS));
            notifyCompleteSystemCalendar(talentRecruitmentProcess.getJobId(), CalendarRelationEnum.JOB, List.of(CalendarTypeEnum.NO_INTERVIEW));
        });
        return res;
    }

    private void addCalendar(TalentRecruitmentProcessVO res,TalentRecruitmentProcessInterviewVO interviewVO,TalentRecruitmentProcessOfferVO onboardVO){

        CalendarEventVO eventVO = null;
        if (interviewVO != null) {
            eventVO = commonClient.getCalendarEventByTypeIdAndReferenceId(9, interviewVO.getId()).getBody();
        } else {
            eventVO = commonClient.getCalendarEventByTypeIdAndReferenceId(10, onboardVO.getId()).getBody();
        }

        CalendarEventDTO dto = new CalendarEventDTO();

        dto.setCalendarType(CalendarTypeEnum.DEADLINE);
        dto.setPriority(CalendarPriorityEnum.HIGH);

        String lang = commonRedisService.get(CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()));

        dto.setGoToId(res.getId());
        if (interviewVO != null) {
            List<EnumTimezone> enumTimezones = enumTimezoneRepository.findByTimezone(interviewVO.getTimeZone());
            dto.setTitle(StringUtils.isBlank(lang) || lang.equals("EN") ? "Candidate Interview" : "候选人面试");
            dto.setDescription(StringUtils.isBlank(lang) || lang.equals("EN") ? "Candidate Interview" : "候选人面试");
            dto.setStartTime(adjustInstant(interviewVO.getFromTime(), enumTimezones.get(0).getStandardOffset()));
            dto.setEndTime(adjustInstant(interviewVO.getToTime(), enumTimezones.get(0).getStandardOffset()));
            dto.setTypeId(9);
            dto.setReferenceId(interviewVO.getId());
        } else {
            dto.setTitle(StringUtils.isBlank(lang) || lang.equals("EN") ? "Candidate Onboard" : "候选人入职");
            dto.setDescription(StringUtils.isBlank(lang) || lang.equals("EN") ? "Candidate Onboard" : "候选人入职");
            List<EnumTimezone> enumTimezones = enumTimezoneRepository.findByTimezone(SecurityUtils.getUserTimeZone());
            Instant[] time = getStartAndEndOfDay(onboardVO.getEstimateOnboardDate());
            dto.setStartTime(adjustInstant(time[0], enumTimezones.get(0).getStandardOffset()));
            dto.setEndTime(adjustInstant(time[1], enumTimezones.get(0).getStandardOffset()));
            dto.setTypeId(10);
            dto.setReferenceId(onboardVO.getId());
        }

        dto.setTenantId(SecurityUtils.getTenantId());
        CalendarEventAttendeeDTO attendeeDTO = new CalendarEventAttendeeDTO();
        attendeeDTO.setEmail(SecurityUtils.getEmail());
        attendeeDTO.setIsReminder(CalendarEventAttendeeReminderTypeEnum.NO_REMINDER);
        attendeeDTO.setIsOrganizer(CalendarEventAttendeeTypeEnum.ORGANIZER);
        attendeeDTO.setUserId(SecurityUtils.getUserId());
        dto.setAttendees(Arrays.asList(attendeeDTO));

        CalendarEventRelationDTO relationDTO = new CalendarEventRelationDTO();
        relationDTO.setRelationId(res.getTalentId());
        relationDTO.setRelationType(CalendarRelationEnum.CANDIDATE);
        relationDTO.setRelationName(res.getTalent().getFullName());
        dto.setRelationList(Arrays.asList(relationDTO));

        if (null != eventVO && eventVO.getId() != null) {
            dto.setId(eventVO.getId());
            commonClient.updateCalendarEvent(dto);
        } else {
            commonClient.createCalendarEvent(dto);
        }

    }

    public static Instant adjustInstant(Instant instant, String hourStr) {
        if (instant == null || hourStr == null || hourStr.isEmpty()) {
            throw new IllegalArgumentException("参数 instant 和 hourStr 不能为空");
        }

        // 校验格式（正负号开头，后跟 HH:mm）
        if (!hourStr.matches("^[+-]\\d{1,2}:\\d{2}$")) {
            throw new IllegalArgumentException("小时字符串格式无效，应为 +HH:mm 或 -HH:mm");
        }

        // 解析小时和分钟
        String[] parts = hourStr.substring(1).split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);

        // 根据正负号计算总偏移时间
        Duration offsetDuration = Duration.ofHours(hours).plusMinutes(minutes);
        if (hourStr.startsWith("-")) {
            offsetDuration = offsetDuration.negated();
        }

        // 返回调整后的 Instant
        return instant.minus(offsetDuration);
    }

    public static Instant[] getStartAndEndOfDay(LocalDate date) {

        // 构造当天的 00:00:00 和 23:30:00 时间
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(23, 30);

        // 转换为 Instant
        Instant startInstant = startOfDay.toInstant(ZoneOffset.UTC);
        Instant endInstant = endOfDay.toInstant(ZoneOffset.UTC);

        // 返回两个 Instant
        return new Instant[]{startInstant, endInstant};
    }

    @Override
    public TalentRecruitmentProcessVO offer(TalentRecruitmentProcessOfferVO offerVO, boolean noteOnly) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(offerVO.getTalentRecruitmentProcessId());
        validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());
        //get current node status
        Boolean sendEmailFlag = false;
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(offerVO.getTalentRecruitmentProcessId());
        //Compatible historical data, and notifying users
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (NodeType.OFFER.toDbValue() > currentNode.getNodeType().toDbValue()) {
            // new to this node, create one and send status change email
            sendEmailFlag = true;
        } else if (noteOnly || NodeType.OFFER.toDbValue() < currentNode.getNodeType().toDbValue() || NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
            // this node is a complete node or the application has been terminated here, only note is allowed to update
            talentRecruitmentProcessOfferService.updateNoteOnly(offerVO);
            if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) ||
                    (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                            && NodeType.OFFER.toDbValue() >= currentNode.getNodeType().toDbValue())) {
                kpiUserService.save(offerVO.getTalentRecruitmentProcessId(), offerVO.getKpiUsers());
            }
            if (Objects.equals(NodeType.OFFER.toDbValue(), currentNode.getNodeType().toDbValue()) && !NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                updateTalentRecruitmentProcessNoteOnly(offerVO.getTalentRecruitmentProcessId(), offerVO.getNote());
            }
            return toVO(offerVO.getTalentRecruitmentProcessId());
        }

        TalentRecruitmentProcessOfferVO vo = talentRecruitmentProcessOfferService.save(offerVO);
        talentRecruitmentProcessNodeService.completeCurrentNodeAndActiveNextNode(talentRecruitmentProcess, NodeType.OFFER);
        updateTalentRecruitmentProcess(talentRecruitmentProcess, offerVO.getNote());
        //send status change email
        if (sendEmailFlag) {
            sendStatusChangeEmail(talentRecruitmentProcess, currentNode.getNodeType(), NodeType.OFFER);
        }
        TalentRecruitmentProcessVO res = toVO(offerVO.getTalentRecruitmentProcessId());
        if (sendEmailFlag) {
            res.setFirstTimeToThisNode(true);
        }

        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            calendarComplete(offerVO.getTalentRecruitmentProcessId());
            addCalendar(res, null, vo);
        });
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(offerVO.getTalentRecruitmentProcessId(), CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS, List.of(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS));
        });
        return res;
    }

    private void calendarComplete(Long talentRecruitmentProcessId){
       List<TalentRecruitmentProcessInterviewVO> interviewVOS = talentRecruitmentProcessInterviewService.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if(!interviewVOS.isEmpty()){
            List<Long> ids = interviewVOS.stream().map(TalentRecruitmentProcessInterviewVO::getId).collect(Collectors.toList());
            List<Long> eventIds = talentRecruitmentProcessInterviewRepository.findCalendarEventId(ids);
            if(!eventIds.isEmpty()){
                talentRecruitmentProcessInterviewRepository.updateStatusByEventId(eventIds);
            }
        }
    }

    @Override
    public TalentRecruitmentProcessVO offerAccept(TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO, boolean noteOnly) {
        try {
            TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(offerAcceptVO.getTalentRecruitmentProcessId());
            validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());
            //get current node status
            Boolean sendEmailFlag = false;
            TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(offerAcceptVO.getTalentRecruitmentProcessId());
            //Compatible historical data, and notifying users
            if (ObjectUtil.isEmpty(currentNode)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
            if (NodeType.OFFER_ACCEPT.toDbValue() > currentNode.getNodeType().toDbValue()) {
                // new to this node, create one and send status change email
                sendEmailFlag = true;
            } else if (noteOnly || NodeType.OFFER_ACCEPT.toDbValue() < currentNode.getNodeType().toDbValue() || NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                // this node is a complete node or the application has been terminated here, only note is allowed to update
                ipgOfferAcceptService.updateNoteOnly(offerAcceptVO);
                if (Objects.equals(NodeType.OFFER_ACCEPT.toDbValue(), currentNode.getNodeType().toDbValue()) && !NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                    updateTalentRecruitmentProcessNoteOnly(offerAcceptVO.getTalentRecruitmentProcessId(), offerAcceptVO.getNote());
                }
                return toVO(offerAcceptVO.getTalentRecruitmentProcessId());
            }
            ipgOfferAcceptService.save(talentRecruitmentProcess, offerAcceptVO);
            talentRecruitmentProcessNodeService.completeCurrentNodeAndActiveNextNode(talentRecruitmentProcess, NodeType.OFFER_ACCEPT);
            updateTalentRecruitmentProcess(talentRecruitmentProcess, offerAcceptVO.getNote());
            //send offerAccepted email
            sendOfferAcceptedEmail(talentRecruitmentProcess);
            //send status change email
            if (sendEmailFlag) {
                sendStatusChangeEmail(talentRecruitmentProcess, currentNode.getNodeType(), NodeType.OFFER_ACCEPT);
            }
            TalentRecruitmentProcessVO result = toVO(offerAcceptVO.getTalentRecruitmentProcessId());
            xxlJobService.unOnboardedReminder(result);
            // send congrats notification
            try {
                BigDecimal feeCharge = Objects.nonNull(offerAcceptVO.getFeeCharge()) ? offerAcceptVO.getFeeCharge().getTotalAmount() : offerAcceptVO.getContractFeeCharge().getGp();
                this.sendCongratsNotification(talentRecruitmentProcess.getTalentId(), talentService.getTalentBrief(talentRecruitmentProcess.getTalentId()).getFullName(), offerAcceptVO.getKpiUsers(), feeCharge, offerAcceptVO.getCurrency(), offerAcceptVO.getOnboardDate());
            }catch (Exception e){
                log.error("send congrats notification error {}", e.getMessage());
            }
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                LocalDate onboardDate = offerAcceptVO.getOnboardDate();
                // 检查onboardDate是否大于当前日期
                boolean isAfterCurrentDate = onboardDate.isAfter(LocalDate.now());

                notifyCompleteSystemCalendar(offerAcceptVO.getTalentRecruitmentProcessId(), CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS, isAfterCurrentDate ? List.of(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS, CalendarTypeEnum.OFFER_PASS_NOT_UPDATE_STATUS) : List.of(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS));
            });
            return result;
        } catch (Exception e) {
            log.error("[Exception Here] with error msg: {}", e.getMessage(), e);
            throw new CustomParameterizedException("exception: " + e.getMessage());
        }
    }

    @Override
    public void sendCongratsNotification(Long talentRecruitmentProcessId) throws IOException {
        List<TalentRecruitmentProcessKpiUserVO> kpiUserVOS = kpiUserService.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        List<CongratsContent> congratsContents = talentRecruitmentProcessIpgOfferAcceptRepository.getCongratsContent(talentRecruitmentProcessId);
        if (congratsContents.isEmpty()){
            return;
        }
        CongratsContent congratsContent = congratsContents.get(0);
        this.sendCongratsNotification(congratsContent.getTalentId(), congratsContent.getTalentName(), kpiUserVOS, congratsContent.getFeeCharge(), congratsContent.getCurrency(), congratsContent.getOnboardDate());
    }

    private void sendCongratsNotification(Long talentId, String talentName, List<TalentRecruitmentProcessKpiUserVO> kpiUsers, BigDecimal feeCharge, Integer currency, LocalDate offerOnboardDate) throws IOException {
        log.info("send congrats notification: kpiUsers {}, feeCharge {}, currency {}, offerOnboardDate {}", kpiUsers, feeCharge, currency, offerOnboardDate);
        Long tenantId = SecurityUtils.getTenantId();
        Optional<CongratsOfferNotification> congratsOfferNotificationOpt = congratsOfferNotificationRepository.findFirstByTenantId(tenantId);
        if (congratsOfferNotificationOpt.isEmpty()){
            return;
        }

        // 1. BD-AM-Recruiter-Sourcer & TEAM
        Set<Long> kpiUserIds = kpiUsers.stream().map(TalentRecruitmentProcessKpiUserVO::getUserId).collect(Collectors.toSet());

        Map<Long, String> bdMap = new HashMap<>();
        Map<Long, String> amMap = new HashMap<>();
        Map<Long, String> recruiterMap = new HashMap<>();
        Map<Long, String> sourcerMap = new HashMap<>();
        Map<Long, String> userIdToName = userService.findBriefUsers(kpiUserIds).stream().collect(Collectors.toMap(UserBriefDTO::getId, UserBriefDTO::getFullName));
        for (TalentRecruitmentProcessKpiUserVO kpiUser : kpiUsers) {
            UserRole userRole = kpiUser.getUserRole();
            switch (userRole){
                case BD_OWNER -> bdMap.put(kpiUser.getUserId(), userIdToName.get(kpiUser.getUserId()));
                case AM -> amMap.put(kpiUser.getUserId(), userIdToName.get(kpiUser.getUserId()));
                case RECRUITER -> recruiterMap.put(kpiUser.getUserId(), userIdToName.get(kpiUser.getUserId()));
                case SOURCER -> sourcerMap.put(kpiUser.getUserId(), userIdToName.get(kpiUser.getUserId()));
            }
        }

        List<String> list = new ArrayList<>();
        if (!bdMap.isEmpty()){
            list.add("@" + String.join(" @", bdMap.values()) + " BD");
        }

        if (!amMap.isEmpty()){
            list.add("@" + String.join(" @", amMap.values()) + " AM");
        }

        if (!sourcerMap.isEmpty()){
            list.add("@" + String.join(" @", sourcerMap.values()) + " Sourcer");
        }

        String usersForThanks = String.join(", ", list);
        int lastCommaIndex = usersForThanks.lastIndexOf(",");
        if (lastCommaIndex > 0){
            usersForThanks = usersForThanks.substring(0, lastCommaIndex) + " and" + usersForThanks.substring(lastCommaIndex + 1);
        }

        // 2. 成单金额，美元
        BigDecimal currencyRate = talentRecruitmentProcessIpgOfferAcceptRepository.getCurrencyRate(currency);
        int usdAmount = feeCharge.divide(currencyRate, RoundingMode.CEILING).intValue();

        // 3. Onboard Date
        String onboardDate = DateUtil.formatDateWithSuffix(offerOnboardDate);
        List<String> bds = bdMap.values().stream().toList();
        List<String> ams = amMap.values().stream().toList();
        List<String> recruiters = recruiterMap.values().stream().toList();
        List<String> sourcers = sourcerMap.values().stream().toList();
        List<String> allUsers = new ArrayList<>(bds);

        for (String am : ams) {
            if (!allUsers.contains(am)){
                allUsers.add(am);
            }
        }
        for (String recruiter : recruiters) {
            if (!allUsers.contains(recruiter)){
                allUsers.add(recruiter);
            }
        }

        for (String sourcer : sourcers) {
            if (!allUsers.contains(sourcer)){
                allUsers.add(sourcer);
            }
        }

        CongratsOfferNotification congratsOfferNotification = congratsOfferNotificationOpt.get();
        List<String> secondLevelTeamLeaders = new ArrayList<>();
        if (StringUtils.isNotBlank(congratsOfferNotification.getTeamName())){
            secondLevelTeamLeaders.add(congratsOfferNotification.getTeamName());
        }else {
            Map<Long, List<String>> secondLevelTeamLeader = talentRecruitmentProcessIpgOfferAcceptRepository.getSecondLevelTeamLeaders(kpiUserIds, tenantId)
                    .stream()
                    .collect(Collectors.groupingBy(CongratsUserTeam::getUserId, Collectors.mapping(CongratsUserTeam::getLeaderName, Collectors.toList())));
            secondLevelTeamLeaders = Stream.of(bdMap.keySet(), amMap.keySet(), recruiterMap.keySet(), sourcerMap.keySet())
                    .flatMap(Collection::stream)
                    .map(userId -> secondLevelTeamLeader.getOrDefault(userId, new ArrayList<>()))
                    .flatMap(List::stream)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .toList();
        }
        String serviceFee = "$" + NumberFormat.getNumberInstance(Locale.US).format(usdAmount);
        CongratsNotification congratsNotification = new CongratsNotification()
                .setAmount(serviceFee)
//                .setOnboardDate(onboardDate)
                .setUsers(String.join(";", allUsers.subList(0, Math.min(3, allUsers.size()))))
//                .setRecruitersForText("@" + String.join(" @", recruiters))
//                .setUsersForThanks(usersForThanks)
                .setMessage(generateMessage(recruiters, bds, ams, sourcers, serviceFee, onboardDate))
                .setTeams(String.join("/", secondLevelTeamLeaders.isEmpty() ? List.of("--") : secondLevelTeamLeaders))
                .setCandidateProfile(String.format("[%s](%s/candidates/detail/%d)", talentName, congratsOfferNotification.getWebUrl(), talentId));

        log.info("send congrats notification: {}", JsonUtil.toJson(congratsNotification.toString()));

        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put("Authorization", congratsOfferNotification.getNotificationToken());
        httpService.post(congratsOfferNotification.getNotificationUrl(), Headers.of(headersBuilder), JsonUtil.toJson(congratsNotification));
    }

    public static String generateMessage(List<String> recruiters, List<String> bds, List<String> ams, List<String> sourcers,
                                         String serviceFee,
                                         String onboardDate) {
        String recruiterPart = formatList(recruiters);
        String bdPart = formatList(bds);
        String amPart = formatList(ams);
        String sourcerPart = formatList(sourcers);

        // 初始祝贺语
        String message = String.format("Hey Guys, let's congratulate %s, their candidate will be on board on %s. Service fee total is %s.", recruiterPart, onboardDate, serviceFee);

        // 所有人都相同
        HashSet<String> uniqueRecruiters = new HashSet<>(recruiters);
        HashSet<String> uniqueBds = new HashSet<>(bds);
        HashSet<String> uniqueAms = new HashSet<>(ams);
        HashSet<String> uniqueSourcers = new HashSet<>(sourcers);
        Set<String> allUsers = new HashSet<>();
        allUsers.addAll(uniqueRecruiters);
        allUsers.addAll(uniqueBds);
        allUsers.addAll(uniqueAms);
        allUsers.addAll(uniqueSourcers);
        if (uniqueRecruiters.size() == allUsers.size()) {
            return message + " Great job!!! Congrats!!!";
        }

        // BD/AM/Sourcer 三者都相同
        if (equalSets(bds, ams, sourcers) && StringUtils.isNotBlank(bdPart)) {
            message += String.format(" Thanks for %s BD, AM and Sourcer.", bdPart);
        } else {
            // 两两合并情况 + 独立输出
            List<String> parts = new ArrayList<>();
            Set<String> printed = new HashSet<>();

            if (equalSets(bds, ams) && StringUtils.isNotBlank(bdPart)) {
                parts.add(String.format("%s BD and AM", bdPart));
                printed.addAll(bds);
            } else if (equalSets(bds, sourcers) && StringUtils.isNotBlank(bdPart)) {
                parts.add(String.format("%s BD and Sourcer", bdPart));
                printed.addAll(bds);
            } else if (equalSets(ams, sourcers) && StringUtils.isNotBlank(amPart)) {
                parts.add(String.format("%s AM and Sourcer", amPart));
                printed.addAll(ams);
            }

            // 单独输出未被合并的
            if (!printed.containsAll(bds)) {
                Set<String> rest = new HashSet<>(bds);
                if (!rest.isEmpty())
                    parts.add(String.format("%s BD", formatList(rest)));
            }
            if (!printed.containsAll(ams)) {
                Set<String> rest = new HashSet<>(ams);
                if (!rest.isEmpty())
                    parts.add(String.format("%s AM", formatList(rest)));
            }
            if (!printed.containsAll(sourcers)) {
                Set<String> rest = new HashSet<>(sourcers);
                if (!rest.isEmpty())
                    parts.add(String.format("%s Sourcer", formatList(rest)));
            }

            if (!parts.isEmpty()) {
                message += " Thanks for " + String.join(" and ", parts) + ".";
            }
        }

        message += " Great job!!! Congrats!!!";
        return message;
    }

    // 支持两个集合比较
    private static boolean equalSets(List<String> a, List<String> b) {
        return new HashSet<>(a).equals(new HashSet<>(b));
    }

    // 支持三个集合比较
    private static boolean equalSets(List<String> a, List<String> b, List<String> c) {
        return equalSets(a, b) && equalSets(b, c);
    }

    // 支持四个集合比较
    private static boolean equalSets(List<String> a, List<String> b, List<String> c, List<String> d) {
        return equalSets(a, b) && equalSets(b, c) && equalSets(c, d);
    }

    // 格式化多个名字为 "A, B and C" 样式
    private static String formatList(Collection<String> names) {
        if (names == null || names.isEmpty()) return "";
        List<String> list = new ArrayList<>(new LinkedHashSet<>(names)); // 去重保序

        return "@" + String.join(" @", names);
    }

    private void sendOfferAcceptedEmail(TalentRecruitmentProcess process) {
        JobDTOV3 job = jobService.getJob(process.getJobId());
        CompanyDTO company = companyService.getCompany(job.getCompanyId());
        TalentDTOV3 talentDTOV3 = talentClient.getTalentWithoutEntity((process.getTalentId())).getBody();
        ApplicationUpdateStatusOfferAcceptedEmailDTO emailDTO = new ApplicationUpdateStatusOfferAcceptedEmailDTO();
        emailDTO.setJobTitle(job.getTitle());
        emailDTO.setCompanyName(company.getName());
        UserBriefDTO userVO = userService.getUserById(SecurityUtils.getUserId());
        emailDTO.setUserName(CommonUtils.translateName(userVO.getFirstName(), userVO.getLastName()));
        emailDTO.setCandidateName(CommonUtils.translateName(talentDTOV3.getFirstName(), talentDTOV3.getLastName()));

        List<TalentRecruitmentProcessKpiUserVO> kpiUserVOList = kpiUserService.findAllByTalentIdAndTalentRecruitmentProcessIdNotIn(process.getTalentId(), process.getId());
        List<Long> recruiterIds = kpiUserVOList.stream().filter(vo -> UserRole.RECRUITER.equals(vo.getUserRole())).map(TalentRecruitmentProcessKpiUserVO::getUserId).collect(Collectors.toList());
        List<String> recruiterEmails = userService.findBriefUsers(recruiterIds).stream().map(UserBriefDTO::getEmail).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(recruiterEmails)) {
            String template = CommonUtils.readFileToString("templates/apn-application-update-status-offer-accepted-en.html"); //TODO
            Map<String, Object> valueMap = new HashMap<>(16);
            valueMap.put("USER_NAME", emailDTO.getUserName());
            valueMap.put("CANDIDATE_NAME", emailDTO.getCandidateName());
            valueMap.put("COMPANY_NAME", emailDTO.getCompanyName());
            valueMap.put("JOB_TITLE", emailDTO.getJobTitle());
            template = Html2ImageUtils.convertHtmlTemplate(template, valueMap);

            MailVM mailvm = new MailVM(applicationProperties.getSupportSender(), recruiterEmails, null, null, "Candidate accepted offer", template, null, true);
            try {
                mailService.sendHtmlMail(mailvm);
                log.info("[TalentRecruitmentProcessServiceImpl: sendOfferAcceptedEmail] send candidate accepted offer email Success");
            } catch (Exception e) {
                log.error("[TalentRecruitmentProcessServiceImpl: sendOfferAcceptedEmail] error message: {}", e.getMessage(), e);
                if (e instanceof CustomParameterizedException) {
                    throw (CustomParameterizedException) e;
                }
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERACCEPTSENDMAILFAILED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
        }
    }

    @Override
    public TalentRecruitmentProcessVO commission(TalentRecruitmentProcessCommissionVO commissionVO, boolean noteOnly) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(commissionVO.getTalentRecruitmentProcessId());
        validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());

        //get current node status
        Boolean sendEmailFlag = false;
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(commissionVO.getTalentRecruitmentProcessId());
        //Compatible historical data, and notifying users
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (NodeType.COMMISSION.toDbValue() > currentNode.getNodeType().toDbValue()) {
            // new to this node, create one and send status change email
            sendEmailFlag = true;
        } else if (noteOnly || NodeType.COMMISSION.toDbValue() < currentNode.getNodeType().toDbValue() || NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
            // this node is a complete node or the application has been terminated here, only note is allowed to update
            talentRecruitmentProcessCommissionService.updateNoteOnly(commissionVO);
            if (Objects.equals(NodeType.COMMISSION.toDbValue(), currentNode.getNodeType().toDbValue()) && !NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                updateTalentRecruitmentProcessNoteOnly(commissionVO.getTalentRecruitmentProcessId(), commissionVO.getNote());
            }
            return toVO(commissionVO.getTalentRecruitmentProcessId());
        }

        talentRecruitmentProcessCommissionService.save(commissionVO);
        talentRecruitmentProcessNodeService.completeCurrentNodeAndActiveNextNode(talentRecruitmentProcess, NodeType.COMMISSION);
        updateTalentRecruitmentProcess(talentRecruitmentProcess, commissionVO.getNote());
        //send status change email
        if (sendEmailFlag) {
            sendStatusChangeEmail(talentRecruitmentProcess, currentNode.getNodeType(), NodeType.COMMISSION);
        }
        TalentRecruitmentProcessVO result = toVO(commissionVO.getTalentRecruitmentProcessId());
        xxlJobService.unOnboardedReminder(result);
        return result;
    }

    @Override
    @Transactional //for M1 dev
    public TalentRecruitmentProcessVO onboard(TalentRecruitmentProcessOnboardVO onboardVO, boolean noteOnly) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(onboardVO.getTalentRecruitmentProcessId());

        //special validation process, we DO NOT check if the recruitment process is still VALID here
//        validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());
        if (talentRecruitmentProcess.getRecruitmentProcessId() == null) {
            throw new CustomParameterizedException("The input recruitmentProcessId is required.");
        }
        Optional<RecruitmentProcess> recruitmentProcess = recruitmentProcessRepository.findById(talentRecruitmentProcess.getRecruitmentProcessId()); //TODO: add cache
        if (recruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException("Recruitment Process not found by id: " + talentRecruitmentProcess.getRecruitmentProcessId());
        }
        if (!recruitmentProcess.get().getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("No permission for this recruitment process.");
        }

        //get current node status
        Boolean sendEmailFlag = false;
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(onboardVO.getTalentRecruitmentProcessId());
        //Compatible historical data, and notifying users
        if (ObjectUtil.isEmpty(currentNode)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_TOVONODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (NodeType.ON_BOARD.toDbValue() > currentNode.getNodeType().toDbValue()) {
            // new to this node, create one and send status change email
            // if recruitment process is NOT VALID, we cannot continue the application
            if (!recruitmentProcess.get().getStatus().equals(ActiveStatus.ACTIVE)) {
                throw new CustomParameterizedException("Recruitment Process is INACTIVE, id: " + recruitmentProcess.get().getId());
            }
            sendEmailFlag = true;
        } else if (noteOnly || NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
            talentRecruitmentProcessOnboardService.updateNoteOnly(onboardVO);
            if (!NodeStatus.ELIMINATED.equals(currentNode.getNodeStatus())) {
                updateTalentRecruitmentProcessNoteOnly(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getNote());
            }
            return toVO(onboardVO.getTalentRecruitmentProcessId());
        }


        talentRecruitmentProcessOnboardService.save(onboardVO);
        StartDTO startDTO = talentRecruitmentProcessOnboardService.saveStart(talentRecruitmentProcess, onboardVO);
        log.info("startDTO={}", startDTO);
        // 更新候选人工作经历
        this.updateTalentExperience(onboardVO, talentRecruitmentProcess, startDTO);

        talentRecruitmentProcessNodeService.completeCurrentNodeAndActiveNextNode(talentRecruitmentProcess, NodeType.ON_BOARD);
        updateTalentRecruitmentProcess(talentRecruitmentProcess, onboardVO.getNote());
        //send status change email
        if (sendEmailFlag) {
            sendStatusChangeEmail(talentRecruitmentProcess, currentNode.getNodeType(), NodeType.ON_BOARD);
        }
        TalentRecruitmentProcessVO talentRecruitmentProcessVO = toVO(onboardVO.getTalentRecruitmentProcessId());
        xxlJobService.onboardNoInvoiceReminder(talentRecruitmentProcessVO);
        xxlJobService.deleteApplicationReminderByTalentId(talentRecruitmentProcessVO);
        if (sendEmailFlag) {
            talentRecruitmentProcessVO.setFirstTimeToThisNode(true);
        }

        //新增以下发送mq代码
        if (null != startDTO) {
            //组装mq数据
            com.alibaba.fastjson.JSONObject mqParam = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(startDTO);
            String token = SecurityUtils.getCurrentUserToken();
            mqParam.put(AUTHORIZATION_HEADER, String.format("%s %s", TOKEN_TYPE, token));


            CommonMqTransactionRecord record = new CommonMqTransactionRecord();
            record.setBusType(MqTranRecordBusTypeEnums.TALENT_ONBOARD.toDbValue());
            record.setBusId(BigInteger.valueOf(startDTO.getTalentId()));
            record.setSendStatus(MqTranRecordStatusEnums.PENDING.toDbValue());
            record.setSendContent(mqParam.toJSONString());
            record.setSendCount(1);
            commonMqTransactionRecordRepository.save(record);
            log.info("[APN: TalentRecruitmentProcessService @{}] Talent onboard create mq trans record success -> result: {}", SecurityUtils.getUserId(), record);
            //当发送失败时候用于更新记录信息
            mqParam.put("mqRecordId", record.getId());
            mqParam.put("mqRecordType", MqTranRecordBusTypeEnums.TALENT_ONBOARD.toDbValue());
            mqParam.put(SecurityUtils.OPERATOR_UID, SecurityUtils.getUserUid());
            executor.execute(() -> {
                rabbitTemplate.convertAndSend(talentOnboardMQProperties.getTalentOnboardTxExchange(), talentOnboardMQProperties.getTalentOnboardTxRoutingKey(), mqParam, new CorrelationData(String.valueOf(record.getId())));
                log.info("[APN: TalentRecruitmentProcessService @{}] send to talent tx rabbit -> param: {}", SecurityUtils.getUserId(), JSON.toJSONString(startDTO));
            });
        }
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(onboardVO.getTalentRecruitmentProcessId(), CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS, List.of(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS,
                    CalendarTypeEnum.OFFER_PASS_NOT_UPDATE_STATUS));
        });
        return talentRecruitmentProcessVO;
    }

    private void updateTalentExperience(TalentRecruitmentProcessOnboardVO onboardVO, TalentRecruitmentProcess talentRecruitmentProcess, StartDTO startDTO){
        TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO()
                .setTalentRecruitmentProcessId(onboardVO.getTalentRecruitmentProcessId())
                .setLocation(Objects.nonNull(onboardVO.getWorkLocation()) ? onboardVO.getWorkLocation().getLocation() : null)
                .setStartDate(onboardVO.getOnboardDate())
                .setCurrent(Boolean.TRUE);
        if (Objects.nonNull(startDTO)){
            talentExperienceDTO
                    .setCompanyId(startDTO.getCompanyId())
                    .setCompanyName(startDTO.getCompany())
                    .setTitle(startDTO.getJobTitle())
                    .setLocation(Objects.nonNull(startDTO.getStartAddress()) ? startDTO.getStartAddress().getOriginDisplay() : null);
        }else {
            JobDTOV3 job = jobService.getJob(talentRecruitmentProcess.getJobId());
            talentExperienceDTO
                    .setCompanyId(job.getCompanyId())
                    .setCompanyName(companyService.getCompany(job.getCompanyId()).getName())
                    .setTitle(job.getTitle());
        }
        talentService.updateTalentExperience(talentRecruitmentProcess.getTalentId(), talentExperienceDTO);
    }

    @Override
    public void updateTalentExperience(StartDTO startDTO) {
        if (Objects.isNull(startDTO)){
            return;
        }
        TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO()
                .setTalentRecruitmentProcessId(startDTO.getTalentRecruitmentProcessId())
                .setStartDate(startDTO.getStartDate())
                .setCompanyId(startDTO.getCompanyId())
                .setCompanyName(startDTO.getCompany())
                .setTitle(startDTO.getJobTitle())
                .setLocation(Objects.nonNull(startDTO.getStartAddress()) ? startDTO.getStartAddress().getOriginDisplay() : null)
                .setCurrent(Boolean.TRUE);
        talentService.updateTalentExperience(startDTO.getTalentId(), talentExperienceDTO);
    }

    private void deleteTalentExperience(Long talentId, Long talentRecruitmentProcessId){
        talentService.deleteTalentExperience(talentId, talentRecruitmentProcessId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentRecruitmentProcessVO eliminate(EliminateDTO eliminate) {
        //1.validateTalentRecruitmentProcess方法，就一条检查sql根据主键id查询，优化空间:暂无
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(eliminate.getTalentRecruitmentProcessId());
        //2.validateRecruitmentProcess方法，就一条检查sql根据主键id查询，优化空间:暂无
        validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());
        //3.findCurrentNode方法，优化方案：2条sql查询优化为1条，新方法：findCurrentNodeV2，是否优化：是
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNodeV2(eliminate.getTalentRecruitmentProcessId());
        //4.validateRejectReason方法，优化空间:暂无
        validateRejectReason(eliminate.getEliminateReason(), currentNode.getNodeType());
        //5.save方法，比较简单逻辑，优化空间:暂无
        talentRecruitmentProcessEliminateService.save(eliminate);
        //6.eliminate方法，优化方案：2条sql,第一条sql查询后会做逻辑校验，但暂不优化，后期建议校验逻辑放在统一的校验方法中，避免重复查询对象 是否优化：否
        talentRecruitmentProcessNodeService.eliminate(talentRecruitmentProcess);
        //7.updateStartStatusWithEliminatedCandidate方法，优化方案：结合业务逻辑可以决定是否改为异步调用
        financeClient.updateStartStatusWithEliminatedCandidate(eliminate.getTalentRecruitmentProcessId());
        //8.updateTalentRecruitmentProcess方法，仅更新操作，优化空间：暂无
        updateTalentRecruitmentProcess(talentRecruitmentProcess, eliminate.getNote());
        //9.sendEliminateEmail方法，优化方案：整体改为异步
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            sendEliminateEmail(talentRecruitmentProcess, currentNode.getNodeType(), eliminate.getNote());
        });
        //10.toVO方法，优化方案：查询对象信息和校验逻辑统一前置，避免重复连接数据库查询，是否优化：暂不
        TalentRecruitmentProcessVO result = toVO(eliminate.getTalentRecruitmentProcessId());
        //11.eliminateXxlJobForApplication方法，for循环中每个处理逻辑都是异步，优化空间：暂无，是否优化：暂不
        xxlJobService.eliminateXxlJobForApplication(result);
        //12.deleteApplicationReminder方法，异步，优化空间：暂无，是否优化：暂不
        xxlJobService.deleteApplicationReminder(result);
        //13.deleteTalentExperience方法，优化方案：可以改为异步，有数据库操作，如果后续查询同一数据会有问题，暂不优化
        this.deleteTalentExperience(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getId());
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(eliminate.getTalentRecruitmentProcessId(), CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS, List.of(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS,
                    CalendarTypeEnum.OFFER_PASS_NOT_UPDATE_STATUS));
            List<Long> invoiceIdByTalentProgressId = talentRecruitmentProcessRepository.getInvoiceIdByTalentProgressId(eliminate.getTalentRecruitmentProcessId());
            if(!invoiceIdByTalentProgressId.isEmpty()) {
                for(Long invoiceId : invoiceIdByTalentProgressId) {
                    notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(invoiceId, InvoiceTypeEnum.INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
                }
            }
            List<Long> invoicingApplicationInfoIdByTalentProgressId = talentRecruitmentProcessRepository.getInvoicingApplicationInfoIdByTalentProgressId(eliminate.getTalentRecruitmentProcessId());
            if(!invoicingApplicationInfoIdByTalentProgressId.isEmpty()) {
                for(Long invoicingApplicationInfoId : invoicingApplicationInfoIdByTalentProgressId) {
                    notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(invoicingApplicationInfoId, InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
                }
            }
            List<Long> tGroupInvoiceIdByTalentProgressId = talentRecruitmentProcessRepository.getTGroupInvoiceIdByTalentProgressId(eliminate.getTalentRecruitmentProcessId());
            if(!tGroupInvoiceIdByTalentProgressId.isEmpty()) {
                for(Long tGroupInvoiceId : tGroupInvoiceIdByTalentProgressId) {
                    notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(tGroupInvoiceId, InvoiceTypeEnum.T_GROUP_INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
                }
            }


        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentRecruitmentProcessVO eliminateForAuto(EliminateDTO eliminate) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcessForAuto(eliminate.getTalentRecruitmentProcessId());
        validateRecruitmentProcessForAuto(talentRecruitmentProcess.getRecruitmentProcessId());

        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(eliminate.getTalentRecruitmentProcessId());
        talentRecruitmentProcessEliminateService.save(eliminate);
        talentRecruitmentProcessNodeService.eliminate(talentRecruitmentProcess);
        startRepository.updateStatusByTalentRecruitmentProcessId(StartStatus.ACTIVE, StartStatus.ELIMINATED, eliminate.getTalentRecruitmentProcessId());
        updateTalentRecruitmentProcess(talentRecruitmentProcess, eliminate.getNote());
        sendEliminateEmailForAuto(talentRecruitmentProcess, currentNode.getNodeType(), eliminate.getNote());
        TalentRecruitmentProcessVO result = toVOForAuto(eliminate.getTalentRecruitmentProcessId());
        xxlJobService.eliminateXxlJobForApplication(result);
        xxlJobService.deleteApplicationReminder(result);
        this.deleteTalentExperience(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getId());
        return result;
    }

    private void validateRejectReason(EliminateReason eliminateReason, NodeType nodeType) {
        if (!nodeType.equals(NodeType.SUBMIT_TO_JOB) && eliminateReason.equals(EliminateReason.ACCIDENTAL_OPERATION)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
    }

    /**
     * 接受offer后但未入职，职位关闭
     *
     * @param eliminate
     * @return
     * @throws URISyntaxException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentRecruitmentProcessVO onboardEliminate(EliminateDTO eliminate) {

        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(eliminate.getTalentRecruitmentProcessId());
        //validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());

        StartDTO startDTO = financeClient.getStartByTalentIdAndTalentRecruitmentProcessId(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getId(), com.altomni.apn.job.domain.enumeration.start.StartStatus.ACTIVE).getBody();
        if (startDTO == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_ONBOARDELIMINATE_NOSTARTINFO.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }

        if (LocalDate.now().isAfter(startDTO.getStartDate())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_ONBOARDELIMINATE_TIMEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }

        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(eliminate.getTalentRecruitmentProcessId());
        //判断流程节点是不是onboard并且是激活，然后修改为淘汰状态
        talentRecruitmentProcessEliminateService.save(eliminate);
        talentRecruitmentProcessNodeService.eliminate(talentRecruitmentProcess);

        //修改start状态为淘汰
        financeClient.updateStartStatusWithEliminatedCandidate(eliminate.getTalentRecruitmentProcessId());

        if (!startDTO.getStartType().equals(StartType.FTE_NEW_HIRE)) {
            //修改assignment状态为淘汰
            jobdivaClient.deleteByStartId(startDTO.getId());
        }

        //修改流程备注
        updateTalentRecruitmentProcess(talentRecruitmentProcess, eliminate.getNote());

        //给am SOURCER等用户发送邮件通知
        sendEliminateEmail(talentRecruitmentProcess, currentNode.getNodeType(), eliminate.getNote());

        //流程淘汰，需要删除提醒
        TalentRecruitmentProcessVO result = toVO(eliminate.getTalentRecruitmentProcessId());
        xxlJobService.eliminateXxlJobForApplication(result);
        this.deleteTalentExperience(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getId());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentRecruitmentProcessVO onboardCancelEliminate(Long talentRecruitmentProcessId) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(talentRecruitmentProcessId);

        StartDTO startDTO = financeClient.getStartByTalentIdAndTalentRecruitmentProcessId(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getId(), com.altomni.apn.job.domain.enumeration.start.StartStatus.ELIMINATED).getBody();
        if (startDTO == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_ONBOARDELIMINATE_NOSTARTINFO.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }

        talentRecruitmentProcessEliminateService.delete(talentRecruitmentProcessId);
        TalentRecruitmentProcessNode talentRecruitmentProcessNode = talentRecruitmentProcessNodeService.cancelEliminate(talentRecruitmentProcess);
        //修改start状态为激活
        financeClient.updateStartStatusWithCancelEliminatedCandidate(talentRecruitmentProcessId);
        String note = this.getLastNodeNote(talentRecruitmentProcessNode);
        updateTalentRecruitmentProcess(talentRecruitmentProcess, note);
        TalentRecruitmentProcessVO result = toVO(talentRecruitmentProcessId);
        xxlJobService.cancelEliminateXxlJobForApplication(result);
        this.updateTalentExperience(startDTO);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentRecruitmentProcessVO cancelEliminate(Long talentRecruitmentProcessId) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(talentRecruitmentProcessId);
        validateRecruitmentProcess(talentRecruitmentProcess.getRecruitmentProcessId());

        this.checkOtherRecruitmentProcessForTalent(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcessId);

        StartDTO startDTO = financeClient.getStartByTalentIdAndTalentRecruitmentProcessId(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getId(), com.altomni.apn.job.domain.enumeration.start.StartStatus.ELIMINATED).getBody();

        talentRecruitmentProcessEliminateService.delete(talentRecruitmentProcessId);
        TalentRecruitmentProcessNode talentRecruitmentProcessNode = talentRecruitmentProcessNodeService.cancelEliminate(talentRecruitmentProcess);
        financeClient.updateStartStatusWithCancelEliminatedCandidate(talentRecruitmentProcessId);
        String note = this.getLastNodeNote(talentRecruitmentProcessNode);
        updateTalentRecruitmentProcess(talentRecruitmentProcess, note);
        TalentRecruitmentProcessVO result = toVO(talentRecruitmentProcessId);
        xxlJobService.cancelEliminateXxlJobForApplication(result);
        this.updateTalentExperience(startDTO);
        return result;
    }

    private void validateExistsStart(Long talentId) {
        StartDTO startDTO = financeClient.findStartByTalentIdAndStatus(talentId, StartStatus.ACTIVE).getBody();
        if (startDTO != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_VALIDATEEXISTSSTART.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startDTO.getJobId(), startDTO.getJobTitle()), applicationApiPromptProperties.getAppl()));
        }
    }

    private void checkOtherRecruitmentProcessForTalent(Long talentId, Long currentRecruitmentProcessId) {
        TalentRecruitmentProcessNode eliminatedNode = talentRecruitmentProcessNodeService.findCurrentNode(currentRecruitmentProcessId);
        if (!eliminatedNode.getNodeType().equals(NodeType.ON_BOARD)) {
            return;
        }
        List<TalentRecruitmentProcess> recruitmentProcesses = talentRecruitmentProcessRepository.findAllByTalentId(talentId);
        List<Long> ids = recruitmentProcesses.stream().map(TalentRecruitmentProcess::getId).filter(id -> !id.equals(currentRecruitmentProcessId)).collect(Collectors.toList());
        Optional<TalentRecruitmentProcessNode> onboardNode = talentRecruitmentProcessNodeService.findOneOnboardNodeByTalentRecruitmentProcessIds(ids);
        if (onboardNode.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_CHECKOTHERRECRUITMENTPROCESSFORTALENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
    }


    private String getLastNodeNote(TalentRecruitmentProcessNode talentRecruitmentProcessNode) {
        Long talentRecruitmentProcessId = talentRecruitmentProcessNode.getTalentRecruitmentProcessId();
        switch (talentRecruitmentProcessNode.getNodeType()) {
            case SUBMIT_TO_JOB:
                return talentRecruitmentProcessRepository.getNoteFromSubmitToJob(talentRecruitmentProcessId);
            case SUBMIT_TO_CLIENT:
                return talentRecruitmentProcessRepository.getNoteFromSubmitToClient(talentRecruitmentProcessId);
            case INTERVIEW:
                return talentRecruitmentProcessRepository.getNoteFromInterview(talentRecruitmentProcessId);
            case OFFER:
                return talentRecruitmentProcessRepository.getNoteFromOffer(talentRecruitmentProcessId);
            case OFFER_ACCEPT:
                return talentRecruitmentProcessRepository.getNoteFromOfferAccepted(talentRecruitmentProcessId);
            case ON_BOARD:
                return talentRecruitmentProcessRepository.getNoteFromOnBoard(talentRecruitmentProcessId);
        }
        return null;
    }

    @Override
    public AllNotesVO updateNote(UpdateNoteDTO noteDTO) {
        switch (noteDTO.getNodeType()) {
            case SUBMIT_TO_JOB:
                talentRecruitmentProcessSubmitToJobService.updateRecommendCommentsOnly(noteDTO.getTalentRecruitmentProcessId(), noteDTO.getNote());
                break;
            case SUBMIT_TO_CLIENT:
                talentRecruitmentProcessSubmitToClientService.updateNoteOnly(noteDTO.getTalentRecruitmentProcessId(), noteDTO.getNote());
                talentRecruitmentProcessRepository.updateLastModifiedDate(noteDTO.getTalentRecruitmentProcessId());
                SecurityContext context = SecurityContextHolder.getContext();
                CompletableFuture.runAsync(() -> {
                    SecurityContextHolder.setContext(context);
                    notifyCompleteSystemCalendar(noteDTO.getTalentRecruitmentProcessId(), CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS, List.of(CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS));
                });
                break;
            case INTERVIEW:
                talentRecruitmentProcessInterviewService.updateNoteOnly(noteDTO.getId(), noteDTO.getNote());
                break;
            case OFFER:
                talentRecruitmentProcessOfferService.updateNoteOnly(noteDTO.getTalentRecruitmentProcessId(), noteDTO.getNote());
                break;
            case OFFER_ACCEPT:
                ipgOfferAcceptService.updateNoteOnly(noteDTO.getTalentRecruitmentProcessId(), noteDTO.getNote());
                break;
            case COMMISSION:
                talentRecruitmentProcessCommissionService.updateNoteOnly(noteDTO.getTalentRecruitmentProcessId(), noteDTO.getNote());
                break;
            case ON_BOARD:
                talentRecruitmentProcessOnboardService.updateNoteOnly(noteDTO.getTalentRecruitmentProcessId(), noteDTO.getNote());
                break;
        }
        return this.getAllNotes(noteDTO.getTalentRecruitmentProcessId());
    }

    /**
     * Get one talentRecruitmentProcess by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<TalentRecruitmentProcessVO> findOne(Long id) {
        return Optional.of(toVO(id));
    }

    @Override
    public Optional<TalentRecruitmentProcessVO> findOneBrief(Long id) {
        return talentRecruitmentProcessRepository.findById(id).map(this::toVOBrief);
    }

    @Override
    public List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByTalentId(Long talentId) {
        List<TalentRecruitmentProcess> talentRecruitmentProcesses = talentRecruitmentProcessRepository.findAllByTalentId(talentId);
        return CollectionUtils.isNotEmpty(talentRecruitmentProcesses) ? talentRecruitmentProcesses.stream().map(this::toVO).collect(Collectors.toList()) : Lists.newArrayList();
    }

    @Override
    public List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessBriefByTalentId(Long talentId) {
        List<TalentRecruitmentProcess> talentRecruitmentProcesses = talentRecruitmentProcessRepository.findAllByTalentId(talentId);
        if (CollectionUtils.isNotEmpty(talentRecruitmentProcesses)){
            return Lists.newArrayList();
        }
        Set<Long> resignedIds = resignationRepository.filterResignedTalentRecruitmentProcessIds(talentRecruitmentProcesses.stream().map(TalentRecruitmentProcess::getId).toList());
        return talentRecruitmentProcesses.stream().map(t -> t.setEmployed(!resignedIds.contains(t.getRecruitmentProcessId()))).map(this::toVOBrief).collect(Collectors.toList());
    }

    @Override
    public List<TalentRecruitmentProcessVO> getAllTalentRecruitmentProcessBriefByTalentId(Long talentId) {
        List<TalentRecruitmentProcess> talentRecruitmentProcesses = talentRecruitmentProcessRepository.findAllByTalentId(talentId);
        return talentRecruitmentProcesses.stream().map(this::toVOBrief).toList();
    }


    @Override
    public Integer countTalentRecruitmentProcessByTalentId(Long talentId) {
        return talentRecruitmentProcessRepository.countDistinctByTalentId(talentId);
    }

    @Override
    public List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessAllByTalentId(Long talentId) {
        List<TalentRecruitmentProcess> talentRecruitmentProcesses = talentRecruitmentProcessRepository.findAllByTalentId(talentId);
        return CollectionUtils.isNotEmpty(talentRecruitmentProcesses) ? talentRecruitmentProcesses.stream().map(this::toVONoPermission).collect(Collectors.toList()) : Lists.newArrayList();
    }

    @Override
    public List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByJobId(Long jobId) {
        List<TalentRecruitmentProcess> talentRecruitmentProcesses = talentRecruitmentProcessRepository.findAllByJobId(jobId);
        return CollectionUtils.isNotEmpty(talentRecruitmentProcesses) ? talentRecruitmentProcesses.stream().map(this::toVO).collect(Collectors.toList()) : Lists.newArrayList();
    }

    @Override
    public List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessBriefByJobId(Long jobId) {
        List<TalentRecruitmentProcess> talentRecruitmentProcesses = talentRecruitmentProcessRepository.findAllByJobId(jobId);
        return CollectionUtils.isNotEmpty(talentRecruitmentProcesses) ? talentRecruitmentProcesses.stream().map(this::toVOBrief).collect(Collectors.toList()) : Lists.newArrayList();
    }

    @Override
    public TalentRecruitmentProcessVO getTalentRecruitmentProcessLastByJobId(Long jobId) {
        TalentRecruitmentProcess talentRecruitmentProcess = talentRecruitmentProcessRepository.findLatestByJobId(jobId);
        TalentRecruitmentProcessVO vo = new TalentRecruitmentProcessVO();
        BeanUtil.copyProperties(talentRecruitmentProcess, vo);
        return vo;
    }

    @Override
    public Integer countApplicationByTalentId(Long talentId, Integer nodeType) {
        return talentRecruitmentProcessRepository.countApplicationByTalentId(talentId, nodeType, JobType.FULL_TIME.getDbValue(), Arrays.asList(NodeStatus.ACTIVE.toDbValue(), NodeStatus.ELIMINATED.toDbValue()));
    }

    @Override
    public TalentRecruitmentProcessVO getTalentRecruitmentProcessByTalentIdAndJobId(Long talentId, Long jobId) {
        return toVO(talentRecruitmentProcessRepository.findByTalentIdAndJobId(talentId, jobId));
    }

    @Override
    public void downloadDetailsOfChargesPdf(HttpServletResponse response, Long id, String language) {
        log.info("[TalentRecruitmentProcessServiceImpl: downloadDetailsOfChargesPdf @{}] Request to download details of charge for application id: {} in language: {}", SecurityUtils.getUserId(), id, language);

        TalentRecruitmentProcessVO talentRecruitmentProcessVO = this.findOne(id).orElseThrow(() -> new CustomParameterizedException("Cannot find talent recruitment process!"));
        HistoryStagesVo stages = this.getStages(id);

        NodeType currentNodeType = talentRecruitmentProcessVO.getLastNodeType();
        boolean isGeneralRecruitmentProcess = true;
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(talentRecruitmentProcessVO.getTenantId())) { //ipg recruitment process
            if (NodeType.OFFER_ACCEPT.toDbValue() > currentNodeType.toDbValue()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_DOWNLOADPDFNOTACCEPTOFFER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
            isGeneralRecruitmentProcess = false;
        } else { //general recruitment process
            if (NodeType.OFFER.toDbValue() > currentNodeType.toDbValue()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_DOWNLOADPDFNOTGOTTENOFFER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
        }

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ByteArrayOutputStream waterMarkedBos = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4, 0, 0, 60, 0);
        try {
            PdfUtil pdfUtil = new PdfUtil();
            PdfWriter.getInstance(document, bos);
            document.open();

            if (isGeneralRecruitmentProcess) { // general recruitment process
                if ("cn".equalsIgnoreCase(language)) {
                    pdfUtil.createGeneralDetailsOfChargePdfInCN(document, talentRecruitmentProcessVO, stages);
                } else {
                    pdfUtil.createGeneralDetailsOfChargePdfInEN(document, talentRecruitmentProcessVO, stages);
                }
            } else {
                if ("cn".equalsIgnoreCase(language)) { // ipg recruitment process
                    pdfUtil.createDetailsOfChargePdfInCN(document, talentRecruitmentProcessVO, stages);
                } else {
                    pdfUtil.createDetailsOfChargePdfInEN(document, talentRecruitmentProcessVO, stages);
                }
            }

            document.close();
            HeaderUtil.setPDFHeader(response, "Details of Charges for application_" + id);
            OutputStream outputStream = response.getOutputStream();
            IOUtils.write(bos.toByteArray(), outputStream);
        } catch (DocumentException | IOException e) {
            log.error("[TalentRecruitmentProcessServiceImpl: downloadDetailsOfChargesPdf @{}] Exception when output details of charges to PDF file with error {}", e.getLocalizedMessage(), e);
        } finally {
            document.close();
            try {
                bos.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
    }

    @Override
    public Integer countUnfinishedApplicationsForJob(Long jobId) {
        return talentRecruitmentProcessRepository.countUnfinishedApplicationsForJob(jobId);
    }

    @Override
    public Map<Long, Long> countUnfinishedApplicationsForJobs(List<Long> jobIds) {
        return talentRecruitmentProcessRepository.countUnfinishedApplicationsForJobs(jobIds)
                .stream()
                .collect(Collectors
                        .toMap(InProcessApplicationCountByJobVM::getJobId, InProcessApplicationCountByJobVM::getInProcessApplication));
    }

    @Override
    public Integer countUnfinishedApplicationsByApplicationIds(List<Long> applicationIds) {
        return talentRecruitmentProcessRepository.countUnfinishedApplicationsByIds(applicationIds);
    }

    @Override
    public List<InProcessApplicationBriefVM> getUnfinishedApplicationsByApplicationIds(List<Long> applicationIds) {
        return talentRecruitmentProcessRepository.getUnfinishedApplicationsByIds(applicationIds);
    }

    @Override
    public List<ApplicationBriefInfoVM> getApplicationsBriefInoByApplicationIds(List<Long> applicationIds) {
        return talentRecruitmentProcessRepository.getApplicationsBriefInoByIds(applicationIds);
    }

    @Override
    public AllNotesVO getAllNotes(Long talentRecruitmentProcessId) {
        TalentRecruitmentProcess talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId).orElseThrow(() -> new CustomParameterizedException("Talent recruitment process not found by id: " + talentRecruitmentProcessId));
        List<Object[]> objectList = talentRecruitmentProcessRepository.getAllNotes(talentRecruitmentProcessId);

        AllNotesVO res = new AllNotesVO(talentRecruitmentProcessId, talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getJobId());

        if (CollectionUtils.isEmpty(objectList)) {
            return res;
        }
        List<NodeAndNote> notes = new ArrayList<>();
        objectList.forEach(o -> {
            NodeAndNote note = new NodeAndNote();
            note.setId(Long.valueOf(StringUtil.valueOf(o[0])));
            note.setNodeType(NodeType.fromDbValue(((BigInteger) o[1]).intValue()));
            note.setNote(StringUtil.valueOf(o[2]));
            note.setLastModifiedBy(StringUtil.valueOf(o[3]));
            note.setLastModifiedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(o[4])));
            note.setNoteLastModifiedByUserId(Long.valueOf(StringUtil.valueOf(o[5])));
            note.setCreatedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(o[6])));
            note.setCreatedBy(StringUtil.valueOf(o[7]));
            notes.add(note);
        });
        res.setNotes(notes);

        return res;
    }

    @Override
    public HistoryStagesVo getStages(Long id) {
        var talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Talent recruitment process not found by id: " + id));
        var historyStagesVo = new HistoryStagesVo(talentRecruitmentProcess);

        var submitToJobFuture = CompletableFuture.supplyAsync(() -> talentRecruitmentProcessSubmitToJobService.findOneByTalentRecruitmentProcessId(id), executor);
        var submitToClientFuture = CompletableFuture.supplyAsync(getTalentRecruitmentProcessSubmitToClientVOSupplier(id, talentRecruitmentProcess), executor);
        var interviewsFuture = CompletableFuture.supplyAsync(() -> talentRecruitmentProcessInterviewService.findAllByTalentRecruitmentProcessId(id), executor);
        var offerFuture = CompletableFuture.supplyAsync(() -> talentRecruitmentProcessOfferService.findByTalentRecruitmentProcessId(id), executor);
        var offerAcceptFuture = CompletableFuture.supplyAsync(() -> ipgOfferAcceptService.findByTalentRecruitmentProcessId(id), executor);
        var commissionFuture = CompletableFuture.supplyAsync(() -> talentRecruitmentProcessCommissionService.findByTalentRecruitmentProcessId(id), executor);
        var onboardFuture = CompletableFuture.supplyAsync(() -> talentRecruitmentProcessOnboardService.findByTalentRecruitmentProcessId(id), executor);
        var eliminateFuture = CompletableFuture.supplyAsync(() -> talentRecruitmentProcessEliminateService.findByTalentRecruitmentProcessId(id), executor);
        var salaryPackageFuture = CompletableFuture.supplyAsync(() -> offerSalaryPackageService.findAllByTalentRecruitmentProcessId(id), executor);
        var feeChargeFuture = CompletableFuture.supplyAsync(() -> feeChargeService.findByTalentRecruitmentProcessId(id), executor);
        var contractFeeChargeFuture = CompletableFuture.supplyAsync(() -> contractFeeChargeService.findByTalentRecruitmentProcessId(id), executor);
        var clientInfoFuture = CompletableFuture.supplyAsync(() -> clientInfoService.findByTalentRecruitmentProcessId(id), executor);

        CompletableFuture.allOf(submitToJobFuture, submitToClientFuture, interviewsFuture, offerFuture, offerAcceptFuture,
                commissionFuture, onboardFuture, eliminateFuture, salaryPackageFuture, feeChargeFuture, contractFeeChargeFuture, clientInfoFuture)
                .exceptionally(FutureExceptionUtil::handleFutureException);

        var voList = Stream.concat(interviewsFuture.join().stream(), Stream.of(submitToJobFuture.join(),
                submitToClientFuture.join(), offerFuture.join(), offerAcceptFuture.join(), commissionFuture.join(), onboardFuture.join(), eliminateFuture.join()))
                .filter(Objects::nonNull).toList();
        var userIds = voList.stream().flatMap(vo -> Stream.of(vo.getLastModifiedBy(), vo.getCreatedBy()))
                .map(SecurityUtils::getUserIdFromCreatedBy).collect(Collectors.toSet());

        var userMap = userService.findBriefUsers(userIds).stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));

        voList.forEach(vo -> {
            Long createUserId = SecurityUtils.getUserIdFromCreatedBy(vo.getCreatedBy());
            Long modifyUserId = SecurityUtils.getUserIdFromCreatedBy(vo.getLastModifiedBy());
            vo.setCreatedUser(userMap.get(createUserId));
            vo.setLastModifiedUser(userMap.get(modifyUserId));
            if (vo instanceof FillFeeCharge fillFeeCharge) {
                fillFeeCharge.setFeeCharge(feeChargeFuture.join());
            }
            if (vo instanceof FillContractFeeCharge fillContractFeeCharge) {
                fillContractFeeCharge.setContractFeeCharge(contractFeeChargeFuture.join());
            }
            if (vo instanceof FillClientInfo fillClientInfo) {
                fillClientInfo.setClientInfo(clientInfoFuture.join());
            }
            if (vo instanceof FillSalaryPackages fillSalaryPackage) {
                fillSalaryPackage.setSalaryPackages(salaryPackageFuture.join());
            }
        });

        historyStagesVo.setSubmitToJob(submitToJobFuture.join());
        historyStagesVo.setSubmitToClient(submitToClientFuture.join());
        historyStagesVo.setInterviews(interviewsFuture.join());
        historyStagesVo.setOffer(offerFuture.join());
        historyStagesVo.setOfferAccept(offerAcceptFuture.join());
        historyStagesVo.setCommission(commissionFuture.join());
        historyStagesVo.setOnboard(onboardFuture.join());
        historyStagesVo.setEliminate(eliminateFuture.join());

        return historyStagesVo;
    }

    @Override
    public Object getStageByType(Long talentRecruitmentProcessId, NodeType nodeType) {
        var talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId)
                .orElseThrow(() -> new CustomParameterizedException("Talent recruitment process not found by id: " + talentRecruitmentProcessId));

        Object result = switch (nodeType) {
            case UNKNOWN -> throw new CustomParameterizedException("stage type error!");
            case SUBMIT_TO_JOB -> talentRecruitmentProcessSubmitToJobService.findOneByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            case SUBMIT_TO_CLIENT -> getTalentRecruitmentProcessSubmitToClientVOSupplier(talentRecruitmentProcessId, talentRecruitmentProcess).get();
            case INTERVIEW -> talentRecruitmentProcessInterviewService.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            case OFFER -> talentRecruitmentProcessOfferService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            case OFFER_ACCEPT -> ipgOfferAcceptService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            case COMMISSION -> talentRecruitmentProcessCommissionService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            case ON_BOARD, OFF_BOARDED -> talentRecruitmentProcessOnboardService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            case ELIMINATED -> null;
        };
        if (result == null) {
            return null;
        }
        boolean isInterViews = result instanceof List<?> interviews && !interviews.isEmpty();
        Stream<AuditingUser> stageStream = result instanceof List<?> interviews ? (Stream<AuditingUser>) interviews.stream() : Stream.of((AuditingUser) result);
        List<AuditingUser> voList = stageStream.filter(Objects::nonNull).toList();
        var userIds = voList.stream().flatMap(vo -> Stream.of(vo.getLastModifiedBy(), vo.getCreatedBy()))
                .map(SecurityUtils::getUserIdFromCreatedBy).collect(Collectors.toSet());

        var userMap = userService.findBriefUsers(userIds).stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));
        var salaryPackage = isInterViews ? offerSalaryPackageService.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId) : null;
        var feeCharge = isInterViews ? feeChargeService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId) : null;
        var contractFeeCharge = isInterViews ? contractFeeChargeService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId) : null;
        var clientInfo = isInterViews ? clientInfoService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId) : null;

        voList.forEach(vo -> {
            Long createUserId = SecurityUtils.getUserIdFromCreatedBy(vo.getCreatedBy());
            Long modifyUserId = SecurityUtils.getUserIdFromCreatedBy(vo.getLastModifiedBy());
            vo.setCreatedUser(userMap.get(createUserId));
            vo.setLastModifiedUser(userMap.get(modifyUserId));
            if (vo instanceof FillFeeCharge fillFeeCharge) {
                fillFeeCharge.setFeeCharge(isInterViews ? feeCharge : feeChargeService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
            }
            if (vo instanceof FillContractFeeCharge fillContractFeeCharge) {
                fillContractFeeCharge.setContractFeeCharge(isInterViews ? contractFeeCharge : contractFeeChargeService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
            }
            if (vo instanceof FillClientInfo fillClientInfo) {
                fillClientInfo.setClientInfo(isInterViews ? clientInfo : clientInfoService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
            }
            if (vo instanceof FillSalaryPackages fillSalaryPackage) {
                fillSalaryPackage.setSalaryPackages(isInterViews ? salaryPackage : offerSalaryPackageService.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId));
            }
        });

        return result;
    }

    /**
     * 获取入职时间，定制版和通用版
     *
     * @param talentRecruitmentProcessId 流程id
     * @return 入职时间
     */
    @Override
    public LocalDate getOnboardDate(Long talentRecruitmentProcessId) {
        talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId)
                .orElseThrow(() -> new CustomParameterizedException("Talent recruitment process not found by id: " + talentRecruitmentProcessId));

        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeService.findCurrentNode(talentRecruitmentProcessId);
        if (currentNode.getNodeType().toDbValue() >= NodeType.ON_BOARD.toDbValue()) {
            return Optional.ofNullable(talentRecruitmentProcessOnboardService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId))
                    .map(TalentRecruitmentProcessOnboardVO::getOnboardDate).orElse(null);
        }
        if (currentNode.getNodeType().toDbValue() >= NodeType.OFFER_ACCEPT.toDbValue()) {
            return Optional.ofNullable(ipgOfferAcceptService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId))
                    .map(TalentRecruitmentProcessIpgOfferAcceptVO::getOnboardDate).orElse(null);
        }
        if (currentNode.getNodeType().toDbValue() >= NodeType.OFFER.toDbValue()) {
            return Optional.ofNullable(talentRecruitmentProcessOfferService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId))
                    .map(TalentRecruitmentProcessOfferVO::getEstimateOnboardDate).orElse(null);
        }
        return null;
    }

    @Resource
    private TalentRecruitmentProcessSubmitToJobRepository talentRecruitmentProcessSubmitToJobRepository;

    @Resource
    private TalentResumeRelationRepository talentResumeRelationRepository;

    @Resource
    private ResumeRepository resumeRepository;

    @Override
    @Transactional
    public void replaceSubmitToJobResume(ReplaceSubmitToJobResumeDTO replaceSubmitToJobResumeDTO) {
        List<TalentRecruitmentProcessSubmitToJob> originSubmitToJobList = talentRecruitmentProcessSubmitToJobRepository.findAllByTalentResumeRelationIdIs(replaceSubmitToJobResumeDTO.getOriginTalentResumeRelationId());
        if (originSubmitToJobList.isEmpty()) {
            throw new CustomParameterizedException("SubmitToJob process not exist.");
        }
        resumeRepository.findById(replaceSubmitToJobResumeDTO.getReplaceResumeId()).orElseThrow(() -> new CustomParameterizedException("Resume not exist."));
        TalentResumeRelation originResumeRelation = talentResumeRelationRepository.findById(replaceSubmitToJobResumeDTO.getOriginTalentResumeRelationId()).orElseThrow(() -> new CustomParameterizedException("Talent resume relation not exist."));
        TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findByResumeIdAndTenantId(replaceSubmitToJobResumeDTO.getReplaceResumeId(), SecurityUtils.getTenantId());
        if (talentResumeRelation != null) {
            if (!talentResumeRelation.getTalentId().equals(originResumeRelation.getTalentId())) {
                throw new CustomParameterizedException("Resume does not belong to the same person.");
            }
        } else {
            talentResumeRelation = new TalentResumeRelation();
            talentResumeRelation.setResumeId(replaceSubmitToJobResumeDTO.getReplaceResumeId());
            talentResumeRelation.setTalentId(replaceSubmitToJobResumeDTO.getReplaceTalentId());
            talentResumeRelation.setFileName(replaceSubmitToJobResumeDTO.getReplaceFileName());
            talentResumeRelation.setSourceType(replaceSubmitToJobResumeDTO.getReplaceSourceType());
            talentResumeRelation.setTenantId(SecurityUtils.getTenantId());
            talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
            talentResumeRelationRepository.save(talentResumeRelation);
        }
        Long relationId = talentResumeRelation.getId();
        originSubmitToJobList.forEach(c -> c.setTalentResumeRelationId(relationId));
        talentRecruitmentProcessSubmitToJobRepository.saveAll(originSubmitToJobList);
    }

    @NotNull
    private Supplier<TalentRecruitmentProcessSubmitToClientVO> getTalentRecruitmentProcessSubmitToClientVOSupplier(Long id, TalentRecruitmentProcess talentRecruitmentProcess) {
        Authentication authentication = SecurityUtils.getAuthentication();
        return () -> {
            SecurityUtils.setAuthentication(authentication);
            var submitToClientVO = talentRecruitmentProcessSubmitToClientService.findOneByTalentRecruitmentProcessId(id);
            if (submitToClientVO == null) {
                return null;
            }
            Boolean isTheFirstJobToChangeToSubmitToClient = isTheFirstJobToChangeToSubmitToClient(talentRecruitmentProcess.getTalentId(), id);
            submitToClientVO.setTheFirstJobToChangeToSubmitToClient(isTheFirstJobToChangeToSubmitToClient);
            return submitToClientVO;
        };
    }

    @Override
    public Set<Long> filterTalentIdsByUserIdAndTalentIds(Long userId, Set<Long> talentIds) {
        return talentRecruitmentProcessKpiUserRepository.filterTalentIdsByUserIdAndTalentIds(userId, talentIds);
    }

    @Override
    public String getTalentRecruitmentProcessLastInterviewDateTimeByJobId(Long jobId) {
        List<TalentRecruitmentProcessInterview> lastInterview = talentRecruitmentProcessRepository.getLastInterview(jobId);
        if(!lastInterview.isEmpty()) {
            return DateUtil.fromInstantToUtcDateTime(lastInterview.get(0).getCreatedDate());
        }
        return null;
    }

    /**
     * 验证当前用户是否有"提交该候选人到新职位"的权限。因为 IPG 的租户有 start 数据，非 IPG 租户没有，所以要分情况判断
     * @param talentId -> 候选人 ID
     * @return TalentEmploymentStatusVO -> 记录当前候选人的工作状态：入职，在职，保证期内。以及记录当前登录用户是否有权限提交该候选人到新的职位
     */
    @Override
    public TalentEmploymentStatusVO getTalentEmploymentStatus(Long talentId) {
        TalentEmploymentStatusVO talentEmploymentStatusVO = new TalentEmploymentStatusVO();
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            //IPG 租户
            this.validateByIpgRule(talentId, talentEmploymentStatusVO);
        } else {
            //非ipg用户
            this.validateByNonIpgRule(talentId, talentEmploymentStatusVO);
        }
        return talentEmploymentStatusVO;
    }

    /**
     * 判断当前候选人的职位状态
     *  1. 如果当前时间早于入职时间，则职位状态为"待入职"
     *  2. 如果当前时间晚于入职时间，且当前时间早于保证期的结束时间，则职位状态为"保证期内"
     *  3. 否则职位状态为"已入职"（当前时间晚于保证期的结束时间，但是start还是 active 的）
     * @param start -> 记录 start 详情
     * @return EmploymentStatus -> 待入职，保证期内，已入职
     */
    private EmploymentStatus getEmploymentStatus(StartDTO start){
        LocalDate onboardDate = start.getStartDate();
        LocalDate warrantyEndDate = start.getWarrantyEndDate();
        LocalDate now = LocalDate.now();
        // 职位状态：待入职
        if (now.isBefore(onboardDate)){
            return EmploymentStatus.PRE_ONBOARDING;
        }
        // 职位状态：保证期内, 如果是 convert to FTE，则UNDER_WARRANTY也显示ONBOARDED，这是特殊需求
        if (!now.isAfter(warrantyEndDate) && !start.getStartType().equals(StartType.CONVERT_TO_FTE)){
            return EmploymentStatus.UNDER_WARRANTY;
        }
        // 职位状态：已入职
        return EmploymentStatus.ONBOARDED;
    }

    /**
     * 判断 IPG 租户下的登录用户，是否有"提交在职的候选人到新职位"的权限"
     * @param talentId -> 候选人 ID
     * @param talentEmploymentStatusVO -> 记录当前候选人的工作状态：入职，在职，保证期内。以及记录当前登录用户是否有权限提交该候选人到新的职位
     */
    private void validateByIpgRule(Long talentId, TalentEmploymentStatusVO talentEmploymentStatusVO){
        List<StartDTO> activeStarts = financeService.getActiveStartByTalentId(talentId);
        // 如果没有active的start， 则所有人都可以提交该候选人
        if (CollectionUtils.isEmpty(activeStarts)){
            return;
        }
        List<StartDTO> fteStarts = activeStarts.stream().filter(s -> s.getPositionType().equals(JobType.FULL_TIME)).toList();
        // 如果没有active的FTE start， 则所有人都可以提交该候选人
        if (CollectionUtils.isEmpty(fteStarts)){
            return;
        }
        Set<Long> resignedApplicationIds = resignationRepository.findAllByTalentId(talentId).stream().map(TalentRecruitmentProcessResignation::getTalentRecruitmentProcessId).collect(Collectors.toSet());
        fteStarts = fteStarts.stream().filter(s -> !resignedApplicationIds.contains(s.getTalentRecruitmentProcessId())).toList();
        // 如果active的FTE 都处于离职状态， 则所有人都可以提交该候选人
        if (CollectionUtils.isEmpty(fteStarts)){
            return;
        }

        talentEmploymentStatusVO.setHasPermission(SecurityUtils.isAdmin() || cachePermission.hasUserPrivilegePermission(SecurityUtils.getUserId(), applicationProperties.getSubmitToJobPermissionKey()));
        fteStarts.forEach(s -> {
            TalentEmploymentStatusVO.ExistedApplication existedApplication = new TalentEmploymentStatusVO.ExistedApplication();
            existedApplication.setTalentId(s.getTalentId());
            existedApplication.setJobId(s.getJobId());
            existedApplication.setJobTitle(s.getJobTitle());
            existedApplication.setTalentRecruitmentProcessId(s.getTalentRecruitmentProcessId());
            existedApplication.setEmploymentStatus(this.getEmploymentStatus(s));
            talentEmploymentStatusVO.getExistedApplicationList().add(existedApplication);
        });
    }

    /**
     * 判断非 IPG 租户下的登录用户，是否有"提交在职的候选人到新职位"的权限"
     * @param talentId -> 候选人 ID
     * @param talentEmploymentStatusVO -> 记录当前候选人的工作状态：入职，在职，保证期内。以及记录当前登录用户是否有权限提交该候选人到新的职位
     */
    @Deprecated
    private void validateByNonIpgRule(Long talentId, TalentEmploymentStatusVO talentEmploymentStatusVO){
//        List<TalentRecruitmentProcessOnboardDateDTO> onboardDates = onboardDateRepository.getOnboardDateByTalentIdAndNodeTypeAndNodeStatus(talentId, NodeType.ON_BOARD, NodeStatus.COMPLETED);
//        if (CollectionUtils.isNotEmpty(onboardDates)){
//            TalentRecruitmentProcessOnboardDateDTO onboardDate = onboardDates.get(0);
//            talentEmploymentStatusVO.setHasPermission(cachePermission.hasUserPrivilegePermission(SecurityUtils.getUserId(), applicationProperties.getSubmitToJobPermissionKey()));
//            talentEmploymentStatusVO.setTalentId(talentId);
//            talentEmploymentStatusVO.setJobId(onboardDate.getJobId());
//            JobDTOV3 job = jobClient.getJob(onboardDate.getJobId()).getBody();
//            talentEmploymentStatusVO.setJobTitle(Objects.nonNull(job) ? job.getTitle() : "");
//            talentEmploymentStatusVO.setTalentRecruitmentProcessId(onboardDate.getTalentRecruitmentProcessId());
//            talentEmploymentStatusVO.setEmploymentStatus(this.getEmploymentStatus(onboardDate.getOnboardDate(), onboardDate.getWarrantyEndDate()));
//        }
    }

    @Override
    public boolean getSubmitOnboardedTalentToJobPermission() {
        return cachePermission.hasUserPrivilegePermission(SecurityUtils.getUserId(), applicationProperties.getSubmitToJobPermissionKey());
    }

    @Override
    public Page<ApplicationIdAndStatusVO> getStats(Set<Long> talentRecruitmentProcessIds, NodeType nodeType, Pageable pageable) {
        if (Objects.isNull(nodeType)) {
            List<ApplicationIdAndStatusVO> res = talentRecruitmentProcessRepository.getAllLatestStatusByApplicationId(talentRecruitmentProcessIds);
            res.stream().filter(applicationIdAndStatusVO -> Objects.isNull(applicationIdAndStatusVO.getLatestStatus()))
                    .forEach(applicationIdAndStatusVO -> applicationIdAndStatusVO.setLatestStatus(NodeType.ELIMINATED));
            return new PageImpl<>(res, pageable, talentRecruitmentProcessIds.size());
        } else if (NodeType.ELIMINATED.equals(nodeType)) {
            return talentRecruitmentProcessRepository.getApplicationIdsInEliminate(talentRecruitmentProcessIds, pageable);
        } else {
            return talentRecruitmentProcessRepository.getApplicationIdsByStatus(talentRecruitmentProcessIds, nodeType, pageable);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONArray deleteByJobId(Long jobId) {
        JSONArray recruitmentProcessArray = new JSONArray();
        List<TalentRecruitmentProcess> talentRecruitmentProcessList = talentRecruitmentProcessRepository.findAllByJobId(jobId);
        List<Long> recruitmentIds = talentRecruitmentProcessList.stream().map(TalentRecruitmentProcess::getId).toList();
        List<TalentRecruitmentProcessNode> talentRecruitmentProcessNodeList = talentRecruitmentProcessNodeRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessSubmitToJob> talentRecruitmentProcessSubmitToJobList = talentRecruitmentProcessSubmitToJobRepository.findAllByTalentResumeRelationIdIn(recruitmentIds);
        List<TalentRecruitmentProcessSubmitToClient> talentRecruitmentProcessSubmitToClientList = talentRecruitmentProcessSubmitToClientRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessInterview> talentRecruitmentProcessInterviewList = talentRecruitmentProcessInterviewRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessOffer> talentRecruitmentProcessOfferList = talentRecruitmentProcessOfferRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessIpgOfferAccept> talentRecruitmentProcessIpgOfferAcceptList = talentRecruitmentProcessIpgOfferAcceptRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessOnboard> talentRecruitmentProcessOnboardList = talentRecruitmentProcessOnboardRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessOnboardDate> talentRecruitmentProcessOnboardDateList = talentRecruitmentProcessOnboardDateRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessKpiUser> talentRecruitmentProcessKpiUserList = talentRecruitmentProcessKpiUserRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessIpgAgreedPayRate> talentRecruitmentProcessIpgAgreedPayRateList = talentRecruitmentProcessIpgAgreedPayRateRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessEliminate> talentRecruitmentProcessEliminateList = talentRecruitmentProcessEliminateRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessOfferSalaryPackage> talentRecruitmentProcessOfferSalaryPackageList = talentRecruitmentProcessOfferSalaryPackageRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessOfferFeeCharge> talentRecruitmentProcessOfferFeeChargeList = talentRecruitmentProcessOfferFeeChargeRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessOnboardClientInfo> talentRecruitmentProcessOnboardClientInfoList = talentRecruitmentProcessOnboardClientInfoRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        List<TalentRecruitmentProcessIpgContractFeeCharge> talentRecruitmentProcessIpgContractFeeChargeList = talentRecruitmentProcessIpgContractFeeChargeRepository.findAllByTalentRecruitmentProcessIdIn(recruitmentIds);
        Map<Long, List<TalentRecruitmentProcessNode>> talentRecruitmentProcessNodeMap = talentRecruitmentProcessNodeList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessNode::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessSubmitToJob>> talentRecruitmentProcessSubmitToJobMap = talentRecruitmentProcessSubmitToJobList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessSubmitToJob::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessSubmitToClient>> talentRecruitmentProcessSubmitToClientMap = talentRecruitmentProcessSubmitToClientList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessSubmitToClient::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessInterview>> talentRecruitmentProcessInterviewMap = talentRecruitmentProcessInterviewList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessInterview::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessOffer>> talentRecruitmentProcessOfferMap = talentRecruitmentProcessOfferList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessOffer::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessIpgOfferAccept>> talentRecruitmentProcessIpgOfferAcceptMap = talentRecruitmentProcessIpgOfferAcceptList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessIpgOfferAccept::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessOnboard>> talentRecruitmentProcessOnboardMap = talentRecruitmentProcessOnboardList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessOnboard::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessOnboardDate>> talentRecruitmentProcessOnboardDateMap = talentRecruitmentProcessOnboardDateList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessOnboardDate::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessKpiUser>> talentRecruitmentProcessKpiUserMap = talentRecruitmentProcessKpiUserList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessKpiUser::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessIpgAgreedPayRate>> talentRecruitmentProcessIpgAgreedPayRateMap = talentRecruitmentProcessIpgAgreedPayRateList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessIpgAgreedPayRate::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessEliminate>> talentRecruitmentProcessEliminateMap = talentRecruitmentProcessEliminateList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessEliminate::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessOfferSalaryPackage>> talentRecruitmentProcessOfferSalaryPackageMap = talentRecruitmentProcessOfferSalaryPackageList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessOfferSalaryPackage::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessOfferFeeCharge>> talentRecruitmentProcessOfferFeeChargeMap = talentRecruitmentProcessOfferFeeChargeList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessOfferFeeCharge::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessOnboardClientInfo>> talentRecruitmentProcessOnboardClientInfoMap = talentRecruitmentProcessOnboardClientInfoList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessOnboardClientInfo::getTalentRecruitmentProcessId));
        Map<Long, List<TalentRecruitmentProcessIpgContractFeeCharge>> talentRecruitmentProcessIpgContractFeeChargeMap = talentRecruitmentProcessIpgContractFeeChargeList.stream().collect(Collectors.groupingBy(TalentRecruitmentProcessIpgContractFeeCharge::getTalentRecruitmentProcessId));
        talentRecruitmentProcessList.forEach(o -> {
            JSONObject item = DtoToJsonUtil.toJsonWithColumnNames(o);
            if (talentRecruitmentProcessNodeMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_node", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessNodeMap.get(o.getId())));
            }
            if (talentRecruitmentProcessSubmitToJobMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_submit_to_job", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessSubmitToJobMap.get(o.getId())));
            }
            if (talentRecruitmentProcessSubmitToClientMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_submit_to_client", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessSubmitToClientMap.get(o.getId())));
            }
            if (talentRecruitmentProcessInterviewMap.containsKey(o.getId())) {
                List<TalentRecruitmentProcessInterview> interviews = talentRecruitmentProcessInterviewMap.get(o.getId());
                JSONArray calendarEventByInterview = commonClient.deleteCalendarEventsByRecruitmentProcessRelationIds(talentRecruitmentProcessInterviewList.stream().map(TalentRecruitmentProcessInterview::getId).toList()).getBody();
                item.put("calendar_event_interview", calendarEventByInterview);
                item.put("talent_recruitment_process_interview", DtoToJsonUtil.toJsonArrayWithColumnNames(interviews));

            }
            if (talentRecruitmentProcessOfferMap.containsKey(o.getId())) {
                List<TalentRecruitmentProcessOffer> offers = talentRecruitmentProcessOfferMap.get(o.getId());
                JSONArray calendarEventByOffer = commonClient.deleteCalendarEventsByRecruitmentProcessRelationIds(talentRecruitmentProcessOfferList.stream().map(TalentRecruitmentProcessOffer::getId).toList()).getBody();
                item.put("calendar_event_offer", calendarEventByOffer);
                item.put("talent_recruitment_process_offer", DtoToJsonUtil.toJsonArrayWithColumnNames(offers));
            }
            if (talentRecruitmentProcessIpgOfferAcceptMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_ipg_offer_accept", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessIpgOfferAcceptMap.get(o.getId())));
            }
            if (talentRecruitmentProcessOnboardMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_onboard", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessOnboardMap.get(o.getId())));
            }
            if (talentRecruitmentProcessOnboardDateMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_onboard_date", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessOnboardDateMap.get(o.getId())));
            }
            if (talentRecruitmentProcessKpiUserMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_kpi_user", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessKpiUserMap.get(o.getId())));
            }
            if (talentRecruitmentProcessIpgAgreedPayRateMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_ipg_agreed_pay_rate", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessIpgAgreedPayRateMap.get(o.getId())));
            }
            if (talentRecruitmentProcessEliminateMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_eliminate", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessEliminateMap.get(o.getId())));
            }
            if (talentRecruitmentProcessOfferSalaryPackageMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_offer_salary_package", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessOfferSalaryPackageMap.get(o.getId())));
            }
            if (talentRecruitmentProcessOfferFeeChargeMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_offer_fee_charge", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessOfferFeeChargeMap.get(o.getId())));
            }
            if (talentRecruitmentProcessOnboardClientInfoMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_onboard_client_info", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessOnboardClientInfoMap.get(o.getId())));
            }
            if (talentRecruitmentProcessIpgContractFeeChargeMap.containsKey(o.getId())) {
                item.put("talent_recruitment_process_ipg_contract_fee_charge", DtoToJsonUtil.toJsonArrayWithColumnNames(talentRecruitmentProcessIpgContractFeeChargeMap.get(o.getId())));
            }
            recruitmentProcessArray.add(item);
        });
        JSONArray calenderEvent = commonClient.deleteCalendarEventsByRecruitmentProcessRelationIds(recruitmentIds).getBody();
        recruitmentProcessArray.addAll(calenderEvent);

        talentRecruitmentProcessRepository.deleteAllByIdInBatch(talentRecruitmentProcessList.stream().map(TalentRecruitmentProcess::getId).toList());
        talentRecruitmentProcessNodeRepository.deleteAllByIdInBatch(talentRecruitmentProcessNodeList.stream().map(TalentRecruitmentProcessNode::getId).collect(Collectors.toList()));
        talentRecruitmentProcessSubmitToJobRepository.deleteAllByIdInBatch(talentRecruitmentProcessSubmitToJobList.stream().map(TalentRecruitmentProcessSubmitToJob::getId).toList());
        talentRecruitmentProcessSubmitToClientRepository.deleteAllByIdInBatch(talentRecruitmentProcessSubmitToClientList.stream().map(TalentRecruitmentProcessSubmitToClient::getId).collect(Collectors.toList()));
        talentRecruitmentProcessInterviewRepository.deleteAllByIdInBatch(talentRecruitmentProcessInterviewList.stream().map(TalentRecruitmentProcessInterview::getId).toList());
        talentRecruitmentProcessOfferRepository.deleteAllByIdInBatch(talentRecruitmentProcessOfferList.stream().map(TalentRecruitmentProcessOffer::getId).collect(Collectors.toList()));
        talentRecruitmentProcessIpgOfferAcceptRepository.deleteAllByIdInBatch(talentRecruitmentProcessIpgOfferAcceptList.stream().map(TalentRecruitmentProcessIpgOfferAccept::getId).toList());
        talentRecruitmentProcessOnboardRepository.deleteAllByIdInBatch(talentRecruitmentProcessOnboardList.stream().map(TalentRecruitmentProcessOnboard::getId).collect(Collectors.toList()));
        talentRecruitmentProcessOnboardDateRepository.deleteAllByIdInBatch(talentRecruitmentProcessOnboardDateList.stream().map(TalentRecruitmentProcessOnboardDate::getId).toList());
        talentRecruitmentProcessKpiUserRepository.deleteAllByIdInBatch(talentRecruitmentProcessKpiUserList.stream().map(TalentRecruitmentProcessKpiUser::getId).collect(Collectors.toList()));
        talentRecruitmentProcessIpgAgreedPayRateRepository.deleteAllByIdInBatch(talentRecruitmentProcessIpgAgreedPayRateList.stream().map(TalentRecruitmentProcessIpgAgreedPayRate::getId).toList());
        talentRecruitmentProcessEliminateRepository.deleteAllByIdInBatch(talentRecruitmentProcessEliminateList.stream().map(TalentRecruitmentProcessEliminate::getId).collect(Collectors.toList()));
        talentRecruitmentProcessOfferSalaryPackageRepository.deleteAllByIdInBatch(talentRecruitmentProcessOfferSalaryPackageList.stream().map(TalentRecruitmentProcessOfferSalaryPackage::getId).toList());
        talentRecruitmentProcessOfferFeeChargeRepository.deleteAllByIdInBatch(talentRecruitmentProcessOfferFeeChargeList.stream().map(TalentRecruitmentProcessOfferFeeCharge::getId).collect(Collectors.toList()));
        talentRecruitmentProcessOnboardClientInfoRepository.deleteAllByIdInBatch(talentRecruitmentProcessOnboardClientInfoList.stream().map(TalentRecruitmentProcessOnboardClientInfo::getId).toList());
        talentRecruitmentProcessIpgContractFeeChargeRepository.deleteAllByIdInBatch(talentRecruitmentProcessIpgContractFeeChargeList.stream().map(TalentRecruitmentProcessIpgContractFeeCharge::getId).collect(Collectors.toList()));

        return recruitmentProcessArray;
    }

    @Override
    public List<SubstituteTalentVO> getSubstituteTalentByJobId(Long jobId) {
        List<SubstituteTalentVO> substituteTalentVOList = processNativeRepository.selectSubstituteTalentByJobId(jobId);
        return substituteTalentVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void aiRecommendByJobIdAndTalentId(JSONObject aiRecommendJson) {
        List<JobCommonTalentAiRecommend> commonTalentAiRecommends = new ArrayList<>();
        List<JobTalentAiRecommend> talentAiRecommends = new ArrayList<>();
        if (aiRecommendJson.getStr("sourceType").equals(AIRecommendType.JOB.getDescription())) {
            Long jobId = aiRecommendJson.getLong("sourceId");
            Long sourceTenantId = aiRecommendJson.getLong("sourceTenantId");
            JSONArray resultList = aiRecommendJson.getJSONArray("recommendations");
            for (int i = 0; i < resultList.size(); i++) {
                JSONObject result = resultList.getJSONObject(i);
                String talentIdStr = result.getStr("targetId");
                Short score = result.getShort("score");
                if (!isLong(talentIdStr)) {
                    JobCommonTalentAiRecommend bean = new JobCommonTalentAiRecommend();
                    bean.setAiScore(score);
                    bean.setJobId(jobId);
                    bean.setTalentId(talentIdStr);
                    bean.setTenantId(sourceTenantId);
                    bean.setSourceType(AIRecommendType.JOB.getDescription());
                    commonTalentAiRecommends.add(bean);
                } else {
                    Long targetTenantId = aiRecommendJson.getLong("targetTenantId");
                    JobTalentAiRecommend bean = new JobTalentAiRecommend();
                    bean.setAiScore(score);
                    bean.setJobId(jobId);
                    bean.setTalentId(Long.valueOf(talentIdStr));
                    bean.setTenantId(targetTenantId);
                    bean.setSourceTenantId(sourceTenantId);
                    bean.setSourceType(AIRecommendType.JOB.getDescription());
                    talentAiRecommends.add(bean);
                }
            }

            saveCommonTalendByJobId(commonTalentAiRecommends);
            saveTalentByJobId(talentAiRecommends);

        } else if (aiRecommendJson.getStr("sourceType").equals(AIRecommendType.TALENT.getDescription())) {
            Long talendId = aiRecommendJson.getLong("sourceId");
            Long sourceTenantId = aiRecommendJson.getLong("sourceTenantId");
            Long targetTenantId = aiRecommendJson.getLong("targetTenantId");
            JSONArray resultList = aiRecommendJson.getJSONArray("recommendations");
            for (int i = 0; i < resultList.size(); i++) {
                JSONObject result = resultList.getJSONObject(i);
                Long jobId = result.getLong("targetId");
                Short score = result.getShort("score");
                JobTalentAiRecommend bean = new JobTalentAiRecommend();
                bean.setAiScore(score);
                bean.setJobId(jobId);
                bean.setTalentId(talendId);
                bean.setTenantId(targetTenantId);
                bean.setSourceTenantId(sourceTenantId);
                bean.setSourceType(AIRecommendType.TALENT.getDescription());
                talentAiRecommends.add(bean);
            }
            saveJobByTalentId(talentAiRecommends);
        } else if (aiRecommendJson.getStr("sourceType").equals(AIRecommendType.COMMON_TALENT.getDescription())) {
            String talendId = aiRecommendJson.getStr("sourceId");
            Long targetTenantId = aiRecommendJson.getLong("targetTenantId");
            JSONArray resultList = aiRecommendJson.getJSONArray("recommendations");
            for (int i = 0; i < resultList.size(); i++) {
                JSONObject result = resultList.getJSONObject(i);
                Long jobId = result.getLong("targetId");
                Short score = result.getShort("score");
                JobCommonTalentAiRecommend bean = new JobCommonTalentAiRecommend();
                bean.setAiScore(score);
                bean.setJobId(jobId);
                bean.setTalentId(talendId);
                bean.setTenantId(targetTenantId);
                bean.setSourceType(AIRecommendType.COMMON_TALENT.getDescription());
                commonTalentAiRecommends.add(bean);
            }
            saveCommonTalendByTalentId(commonTalentAiRecommends);
        }
    }

    private void saveJobByTalentId(List<JobTalentAiRecommend> talentAiRecommends) {
        if (!talentAiRecommends.isEmpty()) {
            List<Long> jobIds = talentAiRecommends.stream().map(JobTalentAiRecommend::getJobId).collect(Collectors.toList());
            Map<Long, JobTalentAiRecommend> jobAiRecommendMap = talentAiRecommends.stream().collect(Collectors.toMap(JobTalentAiRecommend::getJobId, Function.identity(), (key1, key2) -> key2));

            //查询是否已经存在
            List<JobTalentAiRecommend> talentAiRecommendList = jobTalentAiRecommendRepository.findByTenantIdAndJobIdInAndTalentId(talentAiRecommends.get(0).getTenantId(),jobIds,talentAiRecommends.get(0).getTalentId());
            if (!talentAiRecommendList.isEmpty()) {

                Set<String> existingKeySet = talentAiRecommendList.stream()
                        .map(x -> x.getJobId() + "_" + x.getTalentId())
                        .collect(Collectors.toSet());

                // 移除 jobId + talentId 同时匹配的数据
                talentAiRecommends.removeIf(item ->
                        existingKeySet.contains(item.getJobId() + "_" + item.getTalentId())
                );

                /*talentAiRecommendList.forEach(x -> {
                    talentAiRecommends.forEach(z -> {
                        if (z.getTalentId().equals(x.getTalentId()) && z.getJobId().equals(x.getJobId())) {
                            z.setId(x.getId());
                        }
                    });
                });*/
            }

            if (talentAiRecommends.isEmpty()) {
                return;
            }

            //查询流程数据
            List<TalentRecruitmentProcess> recruitmentProcessList = talentRecruitmentProcessRepository.findByTalentIdAndJobIdIn(talentAiRecommends.get(0).getTalentId(), jobIds);
            if (!recruitmentProcessList.isEmpty()) {
                recruitmentProcessList.forEach(x -> {
                    if (jobAiRecommendMap.containsKey(x.getJobId())) {
                        JobTalentAiRecommend talentAiRecommend = jobAiRecommendMap.getOrDefault(x.getJobId(), null);
                        if (null != talentAiRecommend) {
                            //x.setAiScore(talentAiRecommend.getAiScore());
                            talentRecruitmentProcessRepository.updateAiSourceById(x.getId(),talentAiRecommend.getAiScore());
                        }
                    }
                });
                //talentRecruitmentProcessRepository.saveAll(recruitmentProcessList);
                log.info("[aiRecommendByJobIdAndTalentId] update recruitmentProcessList by talent ai recommend, jobId:{},talentIds:{}", JSONUtil.toJsonStr(jobIds), talentAiRecommends.get(0).getTalentId());
            }
            jobTalentAiRecommendRepository.saveAll(talentAiRecommends);
            log.info("[aiRecommendByJobIdAndTalentId] save talent and job info by talent ai recommend, talentId:{}", talentAiRecommends.get(0).getTalentId());
        }
    }

    private void saveCommonTalendByJobId(List<JobCommonTalentAiRecommend> commonTalentAiRecommends) {
        //查询通过esId和jobId 查询流程数据
        if (!commonTalentAiRecommends.isEmpty()) {
            Map<String, JobCommonTalentAiRecommend> commonTalentAiRecommendMap = commonTalentAiRecommends.stream().collect(Collectors.toMap(JobCommonTalentAiRecommend::getTalentId, Function.identity(), (key1, key2) -> key2));
            List<String> talentEsId = commonTalentAiRecommends.stream().map(JobCommonTalentAiRecommend::getTalentId).collect(Collectors.toList());
            //查询是否已经存在
            List<JobCommonTalentAiRecommend> commonTalentAiRecommendList =  jobCommonTalentAiRecommendRepository.findByTenantIdAndJobIdAndTalentIdIn(commonTalentAiRecommends.get(0).getTenantId(),commonTalentAiRecommends.get(0).getJobId(),talentEsId);
            if (!commonTalentAiRecommendList.isEmpty()) {
                Set<String> existingKeySet = commonTalentAiRecommendList.stream()
                        .map(x -> x.getJobId() + "_" + x.getTalentId())
                        .collect(Collectors.toSet());

                // 移除 jobId + talentId 同时匹配的数据
                commonTalentAiRecommends.removeIf(item ->
                        existingKeySet.contains(item.getJobId() + "_" + item.getTalentId())
                );

                /*commonTalentAiRecommendList.forEach(x -> {
                    commonTalentAiRecommends.forEach(z -> {
                        if (z.getTalentId().equals(x.getTalentId()) && z.getJobId().equals(x.getJobId())) {
                            z.setId(x.getId());
                        }
                    });
                });*/
            }

            if (commonTalentAiRecommends.isEmpty()) {
                return;
            }

            //查询esId 与talentId 绑定信息
            List<CreditTransaction> creditTransactions = creditTransactionRepository.findAllByTenantIdAndStatusAndProfileIdIn(commonTalentAiRecommends.get(0).getTenantId(), Status.Available, talentEsId);
            creditTransactions = creditTransactions.stream().filter(x -> x.getTalentId() != null).collect(Collectors.toList());
            if (!creditTransactions.isEmpty()) {
                Map<Long, String> talentIdEsIdMap = creditTransactions.stream().collect(Collectors.toMap(CreditTransaction::getTalentId, CreditTransaction::getProfileId));
                List<Long> talentIds = creditTransactions.stream().map(CreditTransaction::getTalentId).collect(Collectors.toList());
                //根据jobId和talendId查询流程数据
                List<TalentRecruitmentProcess> recruitmentProcessList = talentRecruitmentProcessRepository.findByJobIdAndTalentIdIn(commonTalentAiRecommends.get(0).getJobId(), talentIds);
                if (!recruitmentProcessList.isEmpty()) {
                    recruitmentProcessList.forEach(x -> {
                        if (talentIdEsIdMap.containsKey(x.getTalentId())) {
                            JobCommonTalentAiRecommend commonTalentAiRecommend = commonTalentAiRecommendMap.getOrDefault(talentIdEsIdMap.get(x.getTalentId()), null);
                            if (null != commonTalentAiRecommend) {
                                talentRecruitmentProcessRepository.updateAiSourceById(x.getId(),commonTalentAiRecommend.getAiScore());
                            }
                        }
                    });
                    //talentRecruitmentProcessRepository.saveAll(recruitmentProcessList);
                    log.info("[aiRecommendByJobIdAndTalentId] update recruitmentProcessList by job recommend common talent info, jobId:{},talentIds:{}", commonTalentAiRecommends.get(0).getJobId(), JSONUtil.toJsonStr(talentIds));
                }
            }
            jobCommonTalentAiRecommendRepository.saveAll(commonTalentAiRecommends);
            log.info("[aiRecommendByJobIdAndTalentId] save job recommend common talent info, jobId:{}", commonTalentAiRecommends.get(0).getJobId());
        }
    }

    private void saveTalentByJobId(List<JobTalentAiRecommend> talentAiRecommends) {
        if (!talentAiRecommends.isEmpty()) {
            List<Long> talentIds = talentAiRecommends.stream().map(JobTalentAiRecommend::getTalentId).collect(Collectors.toList());
            Map<Long, JobTalentAiRecommend> talentAiRecommendMap = talentAiRecommends.stream().collect(Collectors.toMap(JobTalentAiRecommend::getTalentId, Function.identity(), (key1, key2) -> key2));

            //查询是否已经存在
            List<JobTalentAiRecommend> talentAiRecommendList = jobTalentAiRecommendRepository.findByTenantIdAndJobIdAndTalentIdIn(talentAiRecommends.get(0).getTenantId(), talentAiRecommends.get(0).getJobId(), talentIds);
            if (!talentAiRecommendList.isEmpty()) {

                Set<String> existingKeySet = talentAiRecommendList.stream()
                        .map(x -> x.getJobId() + "_" + x.getTalentId())
                        .collect(Collectors.toSet());

                // 移除 jobId + talentId 同时匹配的数据
                talentAiRecommends.removeIf(item ->
                        existingKeySet.contains(item.getJobId() + "_" + item.getTalentId())
                );

                /*talentAiRecommendList.forEach(x -> {
                    talentAiRecommends.forEach(z -> {
                        if (z.getTalentId().equals(x.getTalentId()) && z.getJobId().equals(x.getJobId())) {
                            z.setId(x.getId());
                        }
                    });
                });*/
            }

            if (talentAiRecommends.isEmpty()) {
                return;
            }

            List<TalentRecruitmentProcess> recruitmentProcessList = talentRecruitmentProcessRepository.findByJobIdAndTalentIdIn(talentAiRecommends.get(0).getJobId(), talentIds);
            if (!recruitmentProcessList.isEmpty()) {
                recruitmentProcessList.forEach(x -> {
                    if (talentAiRecommendMap.containsKey(x.getTalentId())) {
                        JobTalentAiRecommend talentAiRecommend = talentAiRecommendMap.getOrDefault(x.getTalentId(), null);
                        if (null != talentAiRecommend) {
                            //x.setAiScore(talentAiRecommend.getAiScore());
                            talentRecruitmentProcessRepository.updateAiSourceById(x.getId(),talentAiRecommend.getAiScore());
                        }
                    }
                });
                //talentRecruitmentProcessRepository.saveAll(recruitmentProcessList);
                log.info("[aiRecommendByJobIdAndTalentId] update recruitmentProcessList by talentAiRecommends, jobId:{},talentIds:{}", talentAiRecommends.get(0).getJobId(), JSONUtil.toJsonStr(talentIds));
            }
            jobTalentAiRecommendRepository.saveAll(talentAiRecommends);
            log.info("[aiRecommendByJobIdAndTalentId] save talent recommend job info, jobId:{}", talentAiRecommends.get(0).getJobId());
        }
    }

    /**
     * 一个 talentId对应多个JobId
     * talentId 是str类型
     * jobId 是long
     * @param commonTalentAiRecommends
     */
    private void saveCommonTalendByTalentId(List<JobCommonTalentAiRecommend> commonTalentAiRecommends) {
        //查询通过esId和talentId 查询流程数据
        if (!commonTalentAiRecommends.isEmpty()) {

            Map<Long, JobCommonTalentAiRecommend> commonTalentAiRecommendMap = commonTalentAiRecommends.stream().collect(Collectors.toMap(JobCommonTalentAiRecommend::getJobId, Function.identity(), (key1, key2) -> key2));
            List<Long> jobIds = commonTalentAiRecommends.stream().map(JobCommonTalentAiRecommend::getJobId).collect(Collectors.toList());
            //查询是否已经存在
            List<JobCommonTalentAiRecommend> commonTalentAiRecommendList = jobCommonTalentAiRecommendRepository.findByTenantIdAndJobIdInAndTalentId(commonTalentAiRecommends.get(0).getTenantId(), jobIds, commonTalentAiRecommends.get(0).getTalentId());
            if (!commonTalentAiRecommendList.isEmpty()) {

                Set<String> existingKeySet = commonTalentAiRecommendList.stream()
                        .map(x -> x.getJobId() + "_" + x.getTalentId())
                        .collect(Collectors.toSet());

                // 移除 jobId + talentId 同时匹配的数据
                commonTalentAiRecommends.removeIf(item ->
                        existingKeySet.contains(item.getJobId() + "_" + item.getTalentId())
                );

                /*commonTalentAiRecommendList.forEach(x -> {
                    commonTalentAiRecommends.forEach(z -> {
                        if (z.getTalentId().equals(x.getTalentId()) && z.getJobId().equals(x.getJobId())) {
                            z.setId(x.getId());
                        }
                    });
                });*/
            }

            if (commonTalentAiRecommends.isEmpty()) {
                return;
            }

            //查询esId 与talentId 绑定信息
            List<CreditTransaction> creditTransactions = creditTransactionRepository.findAllByTenantIdAndStatusAndProfileId(commonTalentAiRecommends.get(0).getTenantId(), Status.Available, commonTalentAiRecommends.get(0).getTalentId());
            creditTransactions = creditTransactions.stream().filter(x -> x.getTalentId() != null).collect(Collectors.toList());
            if (!creditTransactions.isEmpty()) {
                Long talentId = creditTransactions.get(0).getTalentId();
                //根据jobId和talendId查询流程数据
                List<TalentRecruitmentProcess> recruitmentProcessList = talentRecruitmentProcessRepository.findByTalentIdAndJobIdIn(talentId, jobIds);
                if (!recruitmentProcessList.isEmpty()) {
                    recruitmentProcessList.forEach(x -> {
                        JobCommonTalentAiRecommend commonTalentAiRecommend = commonTalentAiRecommendMap.getOrDefault(x.getJobId(), null);
                        if (null != commonTalentAiRecommend) {
                            //x.setAiScore(commonTalentAiRecommend.getAiScore());
                            talentRecruitmentProcessRepository.updateAiSourceById(x.getId(),commonTalentAiRecommend.getAiScore());
                        }
                    });
                    //talentRecruitmentProcessRepository.saveAll(recruitmentProcessList);
                    log.info("[aiRecommendByJobIdAndTalentId] update recruitmentProcessList by common talent recommend job info,talentId:{},jobIds:{}", commonTalentAiRecommends.get(0).getTalentId(), JSONUtil.toJsonStr(jobIds));
                }
            }
            jobCommonTalentAiRecommendRepository.saveAll(commonTalentAiRecommends);
            log.info("[aiRecommendByJobIdAndTalentId] save common talent recommend job info, talentId:{}", commonTalentAiRecommends.get(0).getTalentId());
        }
    }

    private boolean isLong(String talentIdStr) {
        try {
            Long.parseLong(talentIdStr);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
