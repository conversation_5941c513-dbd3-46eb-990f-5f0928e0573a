package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessKpiUser;
import com.altomni.apn.application.repository.TalentRecruitmentProcessKpiUserRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserService;
import com.altomni.apn.application.service.user.UserService;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessKpiUser}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessKpiUserServiceImpl implements TalentRecruitmentProcessKpiUserService {

    @Resource
    private TalentRecruitmentProcessKpiUserRepository kpiUserRepository;
    @Resource
    private UserService userService;

    @PersistenceContext
    private EntityManager entityManager;

    private final static List<UserRole> excludeOwnerRole = Arrays.asList(UserRole.RECRUITER, UserRole.AM,UserRole.CO_AM, UserRole.SOURCER, UserRole.AC, UserRole.DM, UserRole.PR);

    @Override
    public List<TalentRecruitmentProcessKpiUserVO> save(Long talentRecruitmentProcessId, List<TalentRecruitmentProcessKpiUserVO> kpiUserVOS) { //TODO: performance optimize 1. compare current kipUsers with new kipUsers 2. update only necessary kpiUser
        if (CollectionUtils.isNotEmpty(kpiUserVOS)) {
            /* back-end doesn't need to validate the kpi user's commission; currently front-end will validate
            List<TalentRecruitmentProcessKpiUser> owners = kpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(talentRecruitmentProcessId, Collections.singletonList(UserRole.OWNER));
            if (CollectionUtils.isNotEmpty(owners)) {
                kpiUserVOS = kpiUserVOS.stream().filter(o -> !o.getUserRole().equals(UserRole.OWNER)).collect(Collectors.toList());
                BigDecimal sum = new BigDecimal(0);
                for (TalentRecruitmentProcessKpiUserVO o: kpiUserVOS) {
                    sum = sum.add(o.getPercentage());
                }
                if (sum.compareTo(new BigDecimal(90)) != 0) {
                    throw new CustomParameterizedException("Invalid commission!");
                }
            } else {
                BigDecimal sum = new BigDecimal(0);
                for (TalentRecruitmentProcessKpiUserVO o: kpiUserVOS) {
                    sum = sum.add(o.getPercentage());
                }
                if (sum.compareTo(new BigDecimal(100)) != 0 && sum.compareTo(new BigDecimal(0)) != 0) {
                    throw new CustomParameterizedException("Invalid commission!");
                }
            }
             */
//            List<TalentRecruitmentProcessKpiUser> exist = kpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(talentRecruitmentProcessId, excludeOwnerRole);
            List<TalentRecruitmentProcessKpiUserVO> owners = kpiUserVOS.stream().filter(u -> UserRole.OWNER.equals(u.getUserRole())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(owners)) {
//                List<TalentRecruitmentProcessKpiUser> existingOwners = kpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(talentRecruitmentProcessId, Collections.singletonList(UserRole.OWNER));
//                if (owners.size() != existingOwners.size()) {
//                    kpiUserRepository.deleteAll(existingOwners);
//                }
                List<TalentRecruitmentProcessKpiUser> exist = kpiUserRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId);
                if (CollectionUtils.isNotEmpty(exist)) {
                    List<Long> ids = exist.stream().map(TalentRecruitmentProcessKpiUser::getId).collect(Collectors.toList());
                    kpiUserRepository.deleteKpiUserById(ids);
                }
            } else {
                List<TalentRecruitmentProcessKpiUser> ownerList = kpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleIn(talentRecruitmentProcessId, Collections.singletonList(UserRole.OWNER));
                if (CollectionUtils.isNotEmpty(ownerList)) {
                    ownerList.forEach(x -> {
                        TalentRecruitmentProcessKpiUserVO bean = new TalentRecruitmentProcessKpiUserVO();
                        bean.setAmount(x.getAmount());
                        bean.setUserId(x.getUserId());
                        bean.setUserRole(x.getUserRole());
                        bean.setPercentage(x.getPercentage());
                        bean.setCurrency(x.getCurrency());
                        bean.setCountry(x.getCountry());
                        kpiUserVOS.add(bean);
                    });

                    List<TalentRecruitmentProcessKpiUser> exist = kpiUserRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId);
                    if (CollectionUtils.isNotEmpty(exist)) {
                        List<Long> ids = exist.stream().map(TalentRecruitmentProcessKpiUser::getId).collect(Collectors.toList());
                        kpiUserRepository.deleteKpiUserById(ids);
                    }
                } else {
                    List<TalentRecruitmentProcessKpiUser> exist = kpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleNotIn(talentRecruitmentProcessId, Collections.singletonList(UserRole.OWNER));
                    if (CollectionUtils.isNotEmpty(exist)) {
                        List<Long> ids = exist.stream().map(TalentRecruitmentProcessKpiUser::getId).collect(Collectors.toList());
                        kpiUserRepository.deleteKpiUserById(ids);
                    }
                }
            }
            List<TalentRecruitmentProcessKpiUser> kpiUsers = kpiUserVOS.stream().map(kpiUserVO -> {
                TalentRecruitmentProcessKpiUser kpiUser = toEntity(kpiUserVO);
                kpiUser.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
                return kpiUser;
            }).collect(Collectors.toList());
            entityManager.clear();
            return toVO(kpiUserRepository.saveAllAndFlush(kpiUsers));
        }
        return Lists.newArrayList();
    }

    private TalentRecruitmentProcessKpiUser toEntity(TalentRecruitmentProcessKpiUserVO kpiUserVO) {
        if (kpiUserVO != null) {
            TalentRecruitmentProcessKpiUser result = new TalentRecruitmentProcessKpiUser();
            ServiceUtils.myCopyProperties(kpiUserVO, result);
            return result;
        }
        return null;
    }

    private List<TalentRecruitmentProcessKpiUserVO> toVO(List<TalentRecruitmentProcessKpiUser> kpiUsers) {
        if (CollectionUtils.isEmpty(kpiUsers)) {
            return Lists.newArrayList();
        }
        List<TalentRecruitmentProcessKpiUserVO> result = ServiceUtils.convert2DTOList(kpiUsers, TalentRecruitmentProcessKpiUserVO.class);
//        Map<Long, UserBriefDTO> userMap = userService.findBriefUsers(kpiUsers.stream().map(TalentRecruitmentProcessKpiUser::getUserId).toList())
//            .stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));
//        result.forEach(kpiUser -> kpiUser.setUser(userMap.get(kpiUser.getUserId())));
        return result;
    }


    @Override
    public List<TalentRecruitmentProcessKpiUserVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(kpiUserRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    @Override
    public List<TalentRecruitmentProcessKpiUserVO> findAllPlainByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        List<TalentRecruitmentProcessKpiUser> kpiUsers = kpiUserRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        return ServiceUtils.convert2DTOList(kpiUsers, TalentRecruitmentProcessKpiUserVO.class);
    }

    @Override
    public List<TalentRecruitmentProcessKpiUserVO> findAllByTalentRecruitmentProcessIdExcludingOwners(Long talentRecruitmentProcessId) {
        return toVO(kpiUserRepository.findAllByTalentRecruitmentProcessIdAndUserRoleNotIn(talentRecruitmentProcessId, Collections.singletonList(UserRole.OWNER)));
    }

    @Override
    public List<TalentRecruitmentProcessKpiUserVO> findAllByJobId(Long jobId) {
        return toVO(kpiUserRepository.findAllByJobId(jobId, UserRole.OWNER.toDbValue()));
    }

    @Override
    public List<TalentRecruitmentProcessKpiUserVO> findAllByTalentIdAndTalentRecruitmentProcessIdNotIn(Long talentId, Long talentRecruitmentProcessId){
        return toVO(kpiUserRepository.findAllByTalentIdAndTalentRecruitmentProcessIdNotIn(talentId, talentRecruitmentProcessId));
    }

    @Override
    public void recalculateOwnerCommission(Long resignedUserId) {
        List<Object[]> kpiOwners = kpiUserRepository.getKpiOwnersByUserId(resignedUserId);
        Set<Long> kpiIds = new HashSet<>();
        Set<Long> talentRecruitmentProcessIds = new HashSet<>();
        kpiOwners.forEach(kpi -> {
            kpiIds.add(Long.parseLong(kpi[0].toString()));
            talentRecruitmentProcessIds.add(Long.parseLong(kpi[1].toString()));
        });
        // 将离职的owner的commission设置为0
        kpiUserRepository.updateCommissionByKpiId(kpiIds);
        //剩余的owner评分10% commission
        kpiUserRepository.resetCommission(talentRecruitmentProcessIds, resignedUserId);
        kpiUserRepository.flush();
        kpiUserRepository.resetCommissionForSumPercentageEqualsTo10(talentRecruitmentProcessIds, resignedUserId);
    }
}
