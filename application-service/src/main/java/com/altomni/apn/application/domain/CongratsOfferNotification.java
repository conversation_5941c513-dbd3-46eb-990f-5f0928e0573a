package com.altomni.apn.application.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "congrats_offer_notification")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CongratsOfferNotification implements Serializable {

    @Serial
    private static final long serialVersionUID = 4442477104358727842L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "team_name")
    private String teamName;

    @Column(name = "notification_url")
    private String notificationUrl;

    @Column(name = "notification_token")
    private String notificationToken;

    @Column(name = "web_url")
    private String webUrl;
}
