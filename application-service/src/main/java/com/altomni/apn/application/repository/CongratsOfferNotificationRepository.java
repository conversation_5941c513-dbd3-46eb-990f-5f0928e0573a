package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.CongratsOfferNotification;
import com.altomni.apn.common.domain.job.JobSnapshot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface CongratsOfferNotificationRepository extends JpaRepository<CongratsOfferNotification, Long> {

    Optional<CongratsOfferNotification> findFirstByTenantId(Long tenantId);
}
